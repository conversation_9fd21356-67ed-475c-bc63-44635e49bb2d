# 多目标追踪系统 - UI设计指南

## 🎨 设计概览

本文档详细说明了如何将多目标追踪系统的UI设计转换为实际的前端代码实现。该界面专注于多路视频流的并行监控、跨摄像头的目标追踪以及单个目标的详细信息展示。

### 设计特点
- **多路视频网格**：支持同时展示多路视频流，布局灵活可配置。
- **跨摄像头追踪**：在不同视频流中高亮显示同一个追踪目标。
- **目标信息面板**：点击目标可查看其详细信息，包括ID、类别、速度、轨迹等。
- **全局目标列表**：实时展示当前所有被追踪的目标。
- **交互式时间轴**：展示目标在不同时间点的快照和位置。
- **高效的UI响应**：优化前端性能，确保多路视频播放和数据更新的流畅性。

## 🎯 核心功能模块

### 1. 多路视频展示模块
- **视频网格布局**：支持2x2, 3x3等多种网格布局，可动态调整。
- **视频流独立控制**：每个视频流都可以独立进行播放、暂停、截图等操作。
- **高亮追踪目标**：当选中一个目标时，在所有包含该目标的视频流中进行高亮显示。

### 2. 目标追踪与信息展示模块
- **实时目标列表**：展示当前所有被追踪目标的ID、类别和缩略图。
- **目标详情面板**：点击列表或视频中的目标，右侧弹出详细信息面板。
- **详细信息**：
    - **基本信息**：追踪ID、目标类别、置信度。
    - **动态信息**：当前速度、行驶方向、所在摄像头。
    - **历史轨迹**：在地图或时间轴上展示目标的移动轨迹。
    - **关联事件**：显示与该目标相关的违章、异常行为等事件。

### 3. 全局控制与筛选模块
- **全局播放控制**：一键同步播放/暂停所有视频流。
- **目标筛选**：根据目标类别（如“汽车”、“行人”）进行筛选显示。
- **日期时间选择**：用于回溯查看特定时间段的追踪录像。

## 🏗️ 组件架构设计

### 1. 主容器组件 (`MultiTargetTracking.vue`)

```vue
<template>
  <div class="multi-target-tracking">
    <!-- Header -->
    <TrackingHeader 
      :system-status="systemStatus"
      :active-targets="activeTargetsCount"
      @layout-change="handleLayoutChange"
      @filter-change="handleFilterChange"
    />
    
    <!-- Main Content -->
    <div class="tracking-content">
      <!-- Video Grid -->
      <VideoGrid 
        :streams="videoStreams"
        :layout="gridLyout"
        :tracked-target-id="selectedTargetId"
        @target-select="handleTargetSelect"
      />
      
      <!-- Target Information Panel -->
      <TargetInfoPanel 
        v-if="selectedTarget"
        :target-data="selectedTarget"
        @close="clearSelectedTarget"
      />
    </div>
    
    <!-- Target List -->
    <TargetList 
      :targets="allTargets"
      :selected-id="selectedTargetId"
      @target-select="handleTargetSelect"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useTrackingWebSocket } from '@/composables/useTrackingWebSocket'

// 组件引入
import TrackingHeader from './components/TrackingHeader.vue'
import VideoGrid from './components/VideoGrid.vue'
import TargetInfoPanel from './components/TargetInfoPanel.vue'
import TargetList from './components/TargetList.vue'

// 响应式数据
const systemStatus = ref('正常运行')
const gridLayout = ref('2x2')
const videoStreams = ref([]) // { id, url, name }
const allTargets = ref([]) // { id, type, thumbnail }
const selectedTargetId = ref(null)

const selectedTarget = ref(null)

watch(selectedTargetId, async (newId) => {
  if (newId) {
    // 调用API获取目标详细信息
    const response = await fetch(`/api/tracking/target/${newId}`);
    const data = await response.json();
    if (data.success) {
      selectedTarget.value = data.data;
    }
  } else {
    selectedTarget.value = null;
  }
});

const activeTargetsCount = computed(() => allTargets.value.length)

// WebSocket连接
const { connect, disconnect, onMessage } = useTrackingWebSocket()

const fetchData = async () => {
  // 获取多路视频流
  const streamsResponse = await fetch('/api/realtime/multichannel/streams');
  const streamsData = await streamsResponse.json();
  if (streamsData.success) {
    videoStreams.value = streamsData.data;
  }

  // 获取活动目标
  const targetsResponse = await fetch('/api/tracking/active-targets');
  const targetsData = await targetsResponse.json();
  if (targetsData.success) {
    allTargets.value = targetsData.data;
  }
};

onMounted(() => {
  fetchData();
  // 轮询更新数据
  setInterval(fetchData, 5000); // 每5秒更新一次
});

onUnmounted(() => {
  disconnect()
})

// 方法
const handleTargetSelect = (targetId) => {
  selectedTargetId.value = targetId
  // 触发API调用获取目标详细信息
}

const clearSelectedTarget = () => {
  selectedTargetId.value = null
}

const handleLayoutChange = (layout) => {
  gridLayout.value = layout
}

const handleFilterChange = async (filter) => {
  // 根据filter筛选目标
  const params = new URLSearchParams();
  if (filter.type) {
    params.append('type', filter.type);
  }
  if (filter.min_confidence) {
    params.append('min_confidence', filter.min_confidence);
  }
  const response = await fetch(`/api/tracking/active-targets?${params.toString()}`);
  const data = await response.json();
  if (data.success) {
    allTargets.value = data.data;
  }
};
</script>

<style scoped>
.multi-target-tracking {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.tracking-content {
  flex: 1;
  display: flex;
}
</style>
```