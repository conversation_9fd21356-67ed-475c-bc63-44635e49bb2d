# -*- coding: utf-8 -*-
# <AUTHOR> pan
# @Description : 动态流量图表系统，支持实时和累计流量分析
# @Date : 2023年7月27日10:28:50

import time
import numpy as np
from PySide6.QtCore import QThread, Signal
import matplotlib.pyplot as plt
# mplcyberpunk不可去掉！
import mplcyberpunk
import matplotlib
matplotlib.use('TkAgg')

# 全局变量用于存储图表数据
realtime_data = []  # 实时流量数据
total_data = []     # 累计流量数据
time_labels = []    # 时间标签

class WorkerThread(QThread):
    """
    流量图表线程类，负责创建和更新流量图表
    支持实时流量和累计流量两种模式
    """
    data_updated = Signal(dict)  # 数据更新信号

    def __init__(self):
        super().__init__()
        self.is_stopped = True
        self.is_continue = True
        self.is_close = True
        self.is_exec = True
        self.chart_mode = "realtime"  # 默认为实时流量图，可选值: "realtime", "accumulated"
        
        # 图表配置
        self.plot_styles = {
            "realtime": {
                "title": "实时流量分析",
                "color": "cyan",
                "style": "-",
                "alpha": 0.5,
                "marker": "o",
                "linewidth": 2
            },
            "accumulated": {
                "title": "累计流量分析",
                "color": "magenta",
                "style": "-",
                "alpha": 0.7,
                "marker": "^",
                "linewidth": 2
            }
        }
        
        # 最大数据点数量，防止图表过于拥挤
        self.max_data_points = 50

    def run(self):
        """线程执行函数，创建和更新图表"""
        self.is_stopped = False
        self.is_continue = False
        self.is_close = False
        self.is_exec = False

        # 设置图表样式
        plt.style.use("cyberpunk")
        plt.rcParams['font.sans-serif'] = ['SimHei']  # 支持中文
        plt.rcParams['toolbar'] = 'None'  # 隐藏默认工具栏
        
        # 创建图形窗口
        fig = plt.figure("MTAS系统流量分析", figsize=(10, 6))
        fig.canvas.manager.set_window_title("多源多感知检测平台 - 流量分析")
        
        # 创建两个子图：实时流量和累计流量
        self.ax1 = plt.subplot(211)  # 上面的子图用于实时流量
        self.ax2 = plt.subplot(212)  # 下面的子图用于累计流量
        
        # 注册窗口关闭事件
        fig.canvas.mpl_connect("close_event", self.on_close)
        
        # 主循环
        while True:
            # 检查终止信号
            if self.is_stopped:
                plt.show()
                break

            # 检查暂停信号
            if self.is_continue:
                time.sleep(1)
                continue

            # 检查窗口关闭信号
            if self.is_close:
                return
            
            try:
                # 从YOLO获取数据
                from classes.yolo import y_axis_count_graph as y
                # 获取实时和累计数据
                realtime_data = y if len(y) > 0 else [0]
                if len(realtime_data) > self.max_data_points:
                    realtime_data = realtime_data[-self.max_data_points:]
                
                # 计算累计数据
                total_data = np.cumsum(realtime_data) if len(realtime_data) > 0 else [0]
                
                # 生成时间标签
                time_labels = [f"{i}" for i in range(len(realtime_data))]
                
                # 清空当前坐标轴
                self.ax1.cla()
                self.ax2.cla()
                
                # 绘制实时流量图
                self.ax1.set_title("实时流量分析", color="cyan", fontsize=14)
                self.ax1.set_ylabel("车流量/辆", color="white")
                self.ax1.plot(
                    realtime_data, 
                    linestyle=self.plot_styles["realtime"]["style"], 
                    marker=self.plot_styles["realtime"]["marker"],
                    color=self.plot_styles["realtime"]["color"],
                    linewidth=self.plot_styles["realtime"]["linewidth"]
                )
                mplcyberpunk.add_gradient_fill(
                    alpha_gradientglow=self.plot_styles["realtime"]["alpha"], 
                    gradient_start='zero',
                    ax=self.ax1
                )
                
                # 绘制累计流量图
                self.ax2.set_title("累计流量分析", color="magenta", fontsize=14)
                self.ax2.set_xlabel("时间/帧", color="white")
                self.ax2.set_ylabel("累计车流量/辆", color="white")
                self.ax2.plot(
                    total_data, 
                    linestyle=self.plot_styles["accumulated"]["style"], 
                    marker=self.plot_styles["accumulated"]["marker"],
                    color=self.plot_styles["accumulated"]["color"],
                    linewidth=self.plot_styles["accumulated"]["linewidth"]
                )
                mplcyberpunk.add_gradient_fill(
                    alpha_gradientglow=self.plot_styles["accumulated"]["alpha"], 
                    gradient_start='zero',
                    ax=self.ax2
                )
                
                # 添加网格线
                self.ax1.grid(True, alpha=0.3, linestyle='--')
                self.ax2.grid(True, alpha=0.3, linestyle='--')
                
                # 调整布局
                plt.tight_layout()
                
                # 更新图表
                plt.pause(1)
                
                # 发送数据更新信号
                self.data_updated.emit({
                    "realtime": realtime_data[-1] if len(realtime_data) > 0 else 0,
                    "accumulated": total_data[-1] if len(total_data) > 0 else 0
                })
                
            except Exception as e:
                print(f"图表更新错误: {e}")
                plt.pause(1)

    def set_chart_mode(self, mode):
        """设置图表模式: 'realtime' 或 'accumulated'"""
        if mode in ["realtime", "accumulated"]:
            self.chart_mode = mode
            return True
        return False

    def on_close(self, event):
        """窗口关闭事件处理"""
        self.is_close = True

    def stop(self):
        """停止线程"""
        self.is_stopped = True

    def pause(self):
        """暂停绘图"""
        self.is_continue = True

    def run_continue(self):
        """继续绘图"""
        self.is_continue = False

    def close_exec(self):
        """关闭图表窗口"""
        try:
            self.stop()
            plt.close()
        except Exception as e:
            print(f"关闭图表窗口错误: {e}")
            pass


