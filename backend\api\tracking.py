# @Description : 追踪算法管理API接口
# @Date : 2025年6月20日

import json
from datetime import datetime, timedelta
from flask import request, jsonify
from . import api_v1
from .auth import token_required
from utils.response import success_response, error_response
from utils.database import get_db_connection
from services.tracking_service import TrackingService

# 初始化追踪服务
tracking_service = TrackingService()

@api_v1.route('/tracking/algorithms', methods=['GET'])
@token_required
def get_tracking_algorithms(current_user_id):
    """获取支持的追踪算法列表"""
    try:
        algorithms = tracking_service.get_available_algorithms()
        return success_response(algorithms, '获取追踪算法列表成功')
        
    except Exception as e:
        return error_response(f'获取追踪算法列表失败: {str(e)}')

@api_v1.route('/tracking/switch-algorithm', methods=['POST'])
@token_required
def switch_tracking_algorithm(current_user_id):
    """切换追踪算法"""
    try:
        data = request.get_json()
        monitor_id = data.get('monitor_id')
        algorithm_name = data.get('algorithm_name')
        config_params = data.get('config_params', {})
        
        if not monitor_id or not algorithm_name:
            return error_response('监控点ID和算法名称不能为空')
        
        # 验证算法是否支持
        available_algorithms = tracking_service.get_available_algorithms()
        if algorithm_name not in [alg['name'] for alg in available_algorithms]:
            return error_response('不支持的追踪算法')
        
        # 保存配置到数据库
        config_data = {
            'algorithm_name': algorithm_name,
            'params': config_params,
            'switch_time': datetime.now().isoformat()
        }
        
        with get_db_connection() as db:
            # 检查是否已存在配置
            existing = db.get_one(
                "SELECT id FROM algorithm_configs WHERE monitor_id=%s AND config_type='tracking'",
                (monitor_id,)
            )
            
            if existing:
                # 更新配置
                db.execute(
                    "UPDATE algorithm_configs SET algorithm_name=%s, config_data=%s, update_time=NOW() WHERE id=%s",
                    (algorithm_name, json.dumps(config_data), existing['id'])
                )
            else:
                # 插入新配置
                db.execute(
                    "INSERT INTO algorithm_configs (monitor_id, config_type, algorithm_name, config_data, create_by) VALUES (%s, %s, %s, %s, %s)",
                    (monitor_id, 'tracking', algorithm_name, json.dumps(config_data), current_user_id)
                )
        
        # 切换算法
        result = tracking_service.switch_algorithm(monitor_id, algorithm_name, config_params)
        
        if result['success']:
            return success_response(result['data'], '追踪算法切换成功')
        else:
            return error_response(result['message'])
            
    except Exception as e:
        return error_response(f'切换追踪算法失败: {str(e)}')

@api_v1.route('/tracking/performance', methods=['GET'])
@token_required
def get_tracking_performance(current_user_id):
    """获取追踪算法性能指标"""
    try:
        monitor_id = request.args.get('monitor_id')
        algorithm_name = request.args.get('algorithm_name')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        # 构建查询条件
        conditions = []
        params = []
        
        if monitor_id:
            conditions.append('monitor_id = %s')
            params.append(monitor_id)
            
        if algorithm_name:
            conditions.append('algorithm_name = %s')
            params.append(algorithm_name)
            
        if start_date:
            conditions.append('stat_date >= %s')
            params.append(start_date)
            
        if end_date:
            conditions.append('stat_date <= %s')
            params.append(end_date)
        
        where_clause = ' AND '.join(conditions) if conditions else '1=1'
        
        with get_db_connection() as db:
            # 获取性能统计
            performance_query = f"""
                SELECT 
                    tp.*,
                    m.name as monitor_name
                FROM tracking_performance tp
                LEFT JOIN monitor m ON tp.monitor_id = m.id
                WHERE {where_clause}
                ORDER BY tp.stat_date DESC, tp.stat_hour DESC
                LIMIT 100
            """
            
            performance_data = db.get_all(performance_query, params)
            
            # 获取算法对比数据
            comparison_query = f"""
                SELECT 
                    algorithm_name,
                    AVG(accuracy) as avg_accuracy,
                    AVG(fps) as avg_fps,
                    AVG(cpu_usage) as avg_cpu_usage,
                    AVG(memory_usage) as avg_memory_usage,
                    AVG(avg_track_length) as avg_track_length
                FROM tracking_performance
                WHERE {where_clause}
                GROUP BY algorithm_name
                ORDER BY avg_accuracy DESC
            """
            
            comparison_data = db.get_all(comparison_query, params)
        
        return success_response({
            'performance_data': performance_data,
            'comparison_data': comparison_data
        }, '获取追踪性能数据成功')
        
    except Exception as e:
        return error_response(f'获取追踪性能数据失败: {str(e)}')

@api_v1.route('/tracking/start', methods=['POST'])
@token_required
def start_tracking(current_user_id):
    """启动追踪"""
    try:
        data = request.get_json()
        monitor_id = data.get('monitor_id')
        algorithm_name = data.get('algorithm_name', 'bytetrack')
        
        if not monitor_id:
            return error_response('监控点ID不能为空')
        
        # 启动追踪
        result = tracking_service.start_tracking(monitor_id, algorithm_name)
        
        if result['success']:
            return success_response(result['data'], '追踪启动成功')
        else:
            return error_response(result['message'])
            
    except Exception as e:
        return error_response(f'启动追踪失败: {str(e)}')

@api_v1.route('/tracking/stop', methods=['POST'])
@token_required
def stop_tracking(current_user_id):
    """停止追踪"""

@api_v1.route('/tracking/active-targets', methods=['GET'])
@token_required
def get_active_targets(current_user_id):
    """获取所有活动监控点的追踪目标"""
    try:
        # 添加筛选参数
        target_type = request.args.get('type')
        min_confidence = request.args.get('min_confidence', type=float)

        active_targets = tracking_service.get_all_active_targets(target_type=target_type, min_confidence=min_confidence)
        return success_response(active_targets, '获取活动目标成功')
    except Exception as e:
        return error_response(f'获取活动目标失败: {str(e)}')

@api_v1.route('/tracking/target/<int:target_id>', methods=['GET'])
@token_required
def get_target_details(current_user_id, target_id):
    """获取特定追踪目标的详细信息"""
    try:
        target_details = tracking_service.get_target_details(target_id)
        if target_details:
            return success_response(target_details, '获取目标详情成功')
        else:
            return error_response('未找到指定ID的目标', status_code=404)
    except Exception as e:
        return error_response(f'获取目标详情失败: {str(e)}')


    try:
        data = request.get_json()
        monitor_id = data.get('monitor_id')
        
        if not monitor_id:
            return error_response('监控点ID不能为空')
        
        # 停止追踪
        result = tracking_service.stop_tracking(monitor_id)
        
        if result['success']:
            return success_response(result['data'], '追踪停止成功')
        else:
            return error_response(result['message'])
            
    except Exception as e:
        return error_response(f'停止追踪失败: {str(e)}')

@api_v1.route('/tracking/status', methods=['GET'])
@token_required
def get_tracking_status(current_user_id):
    """获取追踪状态"""
    try:
        monitor_id = request.args.get('monitor_id')
        
        if monitor_id:
            # 获取指定监控点的追踪状态
            status = tracking_service.get_tracking_status(monitor_id)
        else:
            # 获取所有监控点的追踪状态
            status = tracking_service.get_all_tracking_status()
        
        return success_response(status, '获取追踪状态成功')
        
    except Exception as e:
        return error_response(f'获取追踪状态失败: {str(e)}')