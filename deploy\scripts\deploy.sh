#!/bin/bash

# 基于Yolov8与ByteTrack的高速公路智慧监控平台 - 部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 命令未找到，请先安装"
        exit 1
    fi
}

# 检查Docker和Docker Compose
check_dependencies() {
    log_info "检查依赖..."
    check_command docker
    check_command docker-compose
    log_success "依赖检查完成"
}

# 创建环境配置文件
create_env_file() {
    log_info "创建环境配置文件..."
    
    if [ ! -f .env ]; then
        cat > .env << EOF
# 数据库配置
MYSQL_ROOT_PASSWORD=root_password_123
MYSQL_DATABASE=yolo
MYSQL_USER=yolo_user
MYSQL_PASSWORD=yolo_password_123
MYSQL_PORT=3306

# Redis配置
REDIS_PASSWORD=redis_password_123
REDIS_PORT=6379

# 应用配置
BACKEND_PORT=5500
FRONTEND_PORT=3000
HTTP_PORT=80
HTTPS_PORT=443

# 监控配置
PROMETHEUS_PORT=9090
GRAFANA_PORT=3001
GRAFANA_PASSWORD=admin123

# 安全配置
SECRET_KEY=yolo-highway-monitoring-system-secret-key-2025
JWT_SECRET=jwt-secret-key-2025

# 环境设置
FLASK_ENV=production
DEBUG=false
EOF
        log_success "环境配置文件已创建: .env"
    else
        log_warning "环境配置文件已存在，跳过创建"
    fi
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    directories=(
        "logs"
        "uploads"
        "static/images"
        "static/after_img"
        "static/before_img"
        "models"
        "backups"
        "exports"
        "deploy/mysql/conf.d"
        "deploy/ssl"
        "deploy/monitoring"
    )
    
    for dir in "${directories[@]}"; do
        mkdir -p "$dir"
        log_info "创建目录: $dir"
    done
    
    log_success "目录创建完成"
}

# 下载模型文件
download_models() {
    log_info "检查模型文件..."
    
    if [ ! -f "models/yolov8n.pt" ]; then
        log_info "下载YOLOv8n模型..."
        wget -O models/yolov8n.pt https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8n.pt
        log_success "YOLOv8n模型下载完成"
    fi
    
    if [ ! -f "models/car.pt" ]; then
        log_warning "未找到自定义车辆检测模型 car.pt"
        log_info "请将自定义模型文件放置在 models/car.pt"
    fi
}

# 构建Docker镜像
build_images() {
    log_info "构建Docker镜像..."
    
    # 构建后端镜像
    log_info "构建后端镜像..."
    docker-compose -f deploy/docker-compose.yml build backend
    
    log_success "Docker镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 启动基础服务（数据库、Redis）
    log_info "启动基础服务..."
    docker-compose -f deploy/docker-compose.yml up -d mysql redis
    
    # 等待数据库启动
    log_info "等待数据库启动..."
    sleep 30
    
    # 启动应用服务
    log_info "启动应用服务..."
    docker-compose -f deploy/docker-compose.yml up -d backend nginx
    
    log_success "服务启动完成"
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    # 等待服务启动
    sleep 10
    
    # 检查容器状态
    docker-compose -f deploy/docker-compose.yml ps
    
    # 检查健康状态
    log_info "检查应用健康状态..."
    for i in {1..30}; do
        if curl -f http://localhost:${BACKEND_PORT:-5500}/health &> /dev/null; then
            log_success "应用健康检查通过"
            break
        else
            log_info "等待应用启动... ($i/30)"
            sleep 2
        fi
    done
}

# 显示访问信息
show_access_info() {
    log_success "部署完成！"
    echo ""
    echo "=========================================="
    echo "访问信息:"
    echo "=========================================="
    echo "应用地址: http://localhost:${HTTP_PORT:-80}"
    echo "API文档: http://localhost:${HTTP_PORT:-80}/api/v1/docs"
    echo "健康检查: http://localhost:${HTTP_PORT:-80}/health"
    echo ""
    echo "管理界面:"
    echo "Grafana: http://localhost:${GRAFANA_PORT:-3001} (admin/admin123)"
    echo "Prometheus: http://localhost:${PROMETHEUS_PORT:-9090}"
    echo ""
    echo "数据库连接:"
    echo "Host: localhost:${MYSQL_PORT:-3306}"
    echo "Database: ${MYSQL_DATABASE:-yolo}"
    echo "Username: ${MYSQL_USER:-yolo_user}"
    echo ""
    echo "=========================================="
}

# 清理函数
cleanup() {
    log_info "停止并清理服务..."
    docker-compose -f deploy/docker-compose.yml down
    log_success "清理完成"
}

# 更新函数
update() {
    log_info "更新应用..."
    
    # 拉取最新代码
    git pull
    
    # 重新构建镜像
    build_images
    
    # 重启服务
    docker-compose -f deploy/docker-compose.yml up -d --force-recreate backend
    
    log_success "更新完成"
}

# 备份函数
backup() {
    log_info "创建备份..."
    
    backup_dir="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # 备份数据库
    docker-compose -f deploy/docker-compose.yml exec -T mysql mysqldump \
        -u${MYSQL_USER:-yolo_user} -p${MYSQL_PASSWORD:-yolo_password_123} \
        ${MYSQL_DATABASE:-yolo} > "$backup_dir/database.sql"
    
    # 备份配置文件
    cp -r config "$backup_dir/"
    cp .env "$backup_dir/"
    
    # 备份上传文件
    cp -r uploads "$backup_dir/"
    
    log_success "备份完成: $backup_dir"
}

# 主函数
main() {
    case "$1" in
        "install")
            log_info "开始安装基于Yolov8与ByteTrack的高速公路智慧监控平台..."
            check_dependencies
            create_env_file
            create_directories
            download_models
            build_images
            start_services
            check_services
            show_access_info
            ;;
        "start")
            log_info "启动服务..."
            docker-compose -f deploy/docker-compose.yml up -d
            check_services
            ;;
        "stop")
            log_info "停止服务..."
            docker-compose -f deploy/docker-compose.yml down
            ;;
        "restart")
            log_info "重启服务..."
            docker-compose -f deploy/docker-compose.yml restart
            ;;
        "update")
            update
            ;;
        "backup")
            backup
            ;;
        "cleanup")
            cleanup
            ;;
        "logs")
            docker-compose -f deploy/docker-compose.yml logs -f ${2:-backend}
            ;;
        "status")
            docker-compose -f deploy/docker-compose.yml ps
            ;;
        *)
            echo "用法: $0 {install|start|stop|restart|update|backup|cleanup|logs|status}"
            echo ""
            echo "命令说明:"
            echo "  install  - 完整安装系统"
            echo "  start    - 启动服务"
            echo "  stop     - 停止服务"
            echo "  restart  - 重启服务"
            echo "  update   - 更新应用"
            echo "  backup   - 创建备份"
            echo "  cleanup  - 清理服务"
            echo "  logs     - 查看日志"
            echo "  status   - 查看状态"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
