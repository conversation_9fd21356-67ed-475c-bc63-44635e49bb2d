#!/usr/bin/env python
# -*- coding: utf-8 -*-

import gc
import torch
import psutil
import os
import subprocess
import time

class MemoryOptimizer:
    def __init__(self):
        self.initial_memory = psutil.virtual_memory()
        
    def clear_gpu_cache(self):
        """清理GPU缓存"""
        print("清理GPU缓存...")
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            torch.cuda.synchronize()
            print("✓ GPU缓存已清理")
        else:
            print("⚠ CUDA不可用，跳过GPU缓存清理")
    
    def clear_python_cache(self):
        """清理Python内存缓存"""
        print("清理Python内存缓存...")
        gc.collect()
        print("✓ Python垃圾回收完成")
    
    def check_memory_usage(self):
        """检查内存使用情况"""
        memory = psutil.virtual_memory()
        print(f"内存使用情况:")
        print(f"  总内存: {memory.total / 1024**3:.1f} GB")
        print(f"  已使用: {memory.used / 1024**3:.1f} GB ({memory.percent:.1f}%)")
        print(f"  可用内存: {memory.available / 1024**3:.1f} GB")
        
        if memory.percent > 85:
            print("⚠ 内存使用率过高，可能影响性能")
            return False
        elif memory.percent > 75:
            print("⚠ 内存使用率较高")
            return True
        else:
            print("✓ 内存使用率正常")
            return True
    
    def find_memory_hogs(self):
        """找出占用内存最多的进程"""
        print("\n占用内存最多的进程:")
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'memory_info']):
            try:
                processes.append((
                    proc.info['pid'],
                    proc.info['name'],
                    proc.info['memory_info'].rss / 1024**2  # MB
                ))
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        # 按内存使用量排序
        processes.sort(key=lambda x: x[2], reverse=True)
        
        print("前10个占用内存的进程:")
        for i, (pid, name, memory_mb) in enumerate(processes[:10]):
            print(f"  {i+1}. PID: {pid}, 进程: {name}, 内存: {memory_mb:.1f} MB")
    
    def optimize_system_memory(self):
        """优化系统内存"""
        print("\n开始系统内存优化...")
        
        try:
            # 清理系统缓存 (Windows)
            print("清理系统缓存...")
            subprocess.run(['sfc', '/scannow'], capture_output=True, check=False)
            print("✓ 系统缓存清理完成")
        except Exception as e:
            print(f"⚠ 系统缓存清理失败: {e}")
    
    def kill_unnecessary_processes(self):
        """建议关闭不必要的进程"""
        print("\n检查可能影响性能的进程...")
        
        # 常见的可以关闭的进程
        unnecessary_processes = [
            'chrome.exe', 'firefox.exe', 'edge.exe',  # 浏览器
            'discord.exe', 'spotify.exe', 'steam.exe',  # 娱乐软件
            'photoshop.exe', 'illustrator.exe',  # Adobe软件
            'code.exe', 'devenv.exe',  # 开发工具
        ]
        
        found_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'memory_info']):
            try:
                if proc.info['name'].lower() in [p.lower() for p in unnecessary_processes]:
                    memory_mb = proc.info['memory_info'].rss / 1024**2
                    if memory_mb > 100:  # 只显示占用超过100MB的进程
                        found_processes.append((
                            proc.info['pid'],
                            proc.info['name'],
                            memory_mb
                        ))
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        if found_processes:
            print("发现以下可能不必要的进程:")
            for pid, name, memory_mb in found_processes:
                print(f"  PID: {pid}, 进程: {name}, 内存: {memory_mb:.1f} MB")
            print("\n建议关闭这些进程以释放内存")
        else:
            print("✓ 没有发现明显不必要的进程")
    
    def optimize_yolo_config(self):
        """优化YOLO配置以减少内存使用"""
        print("\n优化YOLO配置...")
        
        config_files = ["config/config.json", "main_config.json"]
        
        for config_file in config_files:
            if os.path.exists(config_file):
                try:
                    import json
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    # 优化配置
                    optimized = False
                    
                    # 降低置信度阈值可以减少后处理时间
                    if config.get('conf', 0.5) < 0.6:
                        config['conf'] = 0.6
                        optimized = True
                        print(f"  提高置信度阈值到0.6 ({config_file})")
                    
                    # 统一rate参数
                    if 'rate' in config and config['rate'] != 16:
                        config['rate'] = 16
                        optimized = True
                        print(f"  统一rate参数为16 ({config_file})")
                    
                    if optimized:
                        with open(config_file, 'w', encoding='utf-8') as f:
                            json.dump(config, f, indent=2, ensure_ascii=False)
                        print(f"✓ 已优化配置文件: {config_file}")
                    else:
                        print(f"✓ 配置文件已是最优: {config_file}")
                        
                except Exception as e:
                    print(f"✗ 优化配置文件失败: {e}")
    
    def run_full_optimization(self):
        """运行完整的内存优化"""
        print("ByteTrack内存优化工具")
        print("=" * 60)
        
        print("优化前状态:")
        self.check_memory_usage()
        
        # 执行优化步骤
        self.clear_python_cache()
        self.clear_gpu_cache()
        self.optimize_yolo_config()
        
        print("\n" + "=" * 60)
        print("优化后状态:")
        self.check_memory_usage()
        
        # 显示进程信息
        self.find_memory_hogs()
        self.kill_unnecessary_processes()
        
        print("\n" + "=" * 60)
        print("优化建议:")
        print("1. 关闭不必要的浏览器标签页和应用程序")
        print("2. 重启系统以完全清理内存")
        print("3. 考虑增加虚拟内存大小")
        print("4. 如果可能，增加物理内存")
        print("5. 在任务管理器中结束占用内存大的进程")

def main():
    optimizer = MemoryOptimizer()
    optimizer.run_full_optimization()

if __name__ == "__main__":
    main()
