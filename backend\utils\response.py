# -*- coding: utf-8 -*-
# @Description : 统一响应格式工具
# @Date : 2025年6月20日

import time
from flask import jsonify
from typing import Any, Optional

def success_response(data: Any = None, message: str = "操作成功", code: int = 200) -> dict:
    """
    成功响应格式
    
    Args:
        data: 响应数据
        message: 响应消息
        code: 状态码
    
    Returns:
        dict: 格式化的响应数据
    """
    return jsonify({
        'code': code,
        'message': message,
        'data': data,
        'success': True,
        'timestamp': int(time.time() * 1000)
    })

def error_response(message: str = "操作失败", code: int = 400, data: Any = None) -> dict:
    """
    错误响应格式
    
    Args:
        message: 错误消息
        code: 错误码
        data: 额外数据
    
    Returns:
        dict: 格式化的错误响应
    """
    return jsonify({
        'code': code,
        'message': message,
        'data': data,
        'success': False,
        'timestamp': int(time.time() * 1000)
    })

def paginated_response(data: list, total: int, page: int, page_size: int, message: str = "获取数据成功") -> dict:
    """
    分页响应格式
    
    Args:
        data: 数据列表
        total: 总数量
        page: 当前页码
        page_size: 每页大小
        message: 响应消息
    
    Returns:
        dict: 格式化的分页响应
    """
    return success_response({
        'list': data,
        'pagination': {
            'total': total,
            'page': page,
            'page_size': page_size,
            'total_pages': (total + page_size - 1) // page_size,
            'has_next': page * page_size < total,
            'has_prev': page > 1
        }
    }, message)

def validation_error_response(errors: dict) -> dict:
    """
    验证错误响应格式
    
    Args:
        errors: 验证错误字典
    
    Returns:
        dict: 格式化的验证错误响应
    """
    return error_response(
        message="数据验证失败",
        code=422,
        data={'validation_errors': errors}
    )

def unauthorized_response(message: str = "未授权访问") -> dict:
    """
    未授权响应格式
    
    Args:
        message: 错误消息
    
    Returns:
        dict: 格式化的未授权响应
    """
    return error_response(message, 401)

def forbidden_response(message: str = "权限不足") -> dict:
    """
    禁止访问响应格式
    
    Args:
        message: 错误消息
    
    Returns:
        dict: 格式化的禁止访问响应
    """
    return error_response(message, 403)

def not_found_response(message: str = "资源不存在") -> dict:
    """
    资源不存在响应格式
    
    Args:
        message: 错误消息
    
    Returns:
        dict: 格式化的资源不存在响应
    """
    return error_response(message, 404)

def server_error_response(message: str = "服务器内部错误") -> dict:
    """
    服务器错误响应格式
    
    Args:
        message: 错误消息
    
    Returns:
        dict: 格式化的服务器错误响应
    """
    return error_response(message, 500)

import time
