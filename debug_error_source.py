#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试错误来源 - 找出 'NoneType' object has no attribute 'execute' 的真正来源
"""

import requests
import json
import traceback

def test_login_with_debug():
    """测试登录并获取详细错误信息"""
    print("🔍 调试登录错误来源")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5500"
    login_data = {"username": "admin", "password": "123456"}
    
    try:
        response = requests.post(
            f"{base_url}/api/v1/auth/login", 
            json=login_data, 
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            error_message = data.get('message', '')
            
            if 'SQL执行失败' in error_message:
                print("\n🔍 分析错误信息:")
                print(f"完整错误: {error_message}")
                
                # 分析可能的错误来源
                if "'NoneType' object has no attribute 'execute'" in error_message:
                    print("\n💡 可能的错误来源:")
                    print("1. 登录函数中的数据库连接")
                    print("2. 认证装饰器中的数据库查询")
                    print("3. 其他中间件或钩子函数")
                    print("4. 数据库初始化问题")
                    
                    return analyze_possible_sources()
        
        return False
        
    except Exception as e:
        print(f"请求异常: {e}")
        return False

def analyze_possible_sources():
    """分析可能的错误来源"""
    print("\n🔍 分析可能的错误来源")
    print("=" * 50)
    
    # 检查可能调用数据库的地方
    possible_sources = [
        "登录函数本身",
        "JWT认证装饰器",
        "Flask请求钩子",
        "数据库初始化",
        "其他API函数被意外调用"
    ]
    
    for i, source in enumerate(possible_sources, 1):
        print(f"{i}. {source}")
    
    print("\n💡 建议检查步骤:")
    print("1. 检查backend/api/auth.py中是否还有其他数据库调用")
    print("2. 检查是否有@require_auth装饰器被意外触发")
    print("3. 检查Flask应用的before_request钩子")
    print("4. 检查数据库初始化代码")
    
    return True

def test_simple_endpoint():
    """测试简单端点，看是否是全局问题"""
    print("\n🔍 测试简单端点")
    print("=" * 30)
    
    base_url = "http://127.0.0.1:5500"
    
    # 测试根路径
    try:
        response = requests.get(base_url, timeout=5)
        print(f"根路径状态码: {response.status_code}")
        if response.status_code == 200:
            print("✅ 根路径正常")
        else:
            print("❌ 根路径异常")
    except Exception as e:
        print(f"根路径异常: {e}")
    
    # 测试健康检查
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        print(f"健康检查状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            db_status = data.get('database', {}).get('status')
            print(f"数据库状态: {db_status}")
            if db_status == 'unhealthy':
                error = data.get('database', {}).get('error', '')
                print(f"数据库错误: {error}")
        else:
            print("❌ 健康检查异常")
    except Exception as e:
        print(f"健康检查异常: {e}")

def create_minimal_login_test():
    """创建最小化登录测试"""
    print("\n🔧 创建最小化登录测试")
    print("=" * 40)
    
    minimal_login_code = '''
# 最小化登录测试 - 完全独立的实现
import pymysql
from flask import Flask, request, jsonify

app = Flask(__name__)

@app.route('/test-login', methods=['POST'])
def test_login():
    """最小化登录测试"""
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        
        # 直接数据库连接
        config = {
            'host': '127.0.0.1',
            'port': 3306,
            'user': 'root',
            'password': '123456',
            'database': 'yolo',
            'charset': 'utf8mb4',
            'autocommit': True,
            'cursorclass': pymysql.cursors.DictCursor
        }
        
        connection = pymysql.connect(**config)
        with connection.cursor() as cursor:
            cursor.execute(
                "SELECT * FROM user WHERE username=%s AND password=%s",
                (username, password)
            )
            user = cursor.fetchone()
        connection.close()
        
        if user:
            return jsonify({
                'success': True,
                'message': '登录成功',
                'user': {
                    'id': user['id'],
                    'username': user['username'],
                    'grade': user['grade']
                }
            })
        else:
            return jsonify({
                'success': False,
                'message': '用户名或密码错误'
            })
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'错误: {str(e)}'
        })

if __name__ == '__main__':
    app.run(host='127.0.0.1', port=5501, debug=True)
'''
    
    # 写入测试文件
    with open('minimal_login_test.py', 'w', encoding='utf-8') as f:
        f.write(minimal_login_code)
    
    print("✅ 最小化登录测试已创建: minimal_login_test.py")
    print("💡 运行方式:")
    print("   python minimal_login_test.py")
    print("   然后测试: curl -X POST http://127.0.0.1:5501/test-login -H 'Content-Type: application/json' -d '{\"username\":\"admin\",\"password\":\"123456\"}'")

def main():
    """主函数"""
    print("=" * 80)
    print("🚀 调试错误来源 - 找出真正的问题")
    print("=" * 80)
    
    # 1. 测试登录并分析错误
    test_login_with_debug()
    
    # 2. 测试简单端点
    test_simple_endpoint()
    
    # 3. 创建最小化测试
    create_minimal_login_test()
    
    print("\n" + "=" * 80)
    print("📋 调试完成")
    print("💡 下一步:")
    print("1. 运行最小化登录测试，确认数据库连接本身是否正常")
    print("2. 如果最小化测试成功，说明问题在原有代码的其他地方")
    print("3. 需要检查Flask应用的钩子函数和中间件")
    print("=" * 80)

if __name__ == "__main__":
    main()
