/* 道路预警对话框样式 - 浅色透明风格 */

/* 主对话框容器 */
.RoadWarningDialog {
    background: transparent;
}

/* 主内容框架 */
.RoadWarningDialog QFrame#main_container {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 rgba(255, 255, 255, 0.95),
        stop:0.3 rgba(248, 250, 252, 0.98),
        stop:0.7 rgba(241, 245, 249, 0.98),
        stop:1 rgba(255, 255, 255, 0.95));
    border-radius: 20px;
    border: 1px solid rgba(203, 213, 225, 0.6);
}

/* 标题区域 */
.RoadWarningDialog QFrame#header {
    background: transparent;
    border: none;
    padding: 5px 0px;
}

/* 警告图标 */
.RoadWarningDialog QLabel#warning_icon {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 rgb(251, 191, 36),
        stop:0.5 rgb(245, 158, 11),
        stop:1 rgb(217, 119, 6));
    border-radius: 16px;
    color: white;
    font-size: 18px;
    font-weight: bold;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

/* 标题文本 */
.RoadWarningDialog QLabel#title {
    color: rgb(51, 65, 85);
    font-family: 'Microsoft YaHei UI', 'PingFang SC', 'Helvetica Neue', sans-serif;
    font-size: 20px;
    font-weight: 600;
    background: transparent;
    border: none;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* 关闭按钮 */
.RoadWarningDialog QPushButton#close_btn {
    background: rgba(148, 163, 184, 0.1);
    border: none;
    border-radius: 14px;
    color: rgb(100, 116, 139);
    font-size: 16px;
    font-weight: bold;
    transition: all 0.2s ease;
}

.RoadWarningDialog QPushButton#close_btn:hover {
    background: rgba(239, 68, 68, 0.1);
    color: rgb(239, 68, 68);
    transform: scale(1.05);
}

.RoadWarningDialog QPushButton#close_btn:pressed {
    background: rgba(239, 68, 68, 0.2);
    transform: scale(0.95);
}

/* 内容区域 */
.RoadWarningDialog QFrame#content {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(255, 255, 255, 0.6),
        stop:1 rgba(248, 250, 252, 0.4));
    border-radius: 12px;
    border: 1px solid rgba(226, 232, 240, 0.8);
    padding: 15px;
    backdrop-filter: blur(10px);
}

/* 预警文本 */
.RoadWarningDialog QLabel#warning_text {
    color: rgb(71, 85, 105);
    font-size: 14px;
    font-weight: 500;
    background: transparent;
    border: none;
    margin-bottom: 8px;
}

/* 预警项目框架 */
.RoadWarningDialog QFrame.warning_item {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(255, 255, 255, 0.8),
        stop:1 rgba(248, 250, 252, 0.6));
    border-radius: 8px;
    padding: 8px 12px;
    margin: 2px 0px;
    border: 1px solid rgba(226, 232, 240, 0.5);
}

.RoadWarningDialog QFrame.warning_item:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(255, 255, 255, 0.9),
        stop:1 rgba(248, 250, 252, 0.7));
    border-color: rgba(203, 213, 225, 0.8);
    transform: translateY(-1px);
}

/* 高危预警项目 */
.RoadWarningDialog QFrame.warning_item.danger {
    border-left: 3px solid rgb(239, 68, 68);
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 rgba(254, 242, 242, 0.8),
        stop:1 rgba(255, 255, 255, 0.6));
}

/* 警告预警项目 */
.RoadWarningDialog QFrame.warning_item.warning {
    border-left: 3px solid rgb(245, 158, 11);
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 rgba(255, 251, 235, 0.8),
        stop:1 rgba(255, 255, 255, 0.6));
}

/* 信息预警项目 */
.RoadWarningDialog QFrame.warning_item.info {
    border-left: 3px solid rgb(59, 130, 246);
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 rgba(239, 246, 255, 0.8),
        stop:1 rgba(255, 255, 255, 0.6));
}

/* 预警项目图标 */
.RoadWarningDialog QLabel.warning_icon {
    font-size: 16px;
    background: transparent;
    border: none;
    padding: 2px;
}

/* 预警项目标题 */
.RoadWarningDialog QLabel.warning_title {
    font-size: 13px;
    font-weight: 600;
    background: transparent;
    border: none;
    margin-bottom: 2px;
}

.RoadWarningDialog QLabel.warning_title.danger {
    color: rgb(239, 68, 68);
}

.RoadWarningDialog QLabel.warning_title.warning {
    color: rgb(245, 158, 11);
}

.RoadWarningDialog QLabel.warning_title.info {
    color: rgb(59, 130, 246);
}

/* 预警项目描述 */
.RoadWarningDialog QLabel.warning_desc {
    color: rgb(100, 116, 139);
    font-size: 11px;
    background: transparent;
    border: none;
    opacity: 0.9;
}

/* 底部按钮区域 */
.RoadWarningDialog QFrame#footer {
    background: transparent;
    border: none;
    padding: 5px 0px;
}

/* 查看详情按钮 */
.RoadWarningDialog QPushButton#detail_btn {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(59, 130, 246, 0.1),
        stop:1 rgba(37, 99, 235, 0.1));
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 8px;
    color: rgb(59, 130, 246);
    font-size: 13px;
    font-weight: 500;
    padding: 8px 20px;
    min-width: 80px;
}

.RoadWarningDialog QPushButton#detail_btn:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(59, 130, 246, 0.15),
        stop:1 rgba(37, 99, 235, 0.15));
    border-color: rgba(59, 130, 246, 0.5);
    transform: translateY(-1px);
}

.RoadWarningDialog QPushButton#detail_btn:pressed {
    background: rgba(59, 130, 246, 0.2);
    transform: translateY(0px);
}

/* 确认按钮 */
.RoadWarningDialog QPushButton#confirm_btn {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgb(59, 130, 246),
        stop:0.5 rgb(37, 99, 235),
        stop:1 rgb(29, 78, 216));
    border: none;
    border-radius: 8px;
    color: white;
    font-size: 13px;
    font-weight: 500;
    padding: 8px 24px;
    min-width: 90px;
    /* Qt不支持box-shadow属性 */
}

.RoadWarningDialog QPushButton#confirm_btn:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgb(37, 99, 235),
        stop:0.5 rgb(29, 78, 216),
        stop:1 rgb(30, 64, 175));
    transform: translateY(-1px);
    /* Qt不支持box-shadow属性 */
}

.RoadWarningDialog QPushButton#confirm_btn:pressed {
    background: rgb(29, 78, 216);
    transform: translateY(0px);
    /* Qt不支持box-shadow属性 */
}

/* 滚动条样式 */
.RoadWarningDialog QScrollArea QScrollBar:vertical {
    background: rgba(241, 245, 249, 0.5);
    width: 6px;
    border-radius: 3px;
    margin: 0px;
}

.RoadWarningDialog QScrollArea QScrollBar::handle:vertical {
    background: rgba(148, 163, 184, 0.5);
    border-radius: 3px;
    min-height: 20px;
}

.RoadWarningDialog QScrollArea QScrollBar::handle:vertical:hover {
    background: rgba(100, 116, 139, 0.7);
}

/* 动画过渡效果 */
.RoadWarningDialog * {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 响应式设计 */
@media (max-width: 600px) {
    .RoadWarningDialog {
        margin: 10px;
    }
    
    .RoadWarningDialog QLabel#title {
        font-size: 18px;
    }
    
    .RoadWarningDialog QPushButton {
        padding: 6px 16px;
        font-size: 12px;
    }
}
