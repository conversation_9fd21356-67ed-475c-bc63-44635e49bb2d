#!/usr/bin/env python
# -*- coding: utf-8 -*-

import psutil
import torch
import time
import threading
import json
import os
import sys
import subprocess
import numpy as np
from datetime import datetime

class PerformanceDiagnostic:
    def __init__(self):
        self.monitoring = False
        self.gpu_stats = []
        self.cpu_stats = []
        self.memory_stats = []
        
    def check_gpu_processes(self):
        """检查占用GPU的进程"""
        print("=" * 60)
        print("检查GPU占用进程")
        print("=" * 60)
        
        try:
            # 使用nvidia-smi检查GPU进程
            result = subprocess.run(['nvidia-smi', '--query-compute-apps=pid,process_name,used_memory', 
                                   '--format=csv,noheader,nounits'], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                if lines and lines[0]:
                    print("当前占用GPU的进程:")
                    for line in lines:
                        if line.strip():
                            parts = line.split(', ')
                            if len(parts) >= 3:
                                pid, name, memory = parts[0], parts[1], parts[2]
                                print(f"  PID: {pid}, 进程: {name}, 显存: {memory}MB")
                else:
                    print("✓ 没有其他进程占用GPU")
            else:
                print("⚠ 无法获取GPU进程信息")
                
        except FileNotFoundError:
            print("⚠ nvidia-smi未找到，无法检查GPU进程")
        except Exception as e:
            print(f"✗ 检查GPU进程失败: {e}")
    
    def check_system_resources(self):
        """检查系统资源使用情况"""
        print("\n" + "=" * 60)
        print("系统资源使用情况")
        print("=" * 60)
        
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        print(f"CPU使用率: {cpu_percent}%")
        
        # 内存使用情况
        memory = psutil.virtual_memory()
        print(f"内存使用率: {memory.percent}%")
        print(f"可用内存: {memory.available / 1024**3:.1f} GB")
        print(f"总内存: {memory.total / 1024**3:.1f} GB")
        
        # GPU信息
        if torch.cuda.is_available():
            gpu_memory = torch.cuda.get_device_properties(0).total_memory
            gpu_allocated = torch.cuda.memory_allocated(0)
            gpu_cached = torch.cuda.memory_reserved(0)
            
            print(f"GPU显存总量: {gpu_memory / 1024**3:.1f} GB")
            print(f"GPU已分配: {gpu_allocated / 1024**3:.1f} GB")
            print(f"GPU已缓存: {gpu_cached / 1024**3:.1f} GB")
            print(f"GPU使用率: {(gpu_allocated / gpu_memory) * 100:.1f}%")
        
        # 磁盘使用情况
        disk = psutil.disk_usage('C:')
        print(f"磁盘使用率: {(disk.used / disk.total) * 100:.1f}%")
        print(f"磁盘可用空间: {disk.free / 1024**3:.1f} GB")
    
    def benchmark_inference_speed(self):
        """基准测试推理速度"""
        print("\n" + "=" * 60)
        print("推理速度基准测试")
        print("=" * 60)
        
        try:
            from ultralytics import YOLO
            
            # 测试不同分辨率的推理速度
            resolutions = [(640, 640), (1280, 1280), (1920, 1920)]
            models = ["yolov8n.pt", "yolov8s.pt"]
            
            for model_name in models:
                if os.path.exists(model_name):
                    print(f"\n测试模型: {model_name}")
                    model = YOLO(model_name)
                    model.to('cuda')
                    
                    for width, height in resolutions:
                        # 创建测试图像
                        test_image = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
                        
                        # 预热
                        for _ in range(3):
                            model(test_image, verbose=False)
                        
                        # 测试推理时间
                        times = []
                        for _ in range(10):
                            start = time.time()
                            results = model(test_image, verbose=False)
                            torch.cuda.synchronize()
                            times.append(time.time() - start)
                        
                        avg_time = np.mean(times)
                        fps = 1.0 / avg_time
                        print(f"  分辨率 {width}x{height}: {avg_time:.4f}s, FPS: {fps:.1f}")
                        
        except Exception as e:
            print(f"✗ 推理速度测试失败: {e}")
    
    def check_yolo_config(self):
        """检查YOLO配置"""
        print("\n" + "=" * 60)
        print("检查YOLO配置")
        print("=" * 60)
        
        config_files = ["config/config.json", "main_config.json"]
        
        for config_file in config_files:
            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    print(f"\n配置文件: {config_file}")
                    important_keys = ['conf', 'iou', 'rate', 'models_path']
                    for key in important_keys:
                        if key in config:
                            print(f"  {key}: {config[key]}")
                            
                except Exception as e:
                    print(f"✗ 读取配置文件失败: {e}")
    
    def monitor_realtime_performance(self, duration=30):
        """实时监控性能"""
        print(f"\n" + "=" * 60)
        print(f"实时性能监控 ({duration}秒)")
        print("=" * 60)
        
        start_time = time.time()
        
        while time.time() - start_time < duration:
            # GPU使用率
            if torch.cuda.is_available():
                gpu_util = torch.cuda.utilization(0) if hasattr(torch.cuda, 'utilization') else 0
                gpu_memory = torch.cuda.memory_allocated(0) / torch.cuda.get_device_properties(0).total_memory * 100
            else:
                gpu_util = 0
                gpu_memory = 0
            
            # CPU和内存
            cpu_util = psutil.cpu_percent()
            memory_util = psutil.virtual_memory().percent
            
            print(f"时间: {time.time() - start_time:.1f}s | "
                  f"CPU: {cpu_util:.1f}% | "
                  f"内存: {memory_util:.1f}% | "
                  f"GPU: {gpu_util:.1f}% | "
                  f"显存: {gpu_memory:.1f}%")
            
            time.sleep(2)
    
    def check_temperature(self):
        """检查温度"""
        print("\n" + "=" * 60)
        print("温度检查")
        print("=" * 60)
        
        try:
            # 检查CPU温度
            temps = psutil.sensors_temperatures()
            if temps:
                for name, entries in temps.items():
                    for entry in entries:
                        print(f"{name} - {entry.label}: {entry.current}°C")
            else:
                print("⚠ 无法获取温度信息")
                
        except Exception as e:
            print(f"⚠ 温度检查失败: {e}")
        
        try:
            # 使用nvidia-smi检查GPU温度
            result = subprocess.run(['nvidia-smi', '--query-gpu=temperature.gpu', 
                                   '--format=csv,noheader,nounits'], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                gpu_temp = result.stdout.strip()
                print(f"GPU温度: {gpu_temp}°C")
                
                if int(gpu_temp) > 80:
                    print("⚠ GPU温度过高，可能影响性能")
                elif int(gpu_temp) > 70:
                    print("⚠ GPU温度较高")
                else:
                    print("✓ GPU温度正常")
                    
        except Exception as e:
            print(f"⚠ GPU温度检查失败: {e}")

def main():
    print("ByteTrack性能诊断工具")
    print("=" * 80)
    
    diagnostic = PerformanceDiagnostic()
    
    # 运行所有检查
    diagnostic.check_system_resources()
    diagnostic.check_gpu_processes()
    diagnostic.check_temperature()
    diagnostic.check_yolo_config()
    diagnostic.benchmark_inference_speed()
    
    # 询问是否进行实时监控
    print("\n" + "=" * 80)
    choice = input("是否进行30秒实时性能监控? (y/n): ")
    if choice.lower() == 'y':
        diagnostic.monitor_realtime_performance(30)
    
    print("\n" + "=" * 80)
    print("诊断完成！请查看上述信息找出性能瓶颈。")

if __name__ == "__main__":
    main()
