# Conda环境配置文件 - ByteTrack
# 基于Yolov8与ByteTrack的高速公路智慧监控平台
# Python 3.9.16 + CUDA 11.8

name: ByteTrack

channels:
  - pytorch
  - nvidia
  - conda-forge
  - defaults

dependencies:
  # Python版本
  - python=3.9.16

  # PyTorch生态系统 (CUDA 11.8)
  - pytorch=2.1.2
  - torchvision=0.16.2
  - torchaudio=2.1.2
  - pytorch-cuda=11.8

  # CUDA工具包
  - cudatoolkit=11.8

  # 基础科学计算
  - numpy=1.24.3
  - scipy=1.10.1
  - matplotlib=3.7.2

  # 数据处理
  - pandas=2.0.3
  - openpyxl=3.1.2

  # 系统工具
  - psutil=5.9.6

  # pip依赖 (无法通过conda安装的包)
  - pip

  - pip:
    # 计算机视觉
    - opencv-python==********
    - pillow==9.5.0
    - supervision>=0.16.0

    # YOLO和目标检测
    - ultralytics>=8.0.0

    # Web框架
    - flask==2.3.3
    - flask-cors==4.0.0
    - werkzeug>=2.3.0,<3.0.0

    # 数据库
    - pymysql==1.1.0

    # 配置管理
    - python-dotenv==1.0.0

    # 网络请求
    - requests>=2.28.0

    # 进度条
    - tqdm>=4.64.0

    # 日期时间
    - python-dateutil>=2.8.0

    # 可选依赖 (WebSocket和实时功能)
    - flask-socketio==5.3.6
    - python-socketio>=5.9.0
    - eventlet>=0.33.3

    # 缓存 (可选)
    - redis>=5.0.1

    # 任务调度 (可选)
    - apscheduler>=3.10.4

    # 生产服务器 (可选)
    - gunicorn>=21.2.0

    # 开发工具 (可选)
    - pytest>=7.0.0
    - black>=22.0.0
    - flake8>=5.0.0
