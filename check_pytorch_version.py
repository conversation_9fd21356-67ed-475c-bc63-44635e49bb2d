#!/usr/bin/env python
# -*- coding: utf-8 -*-

import torch
import sys

print("=" * 50)
print("PyTorch环境检查")
print("=" * 50)

print(f"Python版本: {sys.version}")
print(f"PyTorch版本: {torch.__version__}")
print(f"CUDA编译支持: {torch.cuda.is_available()}")

if torch.cuda.is_available():
    print(f"CUDA版本: {torch.version.cuda}")
    print(f"cuDNN版本: {torch.backends.cudnn.version()}")
    print(f"GPU数量: {torch.cuda.device_count()}")
    
    for i in range(torch.cuda.device_count()):
        print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
        props = torch.cuda.get_device_properties(i)
        print(f"  显存: {props.total_memory / 1024**3:.1f} GB")
else:
    print("CUDA不可用")
    print("可能原因:")
    print("1. PyTorch是CPU版本")
    print("2. NVIDIA驱动问题")
    print("3. CUDA Toolkit未安装")

print("=" * 50)
