# 基于Yolov8与ByteTrack的高速公路智慧监控平台 - 快速启动指南

## 🚀 一键启动流程

### 步骤1: 创建虚拟环境
```bash
# 删除旧环境（如果存在）
rm -rf venv

# 创建新虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate
```

### 步骤2: 安装依赖
```bash
# 方法1: 使用自动安装脚本（推荐）
python install_dependencies.py

# 方法2: 手动安装核心依赖
pip install flask flask-cors pymysql python-dotenv
pip install ultralytics opencv-python numpy pillow
```

### 步骤3: 配置数据库
```bash
# 启动MySQL服务
# Windows: net start mysql
# Linux: sudo systemctl start mysql

# 创建数据库
mysql -u root -p
CREATE DATABASE yolo CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
EXIT;

# 导入数据库结构和数据
mysql -u root -p yolo < yolo.sql
```

### 步骤4: 测试连接
```bash
# 测试数据库连接
python test_db.py
```

### 步骤5: 启动系统
```bash
# 方法1: 使用简化启动脚本（推荐新手）
python start_server.py

# 方法2: 使用完整启动脚本
python app_main.py

# 方法3: 使用原版启动脚本
python app.py
```

### 步骤6: 验证系统
打开浏览器访问：
- 主页: http://127.0.0.1:5500/
- 健康检查: http://127.0.0.1:5500/health
- API文档: http://127.0.0.1:5500/api/docs

## 🔧 常见问题解决

### 问题1: 依赖安装失败
```bash
# 使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 分步安装核心包
pip install flask==2.3.3
pip install pymysql==1.1.0
pip install ultralytics==8.0.196
```

### 问题2: 数据库连接失败
```bash
# 检查MySQL服务状态
# Windows: sc query mysql
# Linux: systemctl status mysql

# 检查配置文件
cat config/end-back.env

# 重置MySQL密码
mysql -u root -p
ALTER USER 'root'@'localhost' IDENTIFIED BY '123456';
FLUSH PRIVILEGES;
```

### 问题3: 端口被占用
```bash
# 查看端口占用
netstat -ano | findstr 5500

# 修改端口（编辑config/end-back.env）
PORT=5501
```

### 问题4: 模型文件缺失
```bash
# 创建模型目录
mkdir models

# 下载默认模型（可选）
cd models
curl -L https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8n.pt -o yolov8n.pt
```

## 📁 项目结构说明

```
yolo/
├── app.py                    # 原版启动文件
├── app_main.py              # 完整启动文件
├── start_server.py          # 简化启动文件
├── test_db.py               # 数据库测试脚本
├── install_dependencies.py  # 依赖安装脚本
├── yolo.sql                 # 数据库结构和数据
├── requirements.txt         # 主要依赖
├── config/
│   └── end-back.env        # 环境配置
├── backend/                # 后端代码
│   ├── api/               # API接口
│   ├── services/          # 业务服务
│   ├── utils/             # 工具函数
│   ├── websocket/         # WebSocket处理
│   └── tasks/             # 后台任务
├── static/                # 静态文件
├── models/                # YOLO模型文件
└── logs/                  # 日志文件
```

## 🎯 功能测试

### 测试API接口
```bash
# 测试基本接口
curl http://127.0.0.1:5500/
curl http://127.0.0.1:5500/health

# 测试登录接口
curl -X POST http://127.0.0.1:5500/api/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"123456"}'

# 测试监控点接口
curl http://127.0.0.1:5500/api/monitors
```

### 测试数据库数据
```sql
-- 查看监控点数据
SELECT id, location, highway_section, connection_status FROM monitor LIMIT 5;

-- 查看警报数据
SELECT id, location, description, create_time FROM alarm ORDER BY create_time DESC LIMIT 5;

-- 查看用户数据
SELECT id, username, grade FROM user;
```

## 🔄 开发模式

### 启用调试模式
编辑 `config/end-back.env`:
```ini
DEBUG=true
```

### 实时重载
```bash
export FLASK_APP=start_server.py
export FLASK_ENV=development
flask run --host=0.0.0.0 --port=5500
```

## 🚀 生产部署

### 使用Gunicorn
```bash
pip install gunicorn
gunicorn --bind 0.0.0.0:5500 --workers 4 start_server:app
```

### 使用Docker
```bash
# 构建镜像
docker build -t highway-yolo .

# 运行容器
docker run -p 5500:5500 highway-yolo
```

## 📊 系统监控

### 查看日志
```bash
# 应用日志
tail -f logs/app.log

# 错误日志
tail -f logs/error.log
```

### 性能监控
```bash
# 查看系统资源
python -c "
import psutil
print(f'CPU: {psutil.cpu_percent()}%')
print(f'内存: {psutil.virtual_memory().percent}%')
print(f'磁盘: {psutil.disk_usage(\"/\").percent}%')
"
```

## 🎨 前端开发

### 推荐技术栈
- Vue 3 + TypeScript + Vite
- Element Plus (UI组件)
- ECharts (图表)
- Socket.IO (实时通信)

### 快速开始
```bash
# 创建前端项目
npm create vue@latest highway-monitor-frontend
cd highway-monitor-frontend

# 安装依赖
npm install element-plus @element-plus/icons-vue
npm install axios socket.io-client echarts

# 启动开发服务器
npm run dev
```

## 📞 技术支持

如果遇到问题，请按以下顺序排查：

1. **检查Python版本**: 确保使用Python 3.8+
2. **检查虚拟环境**: 确保已激活虚拟环境
3. **检查依赖安装**: 运行 `python install_dependencies.py`
4. **检查数据库**: 运行 `python test_db.py`
5. **检查配置文件**: 确保 `config/end-back.env` 配置正确
6. **查看日志**: 检查 `logs/` 目录下的日志文件

## 🎉 成功标志

当您看到以下输出时，说明系统启动成功：

```
================================================================================
基于Yolov8与ByteTrack的高速公路智慧监控平台
================================================================================
✓ 依赖包检查通过
✓ 数据库连接成功，共有 8 个表
正在创建Flask应用...
--------------------------------------------------------------------------------
服务地址: http://127.0.0.1:5500
调试模式: False
数据库状态: 正常
--------------------------------------------------------------------------------
系统启动中...
按 Ctrl+C 停止服务
 * Running on http://127.0.0.1:5500
```

现在您可以开始使用基于Yolov8与ByteTrack的高速公路智慧监控平台了！🎊
