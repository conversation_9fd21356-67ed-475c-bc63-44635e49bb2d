#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行时CUDA使用情况检查脚本
"""

import torch
import psutil
import os
import time
from ultralytics import YOLO

def check_cuda_usage():
    print("=== 运行时CUDA使用情况检查 ===")
    
    # 1. 基础CUDA信息
    print(f"\n1. 基础CUDA信息:")
    print(f"   PyTorch版本: {torch.__version__}")
    print(f"   CUDA可用: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        print(f"   CUDA版本: {torch.version.cuda}")
        print(f"   GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"   GPU {i}: {torch.cuda.get_device_name(i)}")
    
    # 2. 检查GPU内存使用情况
    if torch.cuda.is_available():
        print(f"\n2. GPU内存使用情况:")
        for i in range(torch.cuda.device_count()):
            memory_allocated = torch.cuda.memory_allocated(i) / 1024**3  # GB
            memory_reserved = torch.cuda.memory_reserved(i) / 1024**3   # GB
            memory_total = torch.cuda.get_device_properties(i).total_memory / 1024**3  # GB
            print(f"   GPU {i}:")
            print(f"     已分配: {memory_allocated:.2f} GB")
            print(f"     已保留: {memory_reserved:.2f} GB")
            print(f"     总内存: {memory_total:.2f} GB")
            print(f"     使用率: {(memory_allocated/memory_total)*100:.1f}%")
    
    # 3. 测试YOLO模型CUDA使用
    print(f"\n3. YOLO模型CUDA测试:")
    try:
        # 加载一个轻量级模型进行测试
        model = YOLO('yolov8n.pt')
        
        # 检查模型默认设备
        print(f"   模型默认设备: {next(model.model.parameters()).device}")
        
        # 强制移动到CUDA
        if torch.cuda.is_available():
            model.to('cuda')
            print(f"   模型移动到CUDA后设备: {next(model.model.parameters()).device}")
            
            # 创建测试张量
            test_tensor = torch.randn(1, 3, 640, 640).cuda()
            print(f"   测试张量设备: {test_tensor.device}")
            
            # 检查GPU使用情况变化
            memory_after = torch.cuda.memory_allocated(0) / 1024**3
            print(f"   加载模型后GPU内存: {memory_after:.2f} GB")
        
    except Exception as e:
        print(f"   YOLO模型测试失败: {e}")
    
    # 4. 检查当前Python进程的GPU使用
    print(f"\n4. 当前进程信息:")
    current_process = psutil.Process()
    print(f"   进程ID: {current_process.pid}")
    print(f"   内存使用: {current_process.memory_info().rss / 1024**3:.2f} GB")
    print(f"   CPU使用率: {current_process.cpu_percent()}%")
    
    # 5. 环境变量检查
    print(f"\n5. 相关环境变量:")
    cuda_vars = ['CUDA_VISIBLE_DEVICES', 'CUDA_DEVICE_ORDER', 'USE_CUDA', 'YOLO_DEVICE']
    for var in cuda_vars:
        value = os.environ.get(var, '未设置')
        print(f"   {var}: {value}")
    
    print(f"\n=== 检查完成 ===")

if __name__ == "__main__":
    check_cuda_usage()