<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="900" viewBox="0 0 1400 900" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradients -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f7fafc;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="videoGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#1a202c;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2d3748;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="successGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#48bb78;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#38a169;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="warningGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ed8936;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#dd6b20;stop-opacity:1" />
    </linearGradient>
    
    <!-- Filters -->
    <filter id="cardShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="8" stdDeviation="16" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
    <filter id="glowEffect" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/> 
      </feMerge>
    </filter>
    <filter id="videoShadow" x="-10%" y="-10%" width="120%" height="120%">
      <feDropShadow dx="0" dy="4" stdDeviation="12" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="1400" height="900" fill="#f7fafc"/>
  <defs>
    <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
      <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#e2e8f0" stroke-width="0.5" opacity="0.3"/>
    </pattern>
  </defs>
  <rect width="1400" height="900" fill="url(#grid)"/>
  
  <!-- Header Section -->
  <rect width="1400" height="80" fill="url(#primaryGradient)" filter="url(#cardShadow)"/>
  
  <!-- Header Content -->
  <g transform="translate(30, 20)">
    <!-- Logo and Title -->
    <circle cx="25" cy="20" r="18" fill="#ffffff" opacity="0.2"/>
    <text x="20" y="27" font-family="Arial, sans-serif" font-size="16" fill="#ffffff">📹</text>
    <text x="60" y="30" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#ffffff">实时监控</text>
    
    <!-- Status Indicator -->
    <g transform="translate(200, 10)">
      <circle cx="10" cy="10" r="8" fill="#48bb78">
        <animate attributeName="opacity" values="1;0.5;1" dur="2s" repeatCount="indefinite"/>
      </circle>
      <text x="25" y="16" font-family="Arial, sans-serif" font-size="14" fill="#ffffff">系统运行正常</text>
    </g>
    
    <!-- Navigation Breadcrumb -->
    <g transform="translate(400, 15)">
      <text x="0" y="0" font-family="Arial, sans-serif" font-size="12" fill="#ffffff" opacity="0.8">首页</text>
      <text x="40" y="0" font-family="Arial, sans-serif" font-size="12" fill="#ffffff" opacity="0.8">></text>
      <text x="55" y="0" font-family="Arial, sans-serif" font-size="12" fill="#ffffff">实时监控</text>
    </g>
    
    <!-- Time Display -->
    <g transform="translate(1200, 15)">
      <text x="0" y="0" font-family="Arial, sans-serif" font-size="14" fill="#ffffff" id="currentTime">2024-01-15 14:30:25</text>
    </g>
  </g>
  
  <!-- Main Content Container -->
  <rect x="20" y="100" width="1360" height="780" rx="16" fill="#ffffff" filter="url(#cardShadow)"/>
  
  <!-- Control Panel Header -->
  <g transform="translate(40, 130)">
    <!-- Monitor Selection -->
    <g>
      <text x="0" y="0" font-family="Arial, sans-serif" font-size="14" fill="#718096">监控点选择:</text>
      <rect x="80" y="-15" width="120" height="30" rx="8" fill="#ffffff" stroke="#e2e8f0" stroke-width="1"/>
      <text x="90" y="5" font-family="Arial, sans-serif" font-size="12" fill="#2d3748">监控点001</text>
      <text x="185" y="5" font-family="Arial, sans-serif" font-size="12" fill="#718096">▼</text>
    </g>
    
    <!-- Status Display -->
    <g transform="translate(220, 0)">
      <text x="0" y="0" font-family="Arial, sans-serif" font-size="14" fill="#718096">状态:</text>
      <circle cx="45" cy="-5" r="6" fill="#48bb78">
        <animate attributeName="opacity" values="1;0.6;1" dur="1.5s" repeatCount="indefinite"/>
      </circle>
      <text x="60" y="0" font-family="Arial, sans-serif" font-size="12" fill="#48bb78" font-weight="bold">在线</text>
    </g>
    
    <!-- Algorithm Selection -->
    <g transform="translate(320, 0)">
      <text x="0" y="0" font-family="Arial, sans-serif" font-size="14" fill="#718096">算法:</text>
      <rect x="40" y="-15" width="100" height="30" rx="8" fill="#ffffff" stroke="#e2e8f0" stroke-width="1"/>
      <text x="50" y="5" font-family="Arial, sans-serif" font-size="12" fill="#2d3748">YOLO v8</text>
      <text x="125" y="5" font-family="Arial, sans-serif" font-size="12" fill="#718096">▼</text>
    </g>
    
    <!-- Performance Metrics -->
    <g transform="translate(900, 0)">
      <rect x="0" y="-20" width="400" height="40" rx="12" fill="#f7fafc" stroke="#e2e8f0" stroke-width="1"/>
      
      <g transform="translate(20, 0)">
        <text x="0" y="-5" font-family="Arial, sans-serif" font-size="12" fill="#718096">FPS:</text>
        <text x="35" y="-5" font-family="Arial, sans-serif" font-size="14" fill="#48bb78" font-weight="bold">25</text>
        
        <text x="80" y="-5" font-family="Arial, sans-serif" font-size="12" fill="#718096">延迟:</text>
        <text x="115" y="-5" font-family="Arial, sans-serif" font-size="14" fill="#ed8936" font-weight="bold">45ms</text>
        
        <text x="170" y="-5" font-family="Arial, sans-serif" font-size="12" fill="#718096">CPU:</text>
        <text x="200" y="-5" font-family="Arial, sans-serif" font-size="14" fill="#4299e1" font-weight="bold">35%</text>
        
        <text x="250" y="-5" font-family="Arial, sans-serif" font-size="12" fill="#718096">GPU:</text>
        <text x="280" y="-5" font-family="Arial, sans-serif" font-size="14" fill="#9f7aea" font-weight="bold">68%</text>
      </g>
    </g>
  </g>
  
  <!-- Main Content Area -->
  <g transform="translate(40, 180)">
    <!-- Video Stream Container -->
    <rect x="0" y="0" width="900" height="500" rx="16" fill="url(#videoGradient)" filter="url(#videoShadow)"/>
    
    <!-- Video Stream Header -->
    <g transform="translate(20, 30)">
      <text x="0" y="0" font-family="Arial, sans-serif" font-size="16" fill="#ffffff" font-weight="bold">实时视频流</text>
      <circle cx="120" cy="-5" r="4" fill="#f56565">
        <animate attributeName="opacity" values="1;0.3;1" dur="1s" repeatCount="indefinite"/>
      </circle>
      <text x="135" y="0" font-family="Arial, sans-serif" font-size="12" fill="#f56565">LIVE</text>
      
      <!-- Resolution Info -->
      <text x="700" y="0" font-family="Arial, sans-serif" font-size="12" fill="#a0aec0">1920x1080 @ 30fps</text>
    </g>
    
    <!-- Video Content Area -->
    <rect x="20" y="50" width="860" height="400" rx="12" fill="#000000" stroke="#4a5568" stroke-width="2"/>
    
    <!-- Simulated Video Content -->
    <g transform="translate(20, 50)">
      <!-- Road Background -->
      <rect x="0" y="0" width="860" height="400" rx="12" fill="#2d3748"/>
      
      <!-- Road Lines -->
      <g stroke="#ffffff" stroke-width="2" opacity="0.6">
        <line x1="0" y1="200" x2="860" y2="200" stroke-dasharray="20 10"/>
        <line x1="0" y1="150" x2="860" y2="150"/>
        <line x1="0" y1="250" x2="860" y2="250"/>
      </g>
      
      <!-- Detected Vehicles -->
      <!-- Car 1 -->
      <g transform="translate(150, 120)">
        <rect x="0" y="0" width="80" height="50" rx="8" fill="#4299e1" stroke="#48bb78" stroke-width="3"/>
        <text x="40" y="30" font-family="Arial, sans-serif" font-size="12" fill="#ffffff" text-anchor="middle">🚗</text>
        <text x="40" y="45" font-family="Arial, sans-serif" font-size="8" fill="#ffffff" text-anchor="middle">Car</text>
        
        <!-- Detection Box Label -->
        <rect x="-5" y="-25" width="60" height="20" rx="4" fill="#48bb78" opacity="0.9"/>
        <text x="25" y="-10" font-family="Arial, sans-serif" font-size="10" fill="#ffffff" text-anchor="middle">Car 0.92</text>
        
        <!-- Tracking ID -->
        <circle cx="-10" cy="-10" r="8" fill="#ed8936"/>
        <text x="-10" y="-6" font-family="Arial, sans-serif" font-size="8" fill="#ffffff" text-anchor="middle">1</text>
      </g>
      
      <!-- Truck -->
      <g transform="translate(400, 100)">
        <rect x="0" y="0" width="100" height="70" rx="8" fill="#ed8936" stroke="#48bb78" stroke-width="3"/>
        <text x="50" y="40" font-family="Arial, sans-serif" font-size="16" fill="#ffffff" text-anchor="middle">🚛</text>
        <text x="50" y="60" font-family="Arial, sans-serif" font-size="8" fill="#ffffff" text-anchor="middle">Truck</text>
        
        <!-- Detection Box Label -->
        <rect x="-5" y="-25" width="70" height="20" rx="4" fill="#48bb78" opacity="0.9"/>
        <text x="30" y="-10" font-family="Arial, sans-serif" font-size="10" fill="#ffffff" text-anchor="middle">Truck 0.88</text>
        
        <!-- Tracking ID -->
        <circle cx="-10" cy="-10" r="8" fill="#ed8936"/>
        <text x="-10" y="-6" font-family="Arial, sans-serif" font-size="8" fill="#ffffff" text-anchor="middle">2</text>
      </g>
      
      <!-- Car 2 -->
      <g transform="translate(650, 180)">
        <rect x="0" y="0" width="80" height="50" rx="8" fill="#9f7aea" stroke="#48bb78" stroke-width="3"/>
        <text x="40" y="30" font-family="Arial, sans-serif" font-size="12" fill="#ffffff" text-anchor="middle">🚗</text>
        <text x="40" y="45" font-family="Arial, sans-serif" font-size="8" fill="#ffffff" text-anchor="middle">Car</text>
        
        <!-- Detection Box Label -->
        <rect x="-5" y="-25" width="60" height="20" rx="4" fill="#48bb78" opacity="0.9"/>
        <text x="25" y="-10" font-family="Arial, sans-serif" font-size="10" fill="#ffffff" text-anchor="middle">Car 0.95</text>
        
        <!-- Tracking ID -->
        <circle cx="-10" cy="-10" r="8" fill="#ed8936"/>
        <text x="-10" y="-6" font-family="Arial, sans-serif" font-size="8" fill="#ffffff" text-anchor="middle">3</text>
      </g>
      
      <!-- Motion Trails -->
      <g stroke="#48bb78" stroke-width="2" fill="none" opacity="0.6">
        <path d="M 50 145 Q 100 140 150 145" stroke-dasharray="5 5">
          <animate attributeName="stroke-dashoffset" values="0;-10" dur="1s" repeatCount="indefinite"/>
        </path>
        <path d="M 300 135 Q 350 130 400 135" stroke-dasharray="5 5">
          <animate attributeName="stroke-dashoffset" values="0;-10" dur="1s" repeatCount="indefinite"/>
        </path>
        <path d="M 550 205 Q 600 200 650 205" stroke-dasharray="5 5">
          <animate attributeName="stroke-dashoffset" values="0;-10" dur="1s" repeatCount="indefinite"/>
        </path>
      </g>
      
      <!-- Speed Indicators -->
      <g transform="translate(150, 80)">
        <rect x="0" y="0" width="50" height="15" rx="7" fill="#4299e1" opacity="0.8"/>
        <text x="25" y="11" font-family="Arial, sans-serif" font-size="8" fill="#ffffff" text-anchor="middle">65km/h</text>
      </g>
      
      <g transform="translate(400, 60)">
        <rect x="0" y="0" width="50" height="15" rx="7" fill="#ed8936" opacity="0.8"/>
        <text x="25" y="11" font-family="Arial, sans-serif" font-size="8" fill="#ffffff" text-anchor="middle">45km/h</text>
      </g>
      
      <g transform="translate(650, 140)">
        <rect x="0" y="0" width="50" height="15" rx="7" fill="#48bb78" opacity="0.8"/>
        <text x="25" y="11" font-family="Arial, sans-serif" font-size="8" fill="#ffffff" text-anchor="middle">72km/h</text>
      </g>
    </g>
    
    <!-- Video Controls Overlay -->
    <g transform="translate(750, 400)">
      <rect x="0" y="0" width="120" height="40" rx="8" fill="#000000" opacity="0.7"/>
      <circle cx="20" cy="20" r="12" fill="#48bb78" opacity="0.8"/>
      <text x="20" y="25" font-family="Arial, sans-serif" font-size="10" fill="#ffffff" text-anchor="middle">▶️</text>
      
      <circle cx="50" cy="20" r="12" fill="#ed8936" opacity="0.8"/>
      <text x="50" y="25" font-family="Arial, sans-serif" font-size="10" fill="#ffffff" text-anchor="middle">⏸️</text>
      
      <circle cx="80" cy="20" r="12" fill="#f56565" opacity="0.8"/>
      <text x="80" y="25" font-family="Arial, sans-serif" font-size="10" fill="#ffffff" text-anchor="middle">⏹️</text>
      
      <circle cx="110" cy="20" r="12" fill="#4299e1" opacity="0.8"/>
      <text x="110" y="25" font-family="Arial, sans-serif" font-size="10" fill="#ffffff" text-anchor="middle">📸</text>
    </g>
  </g>
  
  <!-- Detection Results Panel -->
  <g transform="translate(960, 180)">
    <rect x="0" y="0" width="400" height="500" rx="16" fill="url(#cardGradient)" filter="url(#cardShadow)"/>
    
    <!-- Panel Header -->
    <g transform="translate(20, 30)">
      <text x="0" y="0" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#2d3748">检测结果</text>
      <circle cx="100" cy="-5" r="4" fill="#48bb78">
        <animate attributeName="opacity" values="1;0.5;1" dur="2s" repeatCount="indefinite"/>
      </circle>
      <text x="115" y="0" font-family="Arial, sans-serif" font-size="12" fill="#48bb78">实时更新</text>
    </g>
    
    <!-- Detection Statistics -->
    <g transform="translate(20, 70)">
      <!-- Vehicle Count -->
      <g>
        <rect x="0" y="0" width="360" height="60" rx="12" fill="#ffffff" stroke="#e2e8f0" stroke-width="1"/>
        <circle cx="30" cy="30" r="20" fill="#4299e1" opacity="0.1"/>
        <text x="30" y="36" font-family="Arial, sans-serif" font-size="16" fill="#4299e1" text-anchor="middle">🚗</text>
        
        <text x="70" y="25" font-family="Arial, sans-serif" font-size="14" fill="#718096">汽车:</text>
        <text x="120" y="25" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#2d3748">15</text>
        <text x="160" y="25" font-family="Arial, sans-serif" font-size="12" fill="#718096">辆</text>
        
        <text x="70" y="45" font-family="Arial, sans-serif" font-size="12" fill="#48bb78">↗ +2 较上分钟</text>
        
        <!-- Mini Chart -->
        <g transform="translate(250, 15)">
          <polyline points="0,30 20,25 40,20 60,15 80,10" fill="none" stroke="#4299e1" stroke-width="2"/>
          <circle cx="80" cy="10" r="2" fill="#4299e1"/>
        </g>
      </g>
      
      <!-- Truck Count -->
      <g transform="translate(0, 80)">
        <rect x="0" y="0" width="360" height="60" rx="12" fill="#ffffff" stroke="#e2e8f0" stroke-width="1"/>
        <circle cx="30" cy="30" r="20" fill="#ed8936" opacity="0.1"/>
        <text x="30" y="36" font-family="Arial, sans-serif" font-size="16" fill="#ed8936" text-anchor="middle">🚛</text>
        
        <text x="70" y="25" font-family="Arial, sans-serif" font-size="14" fill="#718096">卡车:</text>
        <text x="120" y="25" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#2d3748">3</text>
        <text x="150" y="25" font-family="Arial, sans-serif" font-size="12" fill="#718096">辆</text>
        
        <text x="70" y="45" font-family="Arial, sans-serif" font-size="12" fill="#ed8936">→ 无变化</text>
        
        <!-- Mini Chart -->
        <g transform="translate(250, 15)">
          <polyline points="0,20 20,20 40,20 60,20 80,20" fill="none" stroke="#ed8936" stroke-width="2"/>
          <circle cx="80" cy="20" r="2" fill="#ed8936"/>
        </g>
      </g>
      
      <!-- Pedestrian Count -->
      <g transform="translate(0, 160)">
        <rect x="0" y="0" width="360" height="60" rx="12" fill="#ffffff" stroke="#e2e8f0" stroke-width="1"/>
        <circle cx="30" cy="30" r="20" fill="#48bb78" opacity="0.1"/>
        <text x="30" y="36" font-family="Arial, sans-serif" font-size="16" fill="#48bb78" text-anchor="middle">🚶</text>
        
        <text x="70" y="25" font-family="Arial, sans-serif" font-size="14" fill="#718096">行人:</text>
        <text x="120" y="25" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#2d3748">0</text>
        <text x="150" y="25" font-family="Arial, sans-serif" font-size="12" fill="#718096">人</text>
        
        <text x="70" y="45" font-family="Arial, sans-serif" font-size="12" fill="#718096">安全状态</text>
        
        <!-- Status Indicator -->
        <g transform="translate(280, 20)">
          <circle cx="20" cy="10" r="8" fill="#48bb78" opacity="0.2"/>
          <text x="20" y="15" font-family="Arial, sans-serif" font-size="10" fill="#48bb78" text-anchor="middle">✓</text>
        </g>
      </g>
      
      <!-- Performance Metrics -->
      <g transform="translate(0, 240)">
        <rect x="0" y="0" width="360" height="100" rx="12" fill="#f7fafc" stroke="#e2e8f0" stroke-width="1"/>
        
        <text x="20" y="25" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2d3748">性能指标</text>
        
        <!-- Confidence Score -->
        <g transform="translate(20, 40)">
          <text x="0" y="0" font-family="Arial, sans-serif" font-size="12" fill="#718096">置信度:</text>
          <text x="60" y="0" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#48bb78">0.85</text>
          
          <!-- Progress Bar -->
          <rect x="120" y="-8" width="200" height="8" rx="4" fill="#e2e8f0"/>
          <rect x="120" y="-8" width="170" height="8" rx="4" fill="#48bb78"/>
        </g>
        
        <!-- Processing Speed -->
        <g transform="translate(20, 65)">
          <text x="0" y="0" font-family="Arial, sans-serif" font-size="12" fill="#718096">处理速度:</text>
          <text x="70" y="0" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#4299e1">25</text>
          <text x="100" y="0" font-family="Arial, sans-serif" font-size="12" fill="#718096">FPS</text>
          
          <!-- Speed Indicator -->
          <g transform="translate(250, -10)">
            <circle cx="20" cy="10" r="15" fill="none" stroke="#e2e8f0" stroke-width="3"/>
            <circle cx="20" cy="10" r="15" fill="none" stroke="#4299e1" stroke-width="3" 
                    stroke-dasharray="62" stroke-dashoffset="25" transform="rotate(-90 20 10)"/>
            <text x="20" y="15" font-family="Arial, sans-serif" font-size="8" fill="#4299e1" text-anchor="middle">25</text>
          </g>
        </g>
      </g>
    </g>
  </g>
  
  <!-- Control Panel -->
  <g transform="translate(40, 700)">
    <rect x="0" y="0" width="1320" height="160" rx="16" fill="url(#cardGradient)" filter="url(#cardShadow)"/>
    
    <!-- Control Panel Header -->
    <g transform="translate(20, 30)">
      <text x="0" y="0" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#2d3748">控制面板</text>
    </g>
    
    <!-- Control Buttons -->
    <g transform="translate(20, 60)">
      <!-- Start Detection -->
      <g>
        <rect x="0" y="0" width="120" height="40" rx="12" fill="url(#successGradient)" filter="url(#cardShadow)"/>
        <text x="15" y="25" font-family="Arial, sans-serif" font-size="14" fill="#ffffff">▶️ 开始检测</text>
      </g>
      
      <!-- Pause -->
      <g transform="translate(140, 0)">
        <rect x="0" y="0" width="80" height="40" rx="12" fill="#ed8936" filter="url(#cardShadow)"/>
        <text x="15" y="25" font-family="Arial, sans-serif" font-size="14" fill="#ffffff">⏸️ 暂停</text>
      </g>
      
      <!-- Stop -->
      <g transform="translate(240, 0)">
        <rect x="0" y="0" width="80" height="40" rx="12" fill="#f56565" filter="url(#cardShadow)"/>
        <text x="15" y="25" font-family="Arial, sans-serif" font-size="14" fill="#ffffff">⏹️ 停止</text>
      </g>
      
      <!-- Screenshot -->
      <g transform="translate(340, 0)">
        <rect x="0" y="0" width="80" height="40" rx="12" fill="#4299e1" filter="url(#cardShadow)"/>
        <text x="15" y="25" font-family="Arial, sans-serif" font-size="14" fill="#ffffff">📸 截图</text>
      </g>
      
      <!-- Record -->
      <g transform="translate(440, 0)">
        <rect x="0" y="0" width="80" height="40" rx="12" fill="#9f7aea" filter="url(#cardShadow)"/>
        <text x="15" y="25" font-family="Arial, sans-serif" font-size="14" fill="#ffffff">🎥 录制</text>
      </g>
    </g>
    
    <!-- Detection Parameters -->
    <g transform="translate(600, 50)">
      <text x="0" y="0" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2d3748">检测参数</text>
      
      <!-- Confidence Threshold -->
      <g transform="translate(0, 30)">
        <text x="0" y="0" font-family="Arial, sans-serif" font-size="12" fill="#718096">置信度阈值:</text>
        <text x="80" y="0" font-family="Arial, sans-serif" font-size="12" fill="#2d3748" font-weight="bold">0.5</text>
        
        <!-- Slider -->
        <rect x="120" y="-8" width="200" height="8" rx="4" fill="#e2e8f0"/>
        <rect x="120" y="-8" width="100" height="8" rx="4" fill="#667eea"/>
        <circle cx="220" cy="-4" r="8" fill="#667eea" stroke="#ffffff" stroke-width="2"/>
      </g>
      
      <!-- IOU Threshold -->
      <g transform="translate(0, 60)">
        <text x="0" y="0" font-family="Arial, sans-serif" font-size="12" fill="#718096">IOU阈值:</text>
        <text x="70" y="0" font-family="Arial, sans-serif" font-size="12" fill="#2d3748" font-weight="bold">0.4</text>
        
        <!-- Slider -->
        <rect x="120" y="-8" width="200" height="8" rx="4" fill="#e2e8f0"/>
        <rect x="120" y="-8" width="80" height="8" rx="4" fill="#48bb78"/>
        <circle cx="200" cy="-4" r="8" fill="#48bb78" stroke="#ffffff" stroke-width="2"/>
      </g>
    </g>
    
    <!-- Display Options -->
    <g transform="translate(1000, 50)">
      <text x="0" y="0" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2d3748">显示选项</text>
      
      <!-- Show Labels -->
      <g transform="translate(0, 30)">
        <rect x="0" y="-8" width="16" height="16" rx="3" fill="#48bb78" stroke="#48bb78" stroke-width="1"/>
        <text x="8" y="4" font-family="Arial, sans-serif" font-size="10" fill="#ffffff" text-anchor="middle">✓</text>
        <text x="25" y="0" font-family="Arial, sans-serif" font-size="12" fill="#2d3748">显示标签</text>
      </g>
      
      <!-- Show Tracks -->
      <g transform="translate(120, 30)">
        <rect x="0" y="-8" width="16" height="16" rx="3" fill="#48bb78" stroke="#48bb78" stroke-width="1"/>
        <text x="8" y="4" font-family="Arial, sans-serif" font-size="10" fill="#ffffff" text-anchor="middle">✓</text>
        <text x="25" y="0" font-family="Arial, sans-serif" font-size="12" fill="#2d3748">显示轨迹</text>
      </g>
      
      <!-- Save Results -->
      <g transform="translate(0, 60)">
        <rect x="0" y="-8" width="16" height="16" rx="3" fill="#48bb78" stroke="#48bb78" stroke-width="1"/>
        <text x="8" y="4" font-family="Arial, sans-serif" font-size="10" fill="#ffffff" text-anchor="middle">✓</text>
        <text x="25" y="0" font-family="Arial, sans-serif" font-size="12" fill="#2d3748">保存结果</text>
      </g>
    </g>
  </g>
  
  <!-- Footer Status Bar -->
  <g transform="translate(0, 880)">
    <rect width="1400" height="20" fill="#2d3748" opacity="0.8"/>
    
    <g transform="translate(20, 15)">
      <text x="0" y="0" font-family="Arial, sans-serif" font-size="10" fill="#ffffff">系统状态: 正常运行</text>
      <text x="150" y="0" font-family="Arial, sans-serif" font-size="10" fill="#ffffff">|</text>
      <text x="170" y="0" font-family="Arial, sans-serif" font-size="10" fill="#ffffff">连接状态: 已连接</text>
      <text x="280" y="0" font-family="Arial, sans-serif" font-size="10" fill="#ffffff">|</text>
      <text x="300" y="0" font-family="Arial, sans-serif" font-size="10" fill="#ffffff">最后更新: 2024-01-15 14:30:25</text>
      
      <text x="1200" y="0" font-family="Arial, sans-serif" font-size="10" fill="#a0aec0">基于Yolov8与ByteTrack的高速公路智慧监控平台 v2.0</text>
    </g>
  </g>
</svg>