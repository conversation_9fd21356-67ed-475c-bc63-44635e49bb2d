# -*- coding: utf-8 -*-
# @Description : 完整系统启动脚本
# @Date : 2025年6月21日

import os
import sys
import time
import subprocess
import threading
import signal
from datetime import datetime

class SystemManager:
    """系统管理器"""
    
    def __init__(self):
        self.processes = {}
        self.running = True
        
    def start_service(self, name, script, port):
        """启动服务"""
        try:
            print(f"🚀 启动{name}服务 (端口:{port})...")
            
            # 启动Python脚本
            process = subprocess.Popen([
                sys.executable, script
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            self.processes[name] = {
                'process': process,
                'script': script,
                'port': port,
                'start_time': datetime.now()
            }
            
            print(f"✅ {name}服务启动成功 (PID: {process.pid})")
            return True
            
        except Exception as e:
            print(f"❌ {name}服务启动失败: {e}")
            return False
    
    def check_service_health(self, name, port):
        """检查服务健康状态"""
        try:
            import requests
            response = requests.get(f"http://127.0.0.1:{port}/health", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def monitor_services(self):
        """监控服务状态"""
        while self.running:
            for name, info in self.processes.items():
                process = info['process']
                
                # 检查进程是否还在运行
                if process.poll() is not None:
                    print(f"⚠️ {name}服务异常退出，尝试重启...")
                    self.restart_service(name)
                
                # 检查服务健康状态
                elif not self.check_service_health(name, info['port']):
                    print(f"⚠️ {name}服务健康检查失败")
            
            time.sleep(10)  # 每10秒检查一次
    
    def restart_service(self, name):
        """重启服务"""
        if name in self.processes:
            info = self.processes[name]
            
            # 终止旧进程
            try:
                info['process'].terminate()
                info['process'].wait(timeout=5)
            except:
                info['process'].kill()
            
            # 启动新进程
            self.start_service(name, info['script'], info['port'])
    
    def stop_all_services(self):
        """停止所有服务"""
        print("\n🛑 正在停止所有服务...")
        self.running = False
        
        for name, info in self.processes.items():
            try:
                print(f"⏹️ 停止{name}服务...")
                info['process'].terminate()
                info['process'].wait(timeout=5)
                print(f"✅ {name}服务已停止")
            except:
                print(f"🔨 强制终止{name}服务...")
                info['process'].kill()
    
    def show_status(self):
        """显示服务状态"""
        print("\n📊 服务状态:")
        print("="*60)
        
        for name, info in self.processes.items():
            process = info['process']
            uptime = datetime.now() - info['start_time']
            
            if process.poll() is None:
                status = "🟢 运行中"
                health = "✅ 健康" if self.check_service_health(name, info['port']) else "❌ 异常"
            else:
                status = "🔴 已停止"
                health = "❌ 不可用"
            
            print(f"{name:15} | {status:8} | {health:8} | 端口:{info['port']:4} | 运行时间:{str(uptime).split('.')[0]}")

def check_dependencies():
    """检查依赖"""
    print("🔍 检查系统依赖...")
    
    # 修改包名映射
    required_packages = {
        'flask': 'flask',
        'flask_cors': 'flask_cors', 
        'pymysql': 'pymysql',
        'requests': 'requests',
        'ultralytics': 'ultralytics',
        'opencv-python': 'cv2',  # 修复这里
        'torch': 'torch',
        'numpy': 'numpy'
    }
    
    missing_packages = []
    
    for display_name, import_name in required_packages.items():
        try:
            __import__(import_name)
            print(f"✅ {display_name}")
        except ImportError:
            print(f"❌ {display_name} (缺失)")
            missing_packages.append(display_name)
    
    if missing_packages:
        print(f"\n⚠️ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖检查通过")
    return True

def check_files():
    """检查必要文件"""
    print("\n🔍 检查系统文件...")
    
    required_files = [
        'frontend_api_server.py',
        'core_detection_server.py',
        'config/end-back.env'
    ]
    
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} (缺失)")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n⚠️ 缺少文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 所有文件检查通过")
    return True

def show_system_info():
    """显示系统信息"""
    print("🎯 基于Yolov8与ByteTrack的高速公路智慧监控平台")
    print("="*60)
    print(f"📅 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 Python版本: {sys.version.split()[0]}")
    print(f"📁 工作目录: {os.getcwd()}")
    
    # 检查CUDA
    try:
        import torch
        cuda_available = torch.cuda.is_available()
        gpu_count = torch.cuda.device_count() if cuda_available else 0
        print(f"🎮 CUDA支持: {'是' if cuda_available else '否'}")
        if cuda_available:
            print(f"🎮 GPU数量: {gpu_count}")
    except:
        print(f"🎮 CUDA支持: 未知")
    
    print("="*60)

def main():
    """主函数"""
    show_system_info()
    
    # 检查依赖和文件
    if not check_dependencies() or not check_files():
        print("\n❌ 系统检查失败，请解决上述问题后重试")
        return
    
    # 创建系统管理器
    manager = SystemManager()
    
    # 注册信号处理
    def signal_handler(signum, frame):
        print(f"\n收到信号 {signum}，正在关闭系统...")
        manager.stop_all_services()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 启动服务
        print("\n🚀 启动系统服务...")
        
        # 1. 启动基础API服务
        if not manager.start_service("基础API", "frontend_api_server.py", 5500):
            print("❌ 基础API服务启动失败")
            return
        
        time.sleep(3)  # 等待服务启动
        
        # 2. 启动检测服务 (包含WebSocket)
        if not manager.start_service("检测服务", "core_detection_server.py", 5501):
            print("❌ 检测服务启动失败")
            return
        
        time.sleep(3)  # 等待服务启动
        
        # 启动监控线程
        monitor_thread = threading.Thread(target=manager.monitor_services, daemon=True)
        monitor_thread.start()
        
        print("\n🎉 系统启动完成!")
        print("="*60)
        print("📋 服务地址:")
        print("   🌐 基础API: http://127.0.0.1:5500")
        print("   🎯 检测API: http://127.0.0.1:5501")
        print("   🌐 WebSocket: ws://127.0.0.1:5502")
        print("   📚 API文档: http://127.0.0.1:5500/api/v1/docs")

        print("\n🚀 新增功能:")
        print("   🚨 违规检测: 违停、逆行检测")
        print("   🚗 事故检测: 碰撞、急停、拥堵检测")
        print("   📊 交通分析: 流量统计、速度分析")
        print("   🌐 实时推送: WebSocket实时数据推送")
        
        print("\n🔑 测试账号:")
        print("   admin / 123456 (超级管理员)")
        print("   operator / operator (操作员)")
        print("   viewer / hello (观察员)")
        
        print("\n⌨️ 控制命令:")
        print("   输入 'status' 查看服务状态")
        print("   输入 'restart <服务名>' 重启服务")
        print("   输入 'quit' 或 Ctrl+C 退出系统")
        print("="*60)
        
        # 交互式命令行
        while manager.running:
            try:
                command = input("\n> ").strip().lower()
                
                if command == 'status':
                    manager.show_status()
                elif command.startswith('restart '):
                    service_name = command.split(' ', 1)[1]
                    if service_name in manager.processes:
                        manager.restart_service(service_name)
                    else:
                        print(f"❌ 未找到服务: {service_name}")
                elif command in ['quit', 'exit', 'q']:
                    break
                elif command == 'help':
                    print("可用命令: status, restart <服务名>, quit")
                elif command:
                    print("❌ 未知命令，输入 'help' 查看帮助")
                    
            except EOFError:
                break
            except KeyboardInterrupt:
                break
    
    except Exception as e:
        print(f"❌ 系统启动异常: {e}")
    
    finally:
        manager.stop_all_services()
        print("\n👋 系统已关闭")

if __name__ == "__main__":
    main()
