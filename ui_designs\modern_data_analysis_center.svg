<?xml version="1.0" encoding="UTF-8"?>
<svg width="1440" height="1600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .header { font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: 600; fill: #1a202c; }
      .section-title { font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif; font-size: 20px; font-weight: 600; fill: #2d3748; }
      .subsection-title { font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif; font-size: 16px; font-weight: 500; fill: #4a5568; }
      .label { font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif; font-size: 14px; fill: #718096; }
      .value { font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif; font-size: 14px; fill: #2d3748; }
      .chart-text { font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif; font-size: 12px; fill: #a0aec0; }
      .table-header { font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif; font-size: 14px; font-weight: 600; fill: #2d3748; }
      .table-cell { font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif; font-size: 13px; fill: #4a5568; }
      .metric-value { font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: 700; fill: #2b6cb0; }
      .metric-label { font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif; font-size: 12px; fill: #718096; }
      .card { fill: #ffffff; stroke: none; rx: 12; filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.07)); }
      .filter-card { fill: #f7fafc; stroke: #e2e8f0; stroke-width: 1; rx: 8; }
      .primary-btn { fill: #3182ce; stroke: none; rx: 6; }
      .secondary-btn { fill: #e2e8f0; stroke: none; rx: 6; }
      .btn-text { font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif; font-size: 14px; font-weight: 500; }
      .btn-text-primary { fill: #ffffff; }
      .btn-text-secondary { fill: #4a5568; }
      .input-field { fill: #ffffff; stroke: #e2e8f0; stroke-width: 1; rx: 6; }
      .dropdown { fill: #ffffff; stroke: #e2e8f0; stroke-width: 1; rx: 6; }
      .icon { font-size: 20px; }
    </style>
    <linearGradient id="chartGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#3182ce;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#3182ce;stop-opacity:0.1" />
    </linearGradient>
    <linearGradient id="heatmapLow" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#68d391;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#68d391;stop-opacity:0.7" />
    </linearGradient>
    <linearGradient id="heatmapMedium" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#f6ad55;stop-opacity:0.5" />
      <stop offset="100%" style="stop-color:#f6ad55;stop-opacity:0.8" />
    </linearGradient>
    <linearGradient id="heatmapHigh" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#fc8181;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#fc8181;stop-opacity:0.9" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="1440" height="1600" fill="#f7fafc"/>
  
  <!-- Header Section -->
  <rect class="card" x="24" y="24" width="1392" height="80"/>
  <text class="header" x="48" y="55">📊 数据分析中心</text>
  <text class="label" x="48" y="78">智能交通监控数据分析与可视化平台</text>
  
  <!-- Filter Section -->
  <rect class="filter-card" x="24" y="124" width="1392" height="140"/>
  <text class="section-title" x="48" y="155">🔍 筛选条件</text>
  
  <!-- First Row Filters -->
  <text class="label" x="48" y="185">时间范围</text>
  <rect class="input-field" x="48" y="195" width="140" height="36"/>
  <text class="value" x="58" y="217">2023-12-01</text>
  
  <text class="label" x="208" y="185">至</text>
  <rect class="input-field" x="208" y="195" width="140" height="36"/>
  <text class="value" x="218" y="217">2023-12-31</text>
  
  <text class="label" x="368" y="185">监控点</text>
  <rect class="dropdown" x="368" y="195" width="140" height="36"/>
  <text class="value" x="378" y="217">全部监控点 ▼</text>
  
  <text class="label" x="528" y="185">数据类型</text>
  <rect class="dropdown" x="528" y="195" width="140" height="36"/>
  <text class="value" x="538" y="217">交通流量 ▼</text>
  
  <!-- Action Buttons -->
  <rect class="primary-btn" x="688" y="195" width="80" height="36"/>
  <text class="btn-text btn-text-primary" x="718" y="217">🔍 查询</text>
  
  <rect class="secondary-btn" x="788" y="195" width="80" height="36"/>
  <text class="btn-text btn-text-secondary" x="818" y="217">📤 导出</text>
  
  <!-- Key Metrics Cards -->
  <rect class="card" x="24" y="284" width="330" height="120"/>
  <text class="subsection-title" x="48" y="315">今日总流量</text>
  <text class="metric-value" x="48" y="350">45,672</text>
  <text class="metric-label" x="48" y="370">车辆 (+12.5% 较昨日)</text>
  <rect fill="#68d391" x="48" y="380" width="60" height="4" rx="2"/>
  
  <rect class="card" x="374" y="284" width="330" height="120"/>
  <text class="subsection-title" x="398" y="315">平均车速</text>
  <text class="metric-value" x="398" y="350">78.5</text>
  <text class="metric-label" x="398" y="370">km/h (-2.1% 较昨日)</text>
  <rect fill="#f6ad55" x="398" y="380" width="45" height="4" rx="2"/>
  
  <rect class="card" x="724" y="284" width="330" height="120"/>
  <text class="subsection-title" x="748" y="315">事故警报</text>
  <text class="metric-value" x="748" y="350">23</text>
  <text class="metric-label" x="748" y="370">次 (+8.7% 较昨日)</text>
  <rect fill="#fc8181" x="748" y="380" width="35" height="4" rx="2"/>
  
  <rect class="card" x="1074" y="284" width="342" height="120"/>
  <text class="subsection-title" x="1098" y="315">拥堵指数</text>
  <text class="metric-value" x="1098" y="350">0.67</text>
  <text class="metric-label" x="1098" y="370">中等拥堵 (-5.2% 较昨日)</text>
  <rect fill="#f6ad55" x="1098" y="380" width="50" height="4" rx="2"/>
  
  <!-- Traffic Flow Chart -->
  <rect class="card" x="24" y="424" width="1392" height="400"/>
  <text class="section-title" x="48" y="455">📈 交通流量趋势分析</text>
  <text class="subsection-title" x="48" y="480">日均交通流量变化 (最近30天)</text>
  
  <!-- Chart Area -->
  <rect fill="#f7fafc" x="48" y="500" width="1320" height="280" rx="8"/>
  
  <!-- Y-axis -->
  <line x1="80" y1="520" x2="80" y2="760" stroke="#e2e8f0" stroke-width="2"/>
  <text class="chart-text" x="60" y="530">2000</text>
  <text class="chart-text" x="60" y="570">1800</text>
  <text class="chart-text" x="60" y="610">1600</text>
  <text class="chart-text" x="60" y="650">1400</text>
  <text class="chart-text" x="60" y="690">1200</text>
  <text class="chart-text" x="60" y="730">1000</text>
  <text class="chart-text" x="60" y="770">800</text>
  
  <!-- X-axis -->
  <line x1="80" y1="760" x2="1340" y2="760" stroke="#e2e8f0" stroke-width="2"/>
  
  <!-- Grid lines -->
  <line x1="80" y1="530" x2="1340" y2="530" stroke="#f1f5f9" stroke-width="1"/>
  <line x1="80" y1="570" x2="1340" y2="570" stroke="#f1f5f9" stroke-width="1"/>
  <line x1="80" y1="610" x2="1340" y2="610" stroke="#f1f5f9" stroke-width="1"/>
  <line x1="80" y1="650" x2="1340" y2="650" stroke="#f1f5f9" stroke-width="1"/>
  <line x1="80" y1="690" x2="1340" y2="690" stroke="#f1f5f9" stroke-width="1"/>
  <line x1="80" y1="730" x2="1340" y2="730" stroke="#f1f5f9" stroke-width="1"/>
  
  <!-- Chart area fill -->
  <polygon points="120,760 160,720 200,680 240,640 280,600 320,570 360,550 400,540 440,545 480,560 520,580 560,600 600,620 640,640 680,660 720,680 760,700 800,720 840,740 880,750 920,745 960,735 1000,720 1040,700 1080,680 1120,660 1160,640 1200,620 1240,600 1280,580 1320,560 1320,760" 
            fill="url(#chartGradient)"/>
  
  <!-- Chart line -->
  <polyline points="120,760 160,720 200,680 240,640 280,600 320,570 360,550 400,540 440,545 480,560 520,580 560,600 600,620 640,640 680,660 720,680 760,700 800,720 840,740 880,750 920,745 960,735 1000,720 1040,700 1080,680 1120,660 1160,640 1200,620 1240,600 1280,580 1320,560" 
            stroke="#3182ce" stroke-width="3" fill="none"/>
  
  <!-- Data points -->
  <circle cx="120" cy="760" r="5" fill="#3182ce"/>
  <circle cx="200" cy="680" r="5" fill="#3182ce"/>
  <circle cx="280" cy="600" r="5" fill="#3182ce"/>
  <circle cx="360" cy="550" r="5" fill="#3182ce"/>
  <circle cx="440" cy="545" r="5" fill="#3182ce"/>
  <circle cx="520" cy="580" r="5" fill="#3182ce"/>
  <circle cx="600" cy="620" r="5" fill="#3182ce"/>
  <circle cx="680" cy="660" r="5" fill="#3182ce"/>
  <circle cx="760" cy="700" r="5" fill="#3182ce"/>
  <circle cx="840" cy="740" r="5" fill="#3182ce"/>
  <circle cx="920" cy="745" r="5" fill="#3182ce"/>
  <circle cx="1000" cy="720" r="5" fill="#3182ce"/>
  <circle cx="1080" cy="680" r="5" fill="#3182ce"/>
  <circle cx="1160" cy="640" r="5" fill="#3182ce"/>
  <circle cx="1240" cy="600" r="5" fill="#3182ce"/>
  <circle cx="1320" cy="560" r="5" fill="#3182ce"/>
  
  <!-- X-axis labels -->
  <text class="chart-text" x="115" y="780">1</text>
  <text class="chart-text" x="235" y="780">5</text>
  <text class="chart-text" x="355" y="780">10</text>
  <text class="chart-text" x="475" y="780">15</text>
  <text class="chart-text" x="595" y="780">20</text>
  <text class="chart-text" x="715" y="780">25</text>
  <text class="chart-text" x="835" y="780">30</text>
  <text class="chart-text" x="1320" y="780">天</text>
  
  <!-- Y-axis title -->
  <text class="chart-text" x="30" y="640" transform="rotate(-90 30 640)">车辆数</text>
  
  <!-- Hourly Statistics Table -->
  <rect class="card" x="24" y="844" width="690" height="320"/>
  <text class="section-title" x="48" y="875">🕐 小时分布统计</text>
  
  <!-- Table Container -->
  <rect fill="#f7fafc" x="48" y="895" width="642" height="245" rx="8"/>
  
  <!-- Table Headers -->
  <rect fill="#edf2f7" x="48" y="895" width="642" height="40" rx="8 8 0 0"/>
  <text class="table-header" x="68" y="920">时段</text>
  <text class="table-header" x="188" y="920">平均车流量</text>
  <text class="table-header" x="318" y="920">警报次数</text>
  <text class="table-header" x="428" y="920">事故次数</text>
  <text class="table-header" x="538" y="920">拥堵指数</text>
  
  <!-- Table Rows -->
  <rect fill="#ffffff" x="48" y="935" width="642" height="35"/>
  <text class="table-cell" x="68" y="957">06:00-08:00</text>
  <text class="table-cell" x="218" y="957">1,850</text>
  <text class="table-cell" x="348" y="957">25</text>
  <text class="table-cell" x="458" y="957">3</text>
  <text class="table-cell" x="568" y="957">0.75</text>
  
  <rect fill="#f7fafc" x="48" y="970" width="642" height="35"/>
  <text class="table-cell" x="68" y="992">08:00-10:00</text>
  <text class="table-cell" x="218" y="992">2,100</text>
  <text class="table-cell" x="348" y="992">35</text>
  <text class="table-cell" x="458" y="992">5</text>
  <text class="table-cell" x="568" y="992">0.85</text>
  
  <rect fill="#ffffff" x="48" y="1005" width="642" height="35"/>
  <text class="table-cell" x="68" y="1027">10:00-12:00</text>
  <text class="table-cell" x="218" y="1027">1,650</text>
  <text class="table-cell" x="348" y="1027">18</text>
  <text class="table-cell" x="458" y="1027">2</text>
  <text class="table-cell" x="568" y="1027">0.45</text>
  
  <rect fill="#f7fafc" x="48" y="1040" width="642" height="35"/>
  <text class="table-cell" x="68" y="1062">12:00-14:00</text>
  <text class="table-cell" x="218" y="1062">1,800</text>
  <text class="table-cell" x="348" y="1062">22</text>
  <text class="table-cell" x="458" y="1062">3</text>
  <text class="table-cell" x="568" y="1062">0.55</text>
  
  <rect fill="#ffffff" x="48" y="1075" width="642" height="35"/>
  <text class="table-cell" x="68" y="1097">14:00-16:00</text>
  <text class="table-cell" x="218" y="1097">1,950</text>
  <text class="table-cell" x="348" y="1097">28</text>
  <text class="table-cell" x="458" y="1097">4</text>
  <text class="table-cell" x="568" y="1097">0.68</text>
  
  <rect fill="#f7fafc" x="48" y="1110" width="642" height="30" rx="0 0 8 8"/>
  <text class="table-cell" x="68" y="1130">16:00-18:00</text>
  <text class="table-cell" x="218" y="1130">2,200</text>
  <text class="table-cell" x="348" y="1130">40</text>
  <text class="table-cell" x="458" y="1130">6</text>
  <text class="table-cell" x="568" y="1130">0.90</text>
  
  <!-- Heatmap Section -->
  <rect class="card" x="734" y="844" width="682" height="320"/>
  <text class="section-title" x="758" y="875">🗺️ 路段热力分析</text>
  <text class="subsection-title" x="758" y="900">各路段交通密度分布</text>
  
  <!-- Heatmap Container -->
  <rect fill="#f7fafc" x="758" y="920" width="634" height="220" rx="8"/>
  
  <!-- Heatmap Bars -->
  <text class="chart-text" x="778" y="945">K100+000</text>
  <rect x="878" y="930" width="480" height="20" fill="url(#heatmapHigh)" rx="10"/>
  <text class="chart-text" x="1370" y="945">高</text>
  
  <text class="chart-text" x="778" y="975">K100+200</text>
  <rect x="878" y="960" width="390" height="20" fill="url(#heatmapMedium)" rx="10"/>
  <text class="chart-text" x="1280" y="975">中</text>
  
  <text class="chart-text" x="778" y="1005">K100+400</text>
  <rect x="878" y="990" width="330" height="20" fill="url(#heatmapMedium)" rx="10"/>
  <text class="chart-text" x="1220" y="1005">中</text>
  
  <text class="chart-text" x="778" y="1035">K100+600</text>
  <rect x="878" y="1020" width="450" height="20" fill="url(#heatmapHigh)" rx="10"/>
  <text class="chart-text" x="1340" y="1035">高</text>
  
  <text class="chart-text" x="778" y="1065">K100+800</text>
  <rect x="878" y="1050" width="480" height="20" fill="url(#heatmapHigh)" rx="10"/>
  <text class="chart-text" x="1370" y="1065">高</text>
  
  <text class="chart-text" x="778" y="1095">K101+000</text>
  <rect x="878" y="1080" width="270" height="20" fill="url(#heatmapLow)" rx="10"/>
  <text class="chart-text" x="1160" y="1095">低</text>
  
  <!-- Heatmap Legend -->
  <text class="chart-text" x="878" y="1125">交通密度:</text>
  <rect x="950" y="1115" width="20" height="8" fill="url(#heatmapLow)" rx="4"/>
  <text class="chart-text" x="980" y="1125">低</text>
  <rect x="1010" y="1115" width="20" height="8" fill="url(#heatmapMedium)" rx="4"/>
  <text class="chart-text" x="1040" y="1125">中</text>
  <rect x="1070" y="1115" width="20" height="8" fill="url(#heatmapHigh)" rx="4"/>
  <text class="chart-text" x="1100" y="1125">高</text>
  
  <!-- Additional Analysis Cards -->
  <rect class="card" x="24" y="1184" width="460" height="140"/>
  <text class="section-title" x="48" y="1215">📊 违章统计</text>
  <text class="metric-value" x="48" y="1250">156</text>
  <text class="metric-label" x="48" y="1270">今日违章检测</text>
  <text class="table-cell" x="48" y="1290">超速: 89次 | 违停: 34次 | 其他: 33次</text>
  
  <rect class="card" x="504" y="1184" width="460" height="140"/>
  <text class="section-title" x="528" y="1215">⚡ 系统状态</text>
  <text class="metric-value" x="528" y="1250">99.8%</text>
  <text class="metric-label" x="528" y="1270">系统可用性</text>
  <text class="table-cell" x="528" y="1290">监控点: 24/24在线 | 延迟: 45ms</text>
  
  <rect class="card" x="984" y="1184" width="432" height="140"/>
  <text class="section-title" x="1008" y="1215">🎯 AI识别精度</text>
  <text class="metric-value" x="1008" y="1250">96.7%</text>
  <text class="metric-label" x="1008" y="1270">目标检测准确率</text>
  <text class="table-cell" x="1008" y="1290">车辆: 98.2% | 行人: 94.5% | 异常: 95.8%</text>
  
</svg>