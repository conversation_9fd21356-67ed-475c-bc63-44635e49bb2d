# 基于Yolov8与ByteTrack的高速公路智慧监控平台 - 完整API接口文档

## 📋 基本信息
- **基础URL**: `http://localhost:5500/api/v1`
- **版本**: v1.0.0
- **认证方式**: <PERSON><PERSON> (JWT)
- **数据格式**: JSON
- **WebSocket**: `ws://localhost:5500/socket.io`

## 🏗️ 系统架构
本系统基于以下核心技术栈：
- **YOLOv8**: 高精度目标检测算法
- **ByteTrack**: 多目标追踪算法  
- **碰撞检测**: 实时事故预警系统
- **多路视频流**: 支持RTSP/本地视频处理
- **实时分析**: WebSocket实时数据推送
- **Flask后端**: RESTful API服务
- **SQLite数据库**: 数据持久化存储

## 🔐 认证接口 (Authentication)

### 1.1 用户登录
**接口**: `POST /auth/login`

**请求参数**:
```json
{
  "username": "admin",
  "password": "123456"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "user": {
      "id": 1,
      "username": "admin",
      "role": "admin",
      "created_at": "2024-01-01T00:00:00"
    }
  }
}
```

### 1.2 用户注册
**接口**: `POST /auth/register`

**请求参数**:
```json
{
  "username": "newuser",
  "password": "password123",
  "email": "<EMAIL>",
  "role": "user"
}
```

### 1.3 获取用户信息
**接口**: `GET /auth/profile`
**认证**: Bearer Token

### 1.4 用户登出
**接口**: `POST /auth/logout`
**认证**: Bearer Token

## 📹 监控点管理 (Monitor Management)

### 2.1 获取监控点列表
**接口**: `GET /monitor/list`
**认证**: Bearer Token

**查询参数**:
- `page`: 页码 (默认: 1)
- `size`: 每页数量 (默认: 20)
- `location`: 位置筛选
- `status`: 状态筛选 (online/offline)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "monitors": [
      {
        "id": 1,
        "name": "监控点001",
        "location": "杭州-千岛湖高速K100+500",
        "road_section": "K100+500",
        "rtsp_url": "rtsp://*************:554/stream",
        "status": "online",
        "is_alarm": true,
        "threshold": 50,
        "created_at": "2024-01-01T00:00:00"
      }
    ],
    "total": 100,
    "page": 1,
    "size": 20
  }
}
```

### 2.2 创建监控点
**接口**: `POST /monitor`
**认证**: Bearer Token

**请求参数**:
```json
{
  "name": "新监控点",
  "location": "杭州-千岛湖高速K200+300",
  "road_section": "K200+300",
  "rtsp_url": "rtsp://*************:554/stream",
  "threshold": 60,
  "description": "高速公路主要路段监控"
}
```

### 2.3 更新监控点
**接口**: `PUT /monitor/{monitor_id}`
**认证**: Bearer Token

### 2.4 删除监控点
**接口**: `DELETE /monitor/{monitor_id}`
**认证**: Bearer Token

### 2.5 测试监控点连接
**接口**: `POST /monitor/{monitor_id}/test-connection`
**认证**: Bearer Token

## 🎯 YOLO检测模块 (Detection)

### 3.1 图像检测
**接口**: `POST /detection/image`
**认证**: Bearer Token
**Content-Type**: `multipart/form-data`

**请求参数**:
- `file`: 图像文件
- `conf_threshold`: 置信度阈值 (默认: 0.4)
- `iou_threshold`: IOU阈值 (默认: 0.5)
- `show_labels`: 是否显示标签 (默认: true)

**响应示例**:
```json
{
  "success": true,
  "message": "图像检测成功",
  "data": {
    "original_image": "/static/images/original/20241201_123456.jpg",
    "result_image": "/static/images/detection/20241201_123456.jpg",
    "detections": [
      {
        "id": 0,
        "bbox": [100, 100, 200, 200],
        "confidence": 0.85,
        "class_id": 2,
        "class_name": "car"
      }
    ],
    "statistics": {
      "total_objects": 5,
      "class_counts": {"car": 3, "truck": 2},
      "avg_confidence": 0.82
    }
  }
}
```

### 3.2 Base64图像检测
**接口**: `POST /detection/base64`
**认证**: Bearer Token

**请求参数**:
```json
{
  "image": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
  "conf_threshold": 0.5,
  "iou_threshold": 0.4,
  "show_labels": true
}
```

### 3.3 视频检测
**接口**: `POST /detection/video`
**认证**: Bearer Token
**Content-Type**: `multipart/form-data`

**请求参数**:
- `file`: 视频文件
- `conf_threshold`: 置信度阈值
- `iou_threshold`: IOU阈值
- `show_labels`: 是否显示标签
- `save_result`: 是否保存结果视频

**响应示例**:
```json
{
  "success": true,
  "message": "视频检测任务已启动",
  "data": {
    "task_id": "uuid-task-id",
    "status": "started"
  }
}
```

### 3.4 RTSP流检测
**接口**: `POST /detection/rtsp`
**认证**: Bearer Token

**请求参数**:
```json
{
  "rtsp_url": "rtsp://*************:554/stream",
  "conf_threshold": 0.5,
  "iou_threshold": 0.4,
  "show_labels": true,
  "monitor_id": 1
}
```

### 3.5 停止RTSP检测
**接口**: `POST /detection/rtsp/{task_id}/stop`
**认证**: Bearer Token

### 3.6 获取检测任务列表
**接口**: `GET /detection/tasks`
**认证**: Bearer Token

### 3.7 获取任务状态
**接口**: `GET /detection/task/{task_id}/status`
**认证**: Bearer Token

### 3.8 获取可用模型
**接口**: `GET /detection/models`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "models": [
      {
        "name": "car.pt",
        "path": "./models/car.pt",
        "size": 14567890,
        "type": "pt",
        "is_current": true
      }
    ]
  }
}
```

### 3.9 切换检测模型
**接口**: `POST /detection/model`
**认证**: Bearer Token

**请求参数**:
```json
{
  "model_name": "yolov8n.pt"
}
```

## 🚗 多目标追踪模块 (Tracking)

### 4.1 获取可用追踪算法
**接口**: `GET /tracking/algorithms`
**认证**: Bearer Token

**响应示例**:
```json
{
  "success": true,
  "data": {
    "algorithms": [
      {
        "name": "bytetrack",
        "display_name": "ByteTrack",
        "description": "高性能多目标追踪算法",
        "parameters": {
          "track_thresh": {"type": "float", "default": 0.5, "range": [0.1, 0.9]},
          "track_buffer": {"type": "int", "default": 30, "range": [10, 100]},
          "match_thresh": {"type": "float", "default": 0.8, "range": [0.1, 0.9]}
        }
      }
    ]
  }
}
```

### 4.2 启动追踪
**接口**: `POST /tracking/start`
**认证**: Bearer Token

**请求参数**:
```json
{
  "monitor_id": 1,
  "algorithm": "bytetrack",
  "config_params": {
    "track_thresh": 0.5,
    "track_buffer": 30,
    "match_thresh": 0.8
  }
}
```

### 4.3 停止追踪
**接口**: `POST /tracking/stop`
**认证**: Bearer Token

**请求参数**:
```json
{
  "monitor_id": 1
}
```

### 4.4 切换追踪算法
**接口**: `POST /tracking/switch-algorithm`
**认证**: Bearer Token

**请求参数**:
```json
{
  "monitor_id": 1,
  "algorithm_name": "deepsort",
  "config_params": {
    "max_dist": 0.2,
    "min_confidence": 0.3
  }
}
```

### 4.5 获取追踪状态
**接口**: `GET /tracking/status/{monitor_id}`
**认证**: Bearer Token

### 4.6 获取所有追踪状态
**接口**: `GET /tracking/status/all`
**认证**: Bearer Token

### 4.7 获取活动目标
**接口**: `GET /tracking/targets/active`
**认证**: Bearer Token

**查询参数**:
- `target_type`: 目标类型筛选
- `min_confidence`: 最小置信度

### 4.8 获取目标详情
**接口**: `GET /tracking/target/{target_id}`
**认证**: Bearer Token

## 🚨 事故检测模块 (Accident Detection)

### 5.1 更新事故检测配置
**接口**: `POST /accident/config`
**认证**: Bearer Token

**请求参数**:
```json
{
  "monitor_id": "1",
  "collision_detection": {
    "enabled": true,
    "sensitivity": 0.7,
    "min_speed_diff": 20
  },
  "sudden_stop_detection": {
    "enabled": true,
    "deceleration_threshold": -5.0,
    "time_window": 2.0
  },
  "congestion_detection": {
    "enabled": true,
    "density_threshold": 0.8,
    "speed_threshold": 10.0
  }
}
```

### 5.2 启动事故检测
**接口**: `POST /accident/start`
**认证**: Bearer Token

**请求参数**:
```json
{
  "monitor_id": "1"
}
```

### 5.3 停止事故检测
**接口**: `POST /accident/stop`
**认证**: Bearer Token

**请求参数**:
```json
{
  "monitor_id": "1"
}
```

### 5.4 获取检测状态
**接口**: `GET /accident/status/{monitor_id}`
**认证**: Bearer Token

### 5.5 获取事故记录
**接口**: `GET /accident/records`
**认证**: Bearer Token

**查询参数**:
- `monitor_id`: 监控点ID
- `accident_type`: 事故类型
- `start_date`: 开始日期
- `end_date`: 结束日期
- `page`: 页码
- `page_size`: 每页数量

**响应示例**:
```json
{
  "success": true,
  "data": {
    "records": [
      {
        "record_id": 1,
        "monitor_id": "1",
        "accident_type": "collision",
        "severity": "high",
        "location": {"x": 100, "y": 200},
        "description": "检测到车辆碰撞事故",
        "detection_time": "2024-01-01T12:00:00",
        "confidence": 0.95,
        "status": "detected"
      }
    ],
    "total": 50,
    "page": 1,
    "page_size": 20
  }
}
```

### 5.6 获取事故统计
**接口**: `GET /accident/statistics`
**认证**: Bearer Token

**查询参数**:
- `monitor_id`: 监控点ID
- `start_date`: 开始日期
- `end_date`: 结束日期
- `accident_type`: 事故类型

### 5.7 更新预警配置
**接口**: `POST /accident/alert-config`
**认证**: Bearer Token

**请求参数**:
```json
{
  "monitor_id": "1",
  "enabled": true,
  "severity_threshold": "medium",
  "alert_methods": ["email", "webhook"],
  "recipients": ["<EMAIL>"],
  "webhook_url": "https://webhook.example.com/alerts"
}
```

## 📊 数据分析模块 (Analysis)

### 6.1 获取概览统计
**接口**: `GET /analysis/statistics/overview`
**认证**: Bearer Token

**查询参数**:
- `days`: 统计天数 (默认: 7)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "total_monitors": 50,
    "active_monitors": 45,
    "total_detections": 12500,
    "today_detections": 350,
    "total_accidents": 25,
    "today_accidents": 2,
    "avg_vehicles_per_hour": 125.5,
    "peak_hour_vehicles": 280
  }
}
```

### 6.2 获取交通流量统计
**接口**: `GET /analysis/statistics/traffic-flow`
**认证**: Bearer Token

**查询参数**:
- `monitor_id`: 监控点ID
- `days`: 统计天数
- `group_by`: 分组方式 (hour/day/week)

### 6.3 获取热力图数据
**接口**: `GET /analysis/heatmap`
**认证**: Bearer Token

**查询参数**:
- `monitor_id`: 监控点ID
- `start_date`: 开始日期
- `end_date`: 结束日期

### 6.4 获取警报列表
**接口**: `GET /analysis/alarms`
**认证**: Bearer Token

**查询参数**:
- `page`: 页码
- `size`: 每页数量
- `location`: 位置筛选
- `start_date`: 开始日期
- `end_date`: 结束日期
- `alarm_type`: 警报类型

### 6.5 导出数据
**接口**: `POST /analysis/export/alarms`
**认证**: Bearer Token

**请求参数**:
```json
{
  "format": "excel",
  "start_date": "2024-01-01",
  "end_date": "2024-01-31",
  "location": "杭州-千岛湖高速",
  "alarm_type": "accident"
}
```

## 🔄 实时监控模块 (Realtime)

### 7.1 获取实时状态
**接口**: `GET /realtime/status`
**认证**: Bearer Token

### 7.2 获取活动监控点
**接口**: `GET /realtime/monitors/active`
**认证**: Bearer Token

### 7.3 获取实时统计
**接口**: `GET /realtime/statistics/live`
**认证**: Bearer Token

### 7.4 获取监控点实时数据
**接口**: `GET /realtime/monitor/{monitor_id}/data`
**认证**: Bearer Token

## ⚙️ 系统管理模块 (System)

### 8.1 获取系统信息
**接口**: `GET /system/info`
**认证**: Bearer Token

**响应示例**:
```json
{
  "success": true,
  "data": {
    "system_name": "基于Yolov8与ByteTrack的高速公路智慧监控平台",
    "version": "1.0.0",
    "python_version": "3.12.7",
    "yolo_version": "8.0.0",
    "database_version": "SQLite 3.x",
    "uptime": "5 days, 12:30:45"
  }
}
```

### 8.2 获取性能指标
**接口**: `GET /system/performance`
**认证**: Bearer Token

### 8.3 健康检查
**接口**: `GET /system/health-check`

**响应示例**:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00",
  "database": {"status": "healthy", "response_time": 0.05},
  "system": {
    "cpu_percent": 45.2,
    "memory_percent": 68.5,
    "disk_percent": 35.8
  }
}
```

### 8.4 获取系统日志
**接口**: `GET /system/logs`
**认证**: Bearer Token

**查询参数**:
- `level`: 日志级别 (INFO/WARNING/ERROR)
- `page`: 页码
- `size`: 每页数量

## 🌐 WebSocket实时通信

### 连接WebSocket
```javascript
const socket = io('ws://localhost:5500', {
  auth: {
    token: 'your_jwt_token'
  }
});
```

### 事件监听
```javascript
// 连接成功
socket.on('connect', () => {
  console.log('WebSocket连接成功');
});

// 加入监控点房间
socket.emit('join_monitor', {monitor_id: 1});

// 监听检测结果
socket.on('detection_result', (data) => {
  console.log('检测结果:', data);
});

// 监听事故警报
socket.on('accident_alert', (data) => {
  console.log('事故警报:', data);
});

// 监听追踪数据
socket.on('tracking_data', (data) => {
  console.log('追踪数据:', data);
});
```

## 📝 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 🔧 前端开发建议

### 技术栈推荐
- **框架**: Vue 3 + TypeScript
- **UI库**: Arco Design Vue
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **HTTP客户端**: Axios
- **WebSocket**: Socket.IO Client
- **图表**: ECharts
- **视频播放**: Video.js

### 开发步骤
1. **项目初始化**: 使用Vue CLI创建项目
2. **API封装**: 创建统一的API请求封装
3. **认证模块**: 实现登录、权限管理
4. **监控点管理**: CRUD操作界面
5. **实时监控**: 视频流显示、检测结果展示
6. **数据分析**: 图表展示、统计报表
7. **系统管理**: 配置管理、日志查看
