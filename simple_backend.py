#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单可靠的后端服务 - 完全重新实现
"""

import os
import sys
import time
import jwt
import pymysql
from datetime import datetime, timedelta
from flask import Flask, request, jsonify, session
from flask_cors import CORS
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(override=True, dotenv_path='config/end-back.env')

# 创建Flask应用
app = Flask(__name__)
app.config['SECRET_KEY'] = 'yolo-highway-monitoring-system-2025'
app.config['JSON_AS_ASCII'] = False

# 启用CORS
CORS(app, supports_credentials=True, origins="*")

def get_db_config():
    """获取数据库配置"""
    return {
        'host': '127.0.0.1',
        'port': 3306,
        'user': 'root',
        'password': '123456',
        'database': 'yolo',
        'charset': 'utf8mb4',
        'autocommit': True,
        'cursorclass': pymysql.cursors.DictCursor
    }

def success_response(data=None, message="操作成功", code=200):
    """成功响应"""
    return jsonify({
        'code': code,
        'message': message,
        'data': data,
        'success': True,
        'timestamp': int(time.time() * 1000)
    })

def error_response(message="操作失败", code=400, data=None):
    """错误响应"""
    return jsonify({
        'code': code,
        'message': message,
        'data': data,
        'success': False,
        'timestamp': int(time.time() * 1000)
    })

def test_database():
    """测试数据库连接"""
    try:
        config = get_db_config()
        connection = pymysql.connect(**config)
        with connection.cursor() as cursor:
            cursor.execute("SELECT COUNT(*) as count FROM user")
            result = cursor.fetchone()
        connection.close()
        return True, result['count']
    except Exception as e:
        return False, str(e)

@app.route('/')
def index():
    """首页"""
    db_ok, db_info = test_database()
    return jsonify({
        'message': '高速公路智能监控系统 - 简单后端',
        'version': '1.0.0',
        'status': 'running',
        'database': 'connected' if db_ok else 'disconnected',
        'user_count': db_info if db_ok else 0,
        'timestamp': datetime.now().isoformat(),
        'api_docs': '/api/v1/docs',
        'health_check': '/health'
    })

@app.route('/health')
def health():
    """健康检查"""
    db_ok, db_info = test_database()
    return jsonify({
        'status': 'healthy' if db_ok else 'unhealthy',
        'database': {
            'status': 'connected' if db_ok else 'disconnected',
            'user_count': db_info if db_ok else 0
        },
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/v1/docs')
def api_docs():
    """API文档"""
    return success_response({
        'title': '高速公路智能监控系统 API',
        'version': '1.0.0',
        'endpoints': {
            'auth': {
                'POST /api/v1/auth/login': '用户登录',
                'POST /api/v1/auth/logout': '用户登出',
                'GET /api/v1/auth/profile': '获取用户信息'
            },
            'monitor': {
                'GET /api/v1/monitor/list': '获取监控点列表'
            },
            'analysis': {
                'GET /api/v1/analysis/statistics/overview': '获取概览统计'
            },
            'system': {
                'GET /api/v1/system/health-check': '系统健康检查'
            }
        }
    }, 'API文档获取成功')

@app.route('/api/v1/auth/login', methods=['POST'])
def login():
    """用户登录"""
    try:
        data = request.get_json()
        if not data:
            return error_response('请求数据格式错误')
        
        username = data.get('username')
        password = data.get('password')
        
        if not username or not password:
            return error_response('用户名和密码不能为空')
        
        # 数据库查询
        config = get_db_config()
        connection = pymysql.connect(**config)
        
        with connection.cursor() as cursor:
            cursor.execute(
                "SELECT * FROM user WHERE username=%s AND password=%s",
                (username, password)
            )
            user = cursor.fetchone()
        
        connection.close()
        
        if not user:
            return error_response('用户名或密码错误')
        
        # 生成JWT令牌
        token = jwt.encode({
            'user_id': user['id'],
            'username': user['username'],
            'exp': datetime.utcnow() + timedelta(hours=24)
        }, app.config['SECRET_KEY'], algorithm='HS256')
        
        # 设置session
        session['user_id'] = user['id']
        session['username'] = user['username']
        
        return success_response({
            'token': token,
            'user': {
                'id': user['id'],
                'username': user['username'],
                'email': user['email'],
                'grade': user['grade'],
                'avatar': user.get('avatar', 'default_avatar.jpg')
            }
        }, '登录成功')
        
    except Exception as e:
        return error_response(f'登录失败: {str(e)}')

def token_required(f):
    """JWT令牌验证装饰器"""
    from functools import wraps
    
    @wraps(f)
    def decorated(*args, **kwargs):
        token = request.headers.get('Authorization')
        if not token:
            return error_response('缺少访问令牌', 401)
        
        try:
            if token.startswith('Bearer '):
                token = token[7:]
            data = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])
            current_user_id = data['user_id']
        except jwt.ExpiredSignatureError:
            return error_response('令牌已过期', 401)
        except jwt.InvalidTokenError:
            return error_response('无效的令牌', 401)
        
        return f(current_user_id, *args, **kwargs)
    return decorated

@app.route('/api/v1/auth/logout', methods=['POST'])
@token_required
def logout(current_user_id):
    """用户登出"""
    try:
        session.clear()
        return success_response(None, '登出成功')
    except Exception as e:
        return error_response(f'登出失败: {str(e)}')

@app.route('/api/v1/auth/profile', methods=['GET'])
@token_required
def get_profile(current_user_id):
    """获取用户信息"""
    try:
        config = get_db_config()
        connection = pymysql.connect(**config)
        
        with connection.cursor() as cursor:
            cursor.execute(
                "SELECT id, username, email, avatar, grade, create_time FROM user WHERE id=%s",
                (current_user_id,)
            )
            user = cursor.fetchone()
        
        connection.close()
        
        if not user:
            return error_response('用户不存在')
        
        return success_response(user, '获取用户信息成功')
        
    except Exception as e:
        return error_response(f'获取用户信息失败: {str(e)}')

@app.route('/api/v1/monitor/list', methods=['GET'])
@token_required
def get_monitor_list(current_user_id):
    """获取监控点列表"""
    try:
        config = get_db_config()
        connection = pymysql.connect(**config)
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT * FROM monitor ORDER BY id")
            monitors = cursor.fetchall()
        
        connection.close()
        
        return success_response({
            'monitors': monitors,
            'total': len(monitors)
        }, '获取监控点列表成功')
        
    except Exception as e:
        return error_response(f'获取监控点列表失败: {str(e)}')

@app.route('/api/v1/analysis/statistics/overview', methods=['GET'])
@token_required
def get_overview_statistics(current_user_id):
    """获取概览统计"""
    try:
        config = get_db_config()
        connection = pymysql.connect(**config)
        
        with connection.cursor() as cursor:
            # 监控点总数
            cursor.execute("SELECT COUNT(*) as count FROM monitor")
            monitor_count = cursor.fetchone()['count']
            
            # 活跃监控点数
            cursor.execute("SELECT COUNT(*) as count FROM monitor WHERE is_alarm='开启'")
            active_monitor_count = cursor.fetchone()['count']
            
            # 今日警报数
            cursor.execute("SELECT COUNT(*) as count FROM alarm WHERE DATE(create_time) = CURDATE()")
            today_alarms = cursor.fetchone()['count']
            
            # 总警报数
            cursor.execute("SELECT COUNT(*) as count FROM alarm")
            total_alarms = cursor.fetchone()['count']
        
        connection.close()
        
        return success_response({
            'monitor_count': monitor_count,
            'active_monitor_count': active_monitor_count,
            'today_alarms': today_alarms,
            'total_alarms': total_alarms,
            'avg_vehicles': 0,  # 简化版本
            'max_vehicles': 0   # 简化版本
        }, '获取概览统计成功')
        
    except Exception as e:
        return error_response(f'获取概览统计失败: {str(e)}')

@app.route('/api/v1/system/health-check', methods=['GET'])
def system_health_check():
    """系统健康检查"""
    try:
        db_ok, db_info = test_database()
        
        return success_response({
            'overall_status': 'healthy' if db_ok else 'unhealthy',
            'checks': {
                'database': {
                    'status': 'healthy' if db_ok else 'unhealthy',
                    'user_count': db_info if db_ok else 0
                },
                'memory': {
                    'status': 'healthy',
                    'usage': 50.0  # 简化版本
                },
                'cpu': {
                    'status': 'healthy',
                    'usage': 30.0  # 简化版本
                },
                'disk': {
                    'status': 'healthy',
                    'usage': 70.0  # 简化版本
                }
            },
            'timestamp': datetime.now().isoformat()
        }, '健康检查完成')
        
    except Exception as e:
        return error_response(f'健康检查失败: {str(e)}')

if __name__ == '__main__':
    print("=" * 60)
    print("🚀 启动简单可靠的后端服务")
    print("=" * 60)
    
    # 测试数据库连接
    db_ok, db_info = test_database()
    if db_ok:
        print(f"✅ 数据库连接成功，用户数: {db_info}")
    else:
        print(f"❌ 数据库连接失败: {db_info}")
        print("⚠️ 服务将继续启动，但数据库功能不可用")
    
    print("服务地址: http://127.0.0.1:5500")
    print("API文档: http://127.0.0.1:5500/api/v1/docs")
    print("健康检查: http://127.0.0.1:5500/health")
    print("=" * 60)
    
    app.run(host='127.0.0.1', port=5500, debug=False)
