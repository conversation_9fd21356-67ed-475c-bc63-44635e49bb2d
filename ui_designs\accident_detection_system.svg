<svg width="1200" height="900" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f7fafc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#edf2f7;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f7fafc;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="dangerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fed7d7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#feb2b2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="warningGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fef5e7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fbd38d;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="successGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#c6f6d5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#9ae6b4;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="rgba(0,0,0,0.1)"/>
    </filter>
  </defs>
  
  <rect width="100%" height="100%" fill="url(#bgGradient)"/>
  
  <!-- Header -->
  <rect x="20" y="20" width="1160" height="80" rx="16" fill="url(#cardGradient)" filter="url(#shadow)"/>
  <text x="50" y="45" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#2d3748">🚨 事故检测系统</text>
  <text x="50" y="75" font-family="Arial, sans-serif" font-size="14" fill="#718096">智能监控 · 实时预警 · 快速响应</text>
  
  <!-- Status Indicator -->
  <circle cx="1100" cy="50" r="8" fill="#48bb78">
    <animate attributeName="opacity" values="1;0.5;1" dur="2s" repeatCount="indefinite"/>
  </circle>
  <text x="1120" y="55" font-family="Arial, sans-serif" font-size="12" fill="#48bb78">系统运行中</text>
  
  <!-- Statistics Overview -->
  <rect x="20" y="120" width="1160" height="140" rx="16" fill="url(#cardGradient)" filter="url(#shadow)"/>
  <text x="50" y="150" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#2d3748">📊 事故统计概览</text>
  
  <!-- Stat Cards -->
  <!-- Total Accidents -->
  <rect x="50" y="170" width="260" height="70" rx="12" fill="url(#dangerGradient)" stroke="#f56565" stroke-width="2"/>
  <text x="70" y="190" font-family="Arial, sans-serif" font-size="12" fill="#c53030">总事故数</text>
  <text x="70" y="215" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="#c53030">156</text>
  <text x="70" y="230" font-family="Arial, sans-serif" font-size="10" fill="#c53030">累计统计</text>
  <text x="280" y="210" font-family="Arial, sans-serif" font-size="24" fill="#c53030">⚠️</text>
  
  <!-- Today's Accidents -->
  <rect x="330" y="170" width="260" height="70" rx="12" fill="url(#warningGradient)" stroke="#ed8936" stroke-width="2"/>
  <text x="350" y="190" font-family="Arial, sans-serif" font-size="12" fill="#c05621">今日事故</text>
  <text x="350" y="215" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="#c05621">12</text>
  <text x="350" y="230" font-family="Arial, sans-serif" font-size="10" fill="#c05621">较昨日 +3</text>
  <text x="560" y="210" font-family="Arial, sans-serif" font-size="24" fill="#c05621">📈</text>
  
  <!-- Processing -->
  <rect x="610" y="170" width="260" height="70" rx="12" fill="url(#warningGradient)" stroke="#ed8936" stroke-width="2"/>
  <text x="630" y="190" font-family="Arial, sans-serif" font-size="12" fill="#c05621">处理中</text>
  <text x="630" y="215" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="#c05621">3</text>
  <text x="630" y="230" font-family="Arial, sans-serif" font-size="10" fill="#c05621">需要关注</text>
  <circle cx="840" cy="205" r="6" fill="#ed8936">
    <animate attributeName="opacity" values="1;0.3;1" dur="1s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Average Response -->
  <rect x="890" y="170" width="260" height="70" rx="12" fill="url(#successGradient)" stroke="#48bb78" stroke-width="2"/>
  <text x="910" y="190" font-family="Arial, sans-serif" font-size="12" fill="#2f855a">平均响应</text>
  <text x="910" y="215" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="#2f855a">8.5</text>
  <text x="910" y="230" font-family="Arial, sans-serif" font-size="10" fill="#2f855a">分钟</text>
  <text x="1120" y="210" font-family="Arial, sans-serif" font-size="24" fill="#2f855a">⚡</text>
  
  <!-- Detection Configuration -->
  <rect x="20" y="280" width="1160" height="320" rx="16" fill="url(#cardGradient)" filter="url(#shadow)"/>
  <text x="50" y="310" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#2d3748">🔧 检测配置</text>
  
  <!-- Monitor Point Selection -->
  <text x="50" y="340" font-family="Arial, sans-serif" font-size="14" fill="#4a5568">监控点:</text>
  <rect x="120" y="325" width="200" height="30" rx="6" fill="#ffffff" stroke="#cbd5e0" stroke-width="1"/>
  <text x="130" y="345" font-family="Arial, sans-serif" font-size="12" fill="#2d3748">监控点001</text>
  <polygon points="310,335 300,340 310,345" fill="#718096"/>
  
  <!-- Detection Types -->
  <text x="50" y="375" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#4a5568">检测类型:</text>
  
  <!-- Collision Detection -->
  <rect x="50" y="385" width="15" height="15" rx="3" fill="#48bb78" stroke="#2f855a" stroke-width="1"/>
  <text x="55" y="395" font-family="Arial, sans-serif" font-size="10" fill="#ffffff">✓</text>
  <text x="75" y="397" font-family="Arial, sans-serif" font-size="12" fill="#2d3748">碰撞检测</text>
  <text x="160" y="397" font-family="Arial, sans-serif" font-size="12" fill="#718096">阈值:</text>
  <rect x="200" y="387" width="100" height="12" rx="6" fill="#e2e8f0"/>
  <rect x="200" y="387" width="80" height="12" rx="6" fill="#4299e1"/>
  <text x="310" y="397" font-family="Arial, sans-serif" font-size="10" fill="#4a5568">0.8</text>
  
  <!-- Emergency Stop Detection -->
  <rect x="50" y="410" width="15" height="15" rx="3" fill="#48bb78" stroke="#2f855a" stroke-width="1"/>
  <text x="55" y="420" font-family="Arial, sans-serif" font-size="10" fill="#ffffff">✓</text>
  <text x="75" y="422" font-family="Arial, sans-serif" font-size="12" fill="#2d3748">急停检测</text>
  <text x="160" y="422" font-family="Arial, sans-serif" font-size="12" fill="#718096">阈值:</text>
  <rect x="200" y="412" width="100" height="12" rx="6" fill="#e2e8f0"/>
  <rect x="200" y="412" width="50" height="12" rx="6" fill="#ed8936"/>
  <text x="310" y="422" font-family="Arial, sans-serif" font-size="10" fill="#4a5568">5.0</text>
  
  <!-- Traffic Jam Detection -->
  <rect x="50" y="435" width="15" height="15" rx="3" fill="#48bb78" stroke="#2f855a" stroke-width="1"/>
  <text x="55" y="445" font-family="Arial, sans-serif" font-size="10" fill="#ffffff">✓</text>
  <text x="75" y="447" font-family="Arial, sans-serif" font-size="12" fill="#2d3748">拥堵检测</text>
  <text x="160" y="447" font-family="Arial, sans-serif" font-size="12" fill="#718096">密度:</text>
  <rect x="200" y="437" width="100" height="12" rx="6" fill="#e2e8f0"/>
  <rect x="200" y="437" width="70" height="12" rx="6" fill="#9f7aea"/>
  <text x="310" y="447" font-family="Arial, sans-serif" font-size="10" fill="#4a5568">0.7</text>
  <text x="340" y="447" font-family="Arial, sans-serif" font-size="12" fill="#718096">速度:</text>
  <text x="380" y="447" font-family="Arial, sans-serif" font-size="12" fill="#4a5568">20 km/h</text>
  
  <!-- Alert Settings -->
  <text x="50" y="480" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#4a5568">预警设置:</text>
  
  <!-- Email Alert -->
  <rect x="50" y="490" width="15" height="15" rx="3" fill="#48bb78" stroke="#2f855a" stroke-width="1"/>
  <text x="55" y="500" font-family="Arial, sans-serif" font-size="10" fill="#ffffff">✓</text>
  <text x="75" y="502" font-family="Arial, sans-serif" font-size="12" fill="#2d3748">邮件预警</text>
  
  <!-- SMS Alert -->
  <rect x="160" y="490" width="15" height="15" rx="3" fill="#ffffff" stroke="#cbd5e0" stroke-width="1"/>
  <text x="185" y="502" font-family="Arial, sans-serif" font-size="12" fill="#718096">短信预警</text>
  
  <!-- Webhook -->
  <rect x="260" y="490" width="15" height="15" rx="3" fill="#48bb78" stroke="#2f855a" stroke-width="1"/>
  <text x="265" y="500" font-family="Arial, sans-serif" font-size="10" fill="#ffffff">✓</text>
  <text x="285" y="502" font-family="Arial, sans-serif" font-size="12" fill="#2d3748">Webhook</text>
  
  <!-- Severity Threshold -->
  <text x="50" y="525" font-family="Arial, sans-serif" font-size="12" fill="#718096">严重程度阈值:</text>
  <rect x="150" y="510" width="80" height="25" rx="6" fill="#ffffff" stroke="#cbd5e0" stroke-width="1"/>
  <text x="160" y="527" font-family="Arial, sans-serif" font-size="12" fill="#2d3748">中等</text>
  <polygon points="220,520 210,525 220,530" fill="#718096"/>
  
  <!-- Alert Interval -->
  <text x="250" y="525" font-family="Arial, sans-serif" font-size="12" fill="#718096">预警间隔:</text>
  <rect x="320" y="510" width="80" height="25" rx="6" fill="#ffffff" stroke="#cbd5e0" stroke-width="1"/>
  <text x="330" y="527" font-family="Arial, sans-serif" font-size="12" fill="#2d3748">300秒</text>
  
  <!-- Action Buttons -->
  <rect x="50" y="550" width="100" height="35" rx="8" fill="#4299e1" stroke="#3182ce" stroke-width="1"/>
  <text x="75" y="570" font-family="Arial, sans-serif" font-size="12" fill="#ffffff" font-weight="bold">💾 保存配置</text>
  
  <rect x="170" y="550" width="80" height="35" rx="8" fill="#ffffff" stroke="#cbd5e0" stroke-width="1"/>
  <text x="190" y="570" font-family="Arial, sans-serif" font-size="12" fill="#4a5568">🔄 重置</text>
  
  <rect x="270" y="550" width="100" height="35" rx="8" fill="#48bb78" stroke="#38a169" stroke-width="1"/>
  <text x="290" y="570" font-family="Arial, sans-serif" font-size="12" fill="#ffffff" font-weight="bold">▶️ 启动检测</text>
  
  <!-- Accident Records -->
  <rect x="20" y="620" width="1160" height="260" rx="16" fill="url(#cardGradient)" filter="url(#shadow)"/>
  <text x="50" y="650" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#2d3748">📋 事故记录</text>
  
  <!-- Table Header -->
  <rect x="50" y="665" width="1100" height="35" rx="8" fill="#f7fafc" stroke="#e2e8f0" stroke-width="1"/>
  <text x="70" y="685" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#4a5568">时间</text>
  <text x="200" y="685" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#4a5568">类型</text>
  <text x="300" y="685" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#4a5568">严重程度</text>
  <text x="450" y="685" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#4a5568">位置</text>
  <text x="600" y="685" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#4a5568">状态</text>
  <text x="750" y="685" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#4a5568">操作</text>
  
  <!-- Table Rows -->
  <!-- Row 1 -->
  <rect x="50" y="700" width="1100" height="40" rx="6" fill="#ffffff" stroke="#e2e8f0" stroke-width="1"/>
  <text x="70" y="722" font-family="Arial, sans-serif" font-size="12" fill="#2d3748">14:35:20</text>
  <text x="200" y="722" font-family="Arial, sans-serif" font-size="12" fill="#2d3748">碰撞</text>
  <rect x="300" y="710" width="40" height="20" rx="10" fill="#fed7d7"/>
  <text x="310" y="722" font-family="Arial, sans-serif" font-size="10" fill="#c53030">高</text>
  <text x="450" y="722" font-family="Arial, sans-serif" font-size="12" fill="#2d3748">K100+500</text>
  <rect x="600" y="710" width="60" height="20" rx="10" fill="#fef5e7"/>
  <text x="610" y="722" font-family="Arial, sans-serif" font-size="10" fill="#c05621">处理中</text>
  <rect x="750" y="710" width="50" height="20" rx="6" fill="#4299e1"/>
  <text x="765" y="722" font-family="Arial, sans-serif" font-size="10" fill="#ffffff">查看</text>
  
  <!-- Row 2 -->
  <rect x="50" y="740" width="1100" height="40" rx="6" fill="#f7fafc" stroke="#e2e8f0" stroke-width="1"/>
  <text x="70" y="762" font-family="Arial, sans-serif" font-size="12" fill="#2d3748">13:22:15</text>
  <text x="200" y="762" font-family="Arial, sans-serif" font-size="12" fill="#2d3748">急停</text>
  <rect x="300" y="750" width="40" height="20" rx="10" fill="#fef5e7"/>
  <text x="310" y="762" font-family="Arial, sans-serif" font-size="10" fill="#c05621">中</text>
  <text x="450" y="762" font-family="Arial, sans-serif" font-size="12" fill="#2d3748">K100+300</text>
  <rect x="600" y="750" width="60" height="20" rx="10" fill="#c6f6d5"/>
  <text x="610" y="762" font-family="Arial, sans-serif" font-size="10" fill="#2f855a">已处理</text>
  <rect x="750" y="750" width="50" height="20" rx="6" fill="#4299e1"/>
  <text x="765" y="762" font-family="Arial, sans-serif" font-size="10" fill="#ffffff">查看</text>
  
  <!-- Row 3 -->
  <rect x="50" y="780" width="1100" height="40" rx="6" fill="#ffffff" stroke="#e2e8f0" stroke-width="1"/>
  <text x="70" y="802" font-family="Arial, sans-serif" font-size="12" fill="#2d3748">12:45:30</text>
  <text x="200" y="802" font-family="Arial, sans-serif" font-size="12" fill="#2d3748">拥堵</text>
  <rect x="300" y="790" width="40" height="20" rx="10" fill="#e6fffa"/>
  <text x="310" y="802" font-family="Arial, sans-serif" font-size="10" fill="#319795">低</text>
  <text x="450" y="802" font-family="Arial, sans-serif" font-size="12" fill="#2d3748">K100+800</text>
  <rect x="600" y="790" width="60" height="20" rx="10" fill="#c6f6d5"/>
  <text x="610" y="802" font-family="Arial, sans-serif" font-size="10" fill="#2f855a">已处理</text>
  <rect x="750" y="790" width="50" height="20" rx="6" fill="#4299e1"/>
  <text x="765" y="802" font-family="Arial, sans-serif" font-size="10" fill="#ffffff">查看</text>
  
  <!-- Pagination -->
  <text x="50" y="850" font-family="Arial, sans-serif" font-size="12" fill="#718096">显示 1-3 条，共 156 条记录</text>
  <rect x="1000" y="835" width="30" height="25" rx="6" fill="#4299e1"/>
  <text x="1010" y="850" font-family="Arial, sans-serif" font-size="12" fill="#ffffff">1</text>
  <rect x="1040" y="835" width="30" height="25" rx="6" fill="#ffffff" stroke="#cbd5e0" stroke-width="1"/>
  <text x="1050" y="850" font-family="Arial, sans-serif" font-size="12" fill="#4a5568">2</text>
  <rect x="1080" y="835" width="30" height="25" rx="6" fill="#ffffff" stroke="#cbd5e0" stroke-width="1"/>
  <text x="1090" y="850" font-family="Arial, sans-serif" font-size="12" fill="#4a5568">3</text>
  
</svg>