# 基于Yolov8与ByteTrack的高速公路智慧监控平台 - CUDA GPU版本依赖
# 与PyTorch 2.1.2 + CUDA 11.8兼容的版本

# 注意：PyTorch和TorchVision需要从本地wheel文件安装
# torch==2.1.2+cu118  # 从本地安装
# torchvision==0.16.1+cu118  # 从本地安装

# 核心机器学习和计算机视觉（兼容版本）
ultralytics>=8.0.0,<9.0.0
opencv-python==********
# 注意：不要同时安装opencv-contrib-python，会冲突
supervision>=0.16.0,<1.0.0
numpy==1.24.3  # PyTorch 2.1.2兼容版本
Pillow>=9.0.0,<11.0.0  # 兼容范围
matplotlib>=3.5.0,<4.0.0
scipy>=1.9.0,<2.0.0

# Web框架（稳定版本）
Flask==2.3.3
Flask-CORS==4.0.0
Werkzeug>=2.3.0,<3.0.0

# 数据库（稳定版本）
PyMySQL==1.1.0

# 数据处理（兼容版本）
pandas>=2.0.0,<3.0.0

# 网络和通信（基础版本）
requests>=2.28.0,<3.0.0

# 配置管理
python-dotenv==1.0.0

# 系统监控
psutil>=5.9.0

# 其他工具（兼容版本）
tqdm>=4.64.0
click>=8.0.0
python-dateutil>=2.8.0
pytz>=2022.1

# 可选依赖（如果需要完整功能）
# Flask-SocketIO==5.3.6  # WebSocket支持
# redis==5.0.1  # 缓存支持
# APScheduler==3.10.4  # 任务调度
# gunicorn==21.2.0  # 生产服务器

# 开发工具（可选）
# pytest>=7.0.0
# black>=22.0.0
# flake8>=5.0.0
