#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
后端健康检查脚本 - 逐步排查问题
"""

import requests
import json
import time

def check_server_basic():
    """检查服务器基础连接"""
    print("🔍 第一步：检查服务器基础连接")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5500"
    
    # 1. 检查根路径
    print("1. 检查根路径...")
    try:
        response = requests.get(base_url, timeout=5)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ 根路径可访问")
            try:
                data = response.json()
                print(f"   响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            except:
                print(f"   响应文本: {response.text}")
        else:
            print(f"   ❌ 根路径访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 根路径连接失败: {e}")
        return False
    
    # 2. 检查健康检查端点
    print("\n2. 检查健康检查端点...")
    health_endpoints = ["/health", "/api/health", "/api/v1/health", "/api/v1/system/health-check"]
    
    for endpoint in health_endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            print(f"   {endpoint}: {response.status_code}")
            if response.status_code == 200:
                print(f"   ✅ 健康检查端点找到: {endpoint}")
                try:
                    data = response.json()
                    print(f"   响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
                except:
                    print(f"   响应文本: {response.text}")
                break
        except Exception as e:
            print(f"   {endpoint}: 连接失败 - {e}")
    
    # 3. 检查API文档端点
    print("\n3. 检查API文档端点...")
    doc_endpoints = ["/docs", "/api/docs", "/api/v1/docs", "/swagger", "/redoc"]
    
    for endpoint in doc_endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            print(f"   {endpoint}: {response.status_code}")
            if response.status_code == 200:
                print(f"   ✅ API文档端点找到: {endpoint}")
                break
        except Exception as e:
            print(f"   {endpoint}: 连接失败 - {e}")
    
    return True

def check_api_paths():
    """检查API路径"""
    print("\n🔍 第二步：检查API路径")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5500"
    
    # 测试不同的API基础路径
    api_bases = ["/api/v1", "/api", "/v1", ""]
    auth_paths = ["/auth/login", "/login", "/user/login"]
    
    for api_base in api_bases:
        for auth_path in auth_paths:
            full_path = f"{api_base}{auth_path}"
            print(f"\n测试路径: {full_path}")
            
            try:
                # 先用GET测试路径是否存在
                response = requests.get(f"{base_url}{full_path}", timeout=5)
                print(f"   GET {full_path}: {response.status_code}")
                
                if response.status_code != 404:
                    # 如果不是404，尝试POST登录
                    login_data = {"username": "admin", "password": "123456"}
                    response = requests.post(
                        f"{base_url}{full_path}", 
                        json=login_data, 
                        timeout=5
                    )
                    print(f"   POST {full_path}: {response.status_code}")
                    
                    if response.status_code == 200:
                        print(f"   ✅ 找到有效的登录路径: {full_path}")
                        try:
                            data = response.json()
                            print(f"   响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
                            return full_path
                        except:
                            print(f"   响应文本: {response.text}")
                    elif response.status_code != 404:
                        print(f"   ⚠️ 路径存在但登录失败: {response.status_code}")
                        try:
                            data = response.json()
                            print(f"   错误信息: {json.dumps(data, indent=2, ensure_ascii=False)}")
                        except:
                            print(f"   错误文本: {response.text}")
                        
            except Exception as e:
                print(f"   ❌ 连接失败: {e}")
    
    print("   ❌ 未找到有效的登录路径")
    return None

def check_database_endpoints():
    """检查数据库相关端点"""
    print("\n🔍 第三步：检查数据库相关端点")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5500"
    
    # 测试数据库相关的端点
    db_endpoints = [
        "/api/v1/system/health-check",
        "/api/v1/system/info", 
        "/api/v1/monitor/list",
        "/api/v1/analysis/statistics/overview"
    ]
    
    for endpoint in db_endpoints:
        print(f"\n测试端点: {endpoint}")
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ 端点可访问")
                try:
                    data = response.json()
                    print(f"   响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
                except:
                    print(f"   响应文本: {response.text}")
            elif response.status_code == 401:
                print(f"   ⚠️ 需要认证")
            elif response.status_code == 404:
                print(f"   ❌ 端点不存在")
            else:
                print(f"   ⚠️ 其他错误: {response.status_code}")
                try:
                    data = response.json()
                    print(f"   错误信息: {json.dumps(data, indent=2, ensure_ascii=False)}")
                except:
                    print(f"   错误文本: {response.text}")
                    
        except Exception as e:
            print(f"   ❌ 连接失败: {e}")

def main():
    """主函数"""
    print("=" * 80)
    print("🚀 后端健康检查 - 逐步排查问题")
    print("=" * 80)
    
    # 第一步：基础连接
    if not check_server_basic():
        print("\n❌ 服务器基础连接失败，请检查：")
        print("   1. 后端服务是否启动？")
        print("   2. 端口5500是否被占用？")
        print("   3. 防火墙是否阻止了连接？")
        return
    
    # 第二步：API路径
    valid_login_path = check_api_paths()
    if not valid_login_path:
        print("\n❌ 未找到有效的API路径，请检查：")
        print("   1. API路由是否正确注册？")
        print("   2. 蓝图是否正确挂载？")
        print("   3. URL前缀是否正确？")
    
    # 第三步：数据库端点
    check_database_endpoints()
    
    print("\n" + "=" * 80)
    print("📋 检查完成")
    print("💡 请根据上述结果修复相应问题")
    print("=" * 80)

if __name__ == "__main__":
    main()
