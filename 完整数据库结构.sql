-- =====================================================
-- 基于Yolov8与ByteTrack的高速公路智慧监控平台 - 完整数据库结构
-- 创建时间: 2024-12-24
-- 版本: v2.0
-- =====================================================

-- 删除数据库（如果存在）
DROP DATABASE IF EXISTS `yolo`;

-- 创建数据库
CREATE DATABASE `yolo` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE `yolo`;

-- =====================================================
-- 1. 用户管理表
-- =====================================================

-- 用户表
CREATE TABLE `user` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像URL',
  `grade` varchar(20) NOT NULL DEFAULT 'user' COMMENT '用户级别(admin/operator/user)',
  `status` tinyint(1) DEFAULT 1 COMMENT '用户状态(0:禁用,1:启用)',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `login_count` int DEFAULT 0 COMMENT '登录次数',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_grade` (`grade`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- =====================================================
-- 2. 监控点管理表
-- =====================================================

-- 监控点表
CREATE TABLE `monitor` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '监控点ID',
  `name` varchar(100) NOT NULL COMMENT '监控点名称',
  `location` varchar(100) NOT NULL COMMENT '监控地点',
  `highway_section` varchar(100) DEFAULT NULL COMMENT '高速公路路段',
  `camera_position` varchar(200) DEFAULT NULL COMMENT '摄像头位置说明',
  `latitude` decimal(10,7) DEFAULT NULL COMMENT '纬度',
  `longitude` decimal(10,7) DEFAULT NULL COMMENT '经度',
  `threshold` int NOT NULL DEFAULT 15 COMMENT '警报阈值',
  `conf_threshold` float DEFAULT 0.4 COMMENT '检测置信度阈值',
  `iou_threshold` float DEFAULT 0.5 COMMENT 'IOU阈值',
  `person` varchar(50) DEFAULT NULL COMMENT '负责人',
  `video` varchar(500) DEFAULT NULL COMMENT '视频文件路径',
  `url` varchar(500) DEFAULT NULL COMMENT 'RTSP流地址',
  `rtsp_format` varchar(20) DEFAULT 'standard' COMMENT 'RTSP格式',
  `connection_status` varchar(20) DEFAULT 'unknown' COMMENT '连接状态',
  `is_alarm` varchar(10) DEFAULT '开启' COMMENT '是否启用警报',
  `mode` varchar(20) DEFAULT '准确模式' COMMENT '检测模式',
  `show_labels` tinyint(1) DEFAULT 1 COMMENT '是否显示标签',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` text COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_location` (`location`),
  KEY `idx_highway_section` (`highway_section`),
  KEY `idx_connection_status` (`connection_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='监控点表';

-- =====================================================
-- 3. 检测任务管理表
-- =====================================================

-- 检测任务表
CREATE TABLE `detection_task` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `task_id` varchar(100) NOT NULL COMMENT '任务唯一标识',
  `task_name` varchar(100) NOT NULL COMMENT '任务名称',
  `task_type` varchar(20) NOT NULL COMMENT '任务类型(image/video/rtsp)',
  `monitor_id` int DEFAULT NULL COMMENT '关联监控点ID',
  `file_path` varchar(500) DEFAULT NULL COMMENT '文件路径',
  `file_size` bigint DEFAULT NULL COMMENT '文件大小(字节)',
  `file_format` varchar(20) DEFAULT NULL COMMENT '文件格式',
  `model_name` varchar(50) DEFAULT 'yolov8n.pt' COMMENT '使用的模型',
  `conf_threshold` float DEFAULT 0.4 COMMENT '置信度阈值',
  `iou_threshold` float DEFAULT 0.5 COMMENT 'IOU阈值',
  `show_labels` tinyint(1) DEFAULT 1 COMMENT '是否显示标签',
  `status` varchar(20) DEFAULT 'pending' COMMENT '任务状态(pending/running/completed/failed/stopped)',
  `progress` float DEFAULT 0 COMMENT '任务进度(0-100)',
  `result_path` varchar(500) DEFAULT NULL COMMENT '结果文件路径',
  `detection_count` int DEFAULT 0 COMMENT '检测到的目标数量',
  `processing_time` float DEFAULT NULL COMMENT '处理时间(秒)',
  `fps` float DEFAULT NULL COMMENT '处理帧率',
  `error_message` text COMMENT '错误信息',
  `user_id` int DEFAULT NULL COMMENT '创建用户ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_id` (`task_id`),
  KEY `idx_task_type` (`task_type`),
  KEY `idx_monitor_id` (`monitor_id`),
  KEY `idx_status` (`status`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_detection_task_monitor` FOREIGN KEY (`monitor_id`) REFERENCES `monitor` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_detection_task_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='检测任务表';

-- 检测结果表
CREATE TABLE `detection_result` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '结果ID',
  `task_id` varchar(100) NOT NULL COMMENT '任务ID',
  `frame_id` int DEFAULT NULL COMMENT '帧ID',
  `timestamp` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '检测时间',
  `detections` json COMMENT '检测结果(JSON格式)',
  `detection_count` int DEFAULT 0 COMMENT '检测数量',
  `confidence_avg` float DEFAULT NULL COMMENT '平均置信度',
  `confidence_max` float DEFAULT NULL COMMENT '最高置信度',
  `class_counts` json COMMENT '类别统计(JSON格式)',
  `image_path` varchar(500) DEFAULT NULL COMMENT '检测图像路径',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_timestamp` (`timestamp`),
  KEY `idx_detection_count` (`detection_count`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='检测结果表';

-- =====================================================
-- 4. 追踪管理表
-- =====================================================

-- 追踪目标表
CREATE TABLE `tracking_target` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '目标ID',
  `track_id` int NOT NULL COMMENT '追踪ID',
  `monitor_id` int NOT NULL COMMENT '监控点ID',
  `object_type` varchar(50) NOT NULL COMMENT '目标类型',
  `confidence` float NOT NULL COMMENT '置信度',
  `bbox_x` float NOT NULL COMMENT '边界框X坐标',
  `bbox_y` float NOT NULL COMMENT '边界框Y坐标',
  `bbox_w` float NOT NULL COMMENT '边界框宽度',
  `bbox_h` float NOT NULL COMMENT '边界框高度',
  `velocity_x` float DEFAULT NULL COMMENT 'X方向速度',
  `velocity_y` float DEFAULT NULL COMMENT 'Y方向速度',
  `speed` float DEFAULT NULL COMMENT '速度(km/h)',
  `direction` float DEFAULT NULL COMMENT '方向角度',
  `first_seen` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '首次出现时间',
  `last_seen` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后出现时间',
  `track_length` int DEFAULT 1 COMMENT '追踪长度(帧数)',
  `status` varchar(20) DEFAULT 'active' COMMENT '状态(active/lost/completed)',
  `algorithm` varchar(50) DEFAULT 'bytetrack' COMMENT '使用的追踪算法',
  `metadata` json COMMENT '额外元数据',
  PRIMARY KEY (`id`),
  KEY `idx_track_id` (`track_id`),
  KEY `idx_monitor_id` (`monitor_id`),
  KEY `idx_object_type` (`object_type`),
  KEY `idx_status` (`status`),
  KEY `idx_last_seen` (`last_seen`),
  CONSTRAINT `fk_tracking_target_monitor` FOREIGN KEY (`monitor_id`) REFERENCES `monitor` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='追踪目标表';

-- 追踪性能统计表
CREATE TABLE `tracking_performance` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '性能记录ID',
  `monitor_id` int NOT NULL COMMENT '监控点ID',
  `algorithm_name` varchar(100) NOT NULL COMMENT '追踪算法名称',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `stat_hour` int DEFAULT NULL COMMENT '统计小时',
  `total_tracks` int DEFAULT 0 COMMENT '总追踪数',
  `successful_tracks` int DEFAULT 0 COMMENT '成功追踪数',
  `lost_tracks` int DEFAULT 0 COMMENT '丢失追踪数',
  `avg_track_length` float DEFAULT NULL COMMENT '平均追踪长度',
  `fps` float DEFAULT NULL COMMENT '处理帧率',
  `cpu_usage` float DEFAULT NULL COMMENT 'CPU使用率',
  `memory_usage` float DEFAULT NULL COMMENT '内存使用率',
  `accuracy` float DEFAULT NULL COMMENT '追踪准确率',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_monitor_id` (`monitor_id`),
  KEY `idx_algorithm_name` (`algorithm_name`),
  KEY `idx_stat_date` (`stat_date`),
  CONSTRAINT `fk_performance_monitor` FOREIGN KEY (`monitor_id`) REFERENCES `monitor` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='追踪性能统计表';

-- =====================================================
-- 5. 事故检测表
-- =====================================================

-- 事故记录表
CREATE TABLE `accident_record` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '事故记录ID',
  `record_id` varchar(100) NOT NULL COMMENT '事故记录唯一标识',
  `monitor_id` int NOT NULL COMMENT '监控点ID',
  `accident_type` varchar(50) NOT NULL COMMENT '事故类型(collision/sudden_stop/congestion/emergency)',
  `severity` varchar(20) DEFAULT 'medium' COMMENT '严重程度(low/medium/high/critical)',
  `location_x` float DEFAULT NULL COMMENT '事故位置X坐标',
  `location_y` float DEFAULT NULL COMMENT '事故位置Y坐标',
  `description` text COMMENT '事故描述',
  `confidence` float DEFAULT NULL COMMENT '检测置信度',
  `involved_vehicles` json COMMENT '涉及车辆信息(JSON格式)',
  `estimated_damage` varchar(100) DEFAULT NULL COMMENT '预估损失',
  `emergency_services` tinyint(1) DEFAULT 0 COMMENT '是否需要紧急服务',
  `evidence_image` varchar(500) DEFAULT NULL COMMENT '证据图片路径',
  `evidence_video` varchar(500) DEFAULT NULL COMMENT '证据视频路径',
  `status` varchar(20) DEFAULT 'detected' COMMENT '状态(detected/confirmed/resolved/dismissed)',
  `detection_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '检测时间',
  `response_time` datetime DEFAULT NULL COMMENT '响应时间',
  `resolution_time` datetime DEFAULT NULL COMMENT '解决时间',
  `handled_by` varchar(50) DEFAULT NULL COMMENT '处理人',
  `metadata` json COMMENT '额外元数据',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_record_id` (`record_id`),
  KEY `idx_monitor_id` (`monitor_id`),
  KEY `idx_accident_type` (`accident_type`),
  KEY `idx_severity` (`severity`),
  KEY `idx_status` (`status`),
  KEY `idx_detection_time` (`detection_time`),
  CONSTRAINT `fk_accident_monitor` FOREIGN KEY (`monitor_id`) REFERENCES `monitor` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='事故记录表';

-- 违规记录表
CREATE TABLE `violation_record` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '违规记录ID',
  `monitor_id` int NOT NULL COMMENT '监控点ID',
  `violation_type` varchar(50) NOT NULL COMMENT '违规类型(speeding/wrong_way/illegal_lane_change/illegal_parking)',
  `vehicle_id` varchar(100) DEFAULT NULL COMMENT '车辆ID/车牌号',
  `track_id` int DEFAULT NULL COMMENT '追踪ID',
  `violation_time` datetime NOT NULL COMMENT '违规时间',
  `location` varchar(200) DEFAULT NULL COMMENT '违规位置',
  `speed` float DEFAULT NULL COMMENT '车辆速度(km/h)',
  `speed_limit` float DEFAULT NULL COMMENT '限速值(km/h)',
  `evidence_image` varchar(500) DEFAULT NULL COMMENT '证据图片路径',
  `evidence_video` varchar(500) DEFAULT NULL COMMENT '证据视频路径',
  `confidence` float DEFAULT NULL COMMENT '检测置信度',
  `severity` varchar(20) DEFAULT 'medium' COMMENT '严重程度(low/medium/high)',
  `status` varchar(20) DEFAULT 'pending' COMMENT '处理状态(pending/confirmed/dismissed)',
  `description` text COMMENT '违规描述',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_monitor_id` (`monitor_id`),
  KEY `idx_violation_type` (`violation_type`),
  KEY `idx_violation_time` (`violation_time`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_violation_monitor` FOREIGN KEY (`monitor_id`) REFERENCES `monitor` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='违规记录表';

-- =====================================================
-- 6. 系统配置表
-- =====================================================

-- 算法配置表
CREATE TABLE `algorithm_config` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `monitor_id` int DEFAULT NULL COMMENT '监控点ID(NULL表示全局配置)',
  `config_type` varchar(50) NOT NULL COMMENT '配置类型(tracking/detection/accident/violation)',
  `algorithm_name` varchar(100) NOT NULL COMMENT '算法名称',
  `config_data` json NOT NULL COMMENT '配置参数(JSON格式)',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否激活',
  `priority` int DEFAULT 0 COMMENT '优先级',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_monitor_id` (`monitor_id`),
  KEY `idx_config_type` (`config_type`),
  KEY `idx_algorithm_name` (`algorithm_name`),
  CONSTRAINT `fk_config_monitor` FOREIGN KEY (`monitor_id`) REFERENCES `monitor` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='算法配置表';

-- 系统配置表
CREATE TABLE `system_config` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text NOT NULL COMMENT '配置值',
  `config_type` varchar(50) DEFAULT 'string' COMMENT '配置类型(string/int/float/bool/json)',
  `description` varchar(500) DEFAULT NULL COMMENT '配置描述',
  `category` varchar(50) DEFAULT 'general' COMMENT '配置分类',
  `is_system` tinyint(1) DEFAULT 0 COMMENT '是否系统配置',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`),
  KEY `idx_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 模型管理表
CREATE TABLE `model_management` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '模型ID',
  `model_name` varchar(100) NOT NULL COMMENT '模型名称',
  `model_path` varchar(500) NOT NULL COMMENT '模型文件路径',
  `model_type` varchar(50) NOT NULL COMMENT '模型类型(yolo/tracking/classification)',
  `model_version` varchar(20) DEFAULT NULL COMMENT '模型版本',
  `model_size` bigint DEFAULT NULL COMMENT '模型文件大小(字节)',
  `input_size` varchar(20) DEFAULT NULL COMMENT '输入尺寸',
  `classes` json DEFAULT NULL COMMENT '支持的类别(JSON格式)',
  `accuracy` float DEFAULT NULL COMMENT '模型精度',
  `speed` float DEFAULT NULL COMMENT '推理速度(ms)',
  `description` text COMMENT '模型描述',
  `is_active` tinyint(1) DEFAULT 0 COMMENT '是否当前使用',
  `upload_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
  `upload_by` varchar(50) DEFAULT NULL COMMENT '上传人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_model_name` (`model_name`),
  KEY `idx_model_type` (`model_type`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='模型管理表';

-- =====================================================
-- 7. 警报和通知表
-- =====================================================

-- 警报表
CREATE TABLE `alarm` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '警报ID',
  `monitor_id` int NOT NULL COMMENT '监控点ID',
  `alarm_type` varchar(50) NOT NULL COMMENT '警报类型(detection/accident/violation/system)',
  `alarm_level` varchar(20) DEFAULT 'medium' COMMENT '警报级别(low/medium/high/critical)',
  `title` varchar(200) NOT NULL COMMENT '警报标题',
  `content` text COMMENT '警报内容',
  `location` varchar(200) DEFAULT NULL COMMENT '警报位置',
  `image_path` varchar(500) DEFAULT NULL COMMENT '相关图片路径',
  `video_path` varchar(500) DEFAULT NULL COMMENT '相关视频路径',
  `related_id` varchar(100) DEFAULT NULL COMMENT '关联记录ID',
  `status` varchar(20) DEFAULT 'active' COMMENT '状态(active/handled/dismissed)',
  `handled_by` varchar(50) DEFAULT NULL COMMENT '处理人',
  `handled_time` datetime DEFAULT NULL COMMENT '处理时间',
  `handle_note` text COMMENT '处理备注',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_monitor_id` (`monitor_id`),
  KEY `idx_alarm_type` (`alarm_type`),
  KEY `idx_alarm_level` (`alarm_level`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_alarm_monitor` FOREIGN KEY (`monitor_id`) REFERENCES `monitor` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='警报表';

-- 通知表
CREATE TABLE `notification` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '通知ID',
  `user_id` int DEFAULT NULL COMMENT '用户ID(NULL表示系统通知)',
  `title` varchar(200) NOT NULL COMMENT '通知标题',
  `content` text NOT NULL COMMENT '通知内容',
  `type` varchar(50) DEFAULT 'info' COMMENT '通知类型(info/warning/error/success)',
  `priority` int DEFAULT 0 COMMENT '优先级(数字越大优先级越高)',
  `is_read` tinyint(1) DEFAULT 0 COMMENT '是否已读',
  `read_time` datetime DEFAULT NULL COMMENT '阅读时间',
  `related_type` varchar(50) DEFAULT NULL COMMENT '关联类型',
  `related_id` varchar(100) DEFAULT NULL COMMENT '关联ID',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_is_read` (`is_read`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_notification_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知表';

-- =====================================================
-- 8. 统计和日志表
-- =====================================================

-- 交通统计表
CREATE TABLE `traffic_statistics` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `monitor_id` int NOT NULL COMMENT '监控点ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `stat_hour` int DEFAULT NULL COMMENT '统计小时(0-23)',
  `vehicle_count` int DEFAULT 0 COMMENT '车辆总数',
  `car_count` int DEFAULT 0 COMMENT '小汽车数量',
  `truck_count` int DEFAULT 0 COMMENT '卡车数量',
  `bus_count` int DEFAULT 0 COMMENT '公交车数量',
  `motorcycle_count` int DEFAULT 0 COMMENT '摩托车数量',
  `avg_speed` float DEFAULT NULL COMMENT '平均速度(km/h)',
  `max_speed` float DEFAULT NULL COMMENT '最高速度(km/h)',
  `min_speed` float DEFAULT NULL COMMENT '最低速度(km/h)',
  `traffic_density` float DEFAULT NULL COMMENT '交通密度',
  `congestion_level` varchar(20) DEFAULT 'normal' COMMENT '拥堵级别(smooth/normal/congested/severe)',
  `accident_count` int DEFAULT 0 COMMENT '事故数量',
  `violation_count` int DEFAULT 0 COMMENT '违规数量',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_monitor_date_hour` (`monitor_id`, `stat_date`, `stat_hour`),
  KEY `idx_stat_date` (`stat_date`),
  KEY `idx_stat_hour` (`stat_hour`),
  CONSTRAINT `fk_traffic_monitor` FOREIGN KEY (`monitor_id`) REFERENCES `monitor` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交通统计表';

-- 系统日志表
CREATE TABLE `system_log` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `log_level` varchar(20) NOT NULL COMMENT '日志级别(DEBUG/INFO/WARNING/ERROR/CRITICAL)',
  `module` varchar(100) DEFAULT NULL COMMENT '模块名称',
  `action` varchar(100) DEFAULT NULL COMMENT '操作名称',
  `message` text NOT NULL COMMENT '日志消息',
  `user_id` int DEFAULT NULL COMMENT '用户ID',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `request_url` varchar(500) DEFAULT NULL COMMENT '请求URL',
  `request_method` varchar(10) DEFAULT NULL COMMENT '请求方法',
  `response_code` int DEFAULT NULL COMMENT '响应代码',
  `execution_time` float DEFAULT NULL COMMENT '执行时间(秒)',
  `stack_trace` text COMMENT '堆栈跟踪',
  `extra_data` json COMMENT '额外数据',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_log_level` (`log_level`),
  KEY `idx_module` (`module`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_log_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统日志表';

-- =====================================================
-- 9. 性能监控表
-- =====================================================

-- 性能指标表
CREATE TABLE `performance_metrics` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '指标ID',
  `metric_type` varchar(50) NOT NULL COMMENT '指标类型(system/detection/tracking/accident)',
  `metric_name` varchar(100) NOT NULL COMMENT '指标名称',
  `metric_value` float NOT NULL COMMENT '指标值',
  `metric_unit` varchar(20) DEFAULT NULL COMMENT '指标单位',
  `monitor_id` int DEFAULT NULL COMMENT '监控点ID',
  `timestamp` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
  `metadata` json COMMENT '额外元数据',
  PRIMARY KEY (`id`),
  KEY `idx_metric_type` (`metric_type`),
  KEY `idx_metric_name` (`metric_name`),
  KEY `idx_monitor_id` (`monitor_id`),
  KEY `idx_timestamp` (`timestamp`),
  CONSTRAINT `fk_metrics_monitor` FOREIGN KEY (`monitor_id`) REFERENCES `monitor` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='性能指标表';

-- 实时数据表
CREATE TABLE `realtime_data` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '数据ID',
  `monitor_id` int NOT NULL COMMENT '监控点ID',
  `data_type` varchar(50) NOT NULL COMMENT '数据类型(detection/tracking/traffic)',
  `data_content` json NOT NULL COMMENT '数据内容(JSON格式)',
  `timestamp` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  PRIMARY KEY (`id`),
  KEY `idx_monitor_id` (`monitor_id`),
  KEY `idx_data_type` (`data_type`),
  KEY `idx_timestamp` (`timestamp`),
  KEY `idx_expire_time` (`expire_time`),
  CONSTRAINT `fk_realtime_monitor` FOREIGN KEY (`monitor_id`) REFERENCES `monitor` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='实时数据表';

-- WebSocket会话表
CREATE TABLE `websocket_session` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '会话ID',
  `session_id` varchar(100) NOT NULL COMMENT '会话标识',
  `user_id` int DEFAULT NULL COMMENT '用户ID',
  `client_ip` varchar(45) DEFAULT NULL COMMENT '客户端IP',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `connected_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '连接时间',
  `last_activity` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后活动时间',
  `status` varchar(20) DEFAULT 'active' COMMENT '状态(active/disconnected)',
  `subscribed_monitors` json COMMENT '订阅的监控点(JSON格式)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_session_id` (`session_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_websocket_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='WebSocket会话表';

-- =====================================================
-- 10. 文件管理表
-- =====================================================

-- 文件上传表
CREATE TABLE `file_upload` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '文件ID',
  `file_name` varchar(255) NOT NULL COMMENT '文件名',
  `original_name` varchar(255) NOT NULL COMMENT '原始文件名',
  `file_path` varchar(500) NOT NULL COMMENT '文件路径',
  `file_size` bigint NOT NULL COMMENT '文件大小(字节)',
  `file_type` varchar(100) NOT NULL COMMENT '文件类型',
  `mime_type` varchar(100) DEFAULT NULL COMMENT 'MIME类型',
  `file_hash` varchar(64) DEFAULT NULL COMMENT '文件哈希值',
  `upload_type` varchar(50) DEFAULT 'manual' COMMENT '上传类型(manual/auto/system)',
  `related_type` varchar(50) DEFAULT NULL COMMENT '关联类型',
  `related_id` varchar(100) DEFAULT NULL COMMENT '关联ID',
  `user_id` int DEFAULT NULL COMMENT '上传用户ID',
  `status` varchar(20) DEFAULT 'active' COMMENT '状态(active/deleted/archived)',
  `upload_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
  `access_count` int DEFAULT 0 COMMENT '访问次数',
  `last_access` datetime DEFAULT NULL COMMENT '最后访问时间',
  PRIMARY KEY (`id`),
  KEY `idx_file_name` (`file_name`),
  KEY `idx_file_type` (`file_type`),
  KEY `idx_upload_type` (`upload_type`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_upload_time` (`upload_time`),
  CONSTRAINT `fk_file_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件上传表';

-- API访问日志表
CREATE TABLE `api_access_log` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` int DEFAULT NULL COMMENT '用户ID',
  `api_path` varchar(200) NOT NULL COMMENT 'API路径',
  `method` varchar(10) NOT NULL COMMENT '请求方法',
  `request_params` json COMMENT '请求参数',
  `response_code` int NOT NULL COMMENT '响应代码',
  `response_time` float NOT NULL COMMENT '响应时间(毫秒)',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `error_message` text COMMENT '错误信息',
  `access_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '访问时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_api_path` (`api_path`),
  KEY `idx_method` (`method`),
  KEY `idx_response_code` (`response_code`),
  KEY `idx_access_time` (`access_time`),
  CONSTRAINT `fk_api_log_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API访问日志表';

-- =====================================================
-- 11. 插入测试数据
-- =====================================================

-- 插入用户数据
INSERT INTO `user` (`username`, `password`, `email`, `grade`, `status`, `login_count`) VALUES
('admin', '123456', '<EMAIL>', 'admin', 1, 0),
('operator', '123456', '<EMAIL>', 'operator', 1, 0),
('viewer', '123456', '<EMAIL>', 'user', 1, 0);

-- 插入监控点数据（杭州-千岛湖高速全程）
INSERT INTO `monitor` (`name`, `location`, `highway_section`, `camera_position`, `latitude`, `longitude`, `threshold`, `person`, `url`, `create_by`) VALUES
('杭州收费站入口', '杭州市西湖区', 'K0+000', '收费站入口右侧', 30.2741, 120.1551, 20, '张三', 'rtsp://192.168.1.101:554/stream1', 'admin'),
('杭州西湖服务区', '杭州市西湖区', 'K15+500', '服务区出入口', 30.2456, 120.1234, 25, '李四', 'rtsp://192.168.1.102:554/stream1', 'admin'),
('富阳互通', '杭州市富阳区', 'K32+200', '互通主线桥', 30.0498, 119.9512, 30, '王五', 'rtsp://192.168.1.103:554/stream1', 'admin'),
('富阳服务区', '杭州市富阳区', 'K45+800', '服务区停车场', 30.0123, 119.8765, 20, '赵六', 'rtsp://192.168.1.104:554/stream1', 'admin'),
('桐庐互通', '杭州市桐庐县', 'K68+300', '互通匝道口', 29.7971, 119.6789, 25, '钱七', 'rtsp://192.168.1.105:554/stream1', 'admin'),
('桐庐服务区', '杭州市桐庐县', 'K78+600', '服务区加油站', 29.7654, 119.6234, 30, '孙八', 'rtsp://192.168.1.106:554/stream1', 'admin'),
('建德互通', '杭州市建德市', 'K95+100', '互通主线', 29.4765, 119.2789, 20, '周九', 'rtsp://192.168.1.107:554/stream1', 'admin'),
('建德服务区', '杭州市建德市', 'K108+400', '服务区餐厅', 29.4321, 119.2345, 25, '吴十', 'rtsp://192.168.1.108:554/stream1', 'admin'),
('寿昌互通', '杭州市建德市', 'K125+700', '互通收费站', 29.3456, 119.1567, 30, '郑一', 'rtsp://192.168.1.109:554/stream1', 'admin'),
('淳安互通', '杭州市淳安县', 'K142+300', '互通天桥', 29.6098, 119.0432, 20, '陈二', 'rtsp://192.168.1.110:554/stream1', 'admin'),
('千岛湖服务区', '杭州市淳安县', 'K158+900', '服务区观景台', 29.5876, 118.9876, 25, '刘三', 'rtsp://192.168.1.111:554/stream1', 'admin'),
('千岛湖收费站', '杭州市淳安县', 'K175+000', '收费站出口', 29.5432, 118.9234, 30, '黄四', 'rtsp://192.168.1.112:554/stream1', 'admin'),
('隧道入口监控', '杭州市建德市', 'K88+200', '隧道入口上方', 29.4987, 119.3456, 15, '林五', 'rtsp://*************:554/stream1', 'admin'),
('隧道出口监控', '杭州市建德市', 'K92+800', '隧道出口上方', 29.4876, 119.3123, 15, '徐六', 'rtsp://*************:554/stream1', 'admin'),
('应急车道监控', '杭州市桐庐县', 'K72+500', '应急车道中段', 29.7789, 119.6567, 35, '朱七', 'rtsp://*************:554/stream1', 'admin');

-- 插入系统配置数据
INSERT INTO `system_config` (`config_key`, `config_value`, `config_type`, `description`, `category`) VALUES
('system.name', '基于Yolov8与ByteTrack的高速公路智慧监控平台', 'string', '系统名称', 'general'),
('system.version', '2.0.0', 'string', '系统版本', 'general'),
('detection.default_conf_threshold', '0.4', 'float', '默认检测置信度阈值', 'detection'),
('detection.default_iou_threshold', '0.5', 'float', '默认IOU阈值', 'detection'),
('detection.max_concurrent_tasks', '10', 'int', '最大并发检测任务数', 'detection'),
('tracking.default_algorithm', 'bytetrack', 'string', '默认追踪算法', 'tracking'),
('tracking.max_targets', '100', 'int', '最大追踪目标数', 'tracking'),
('accident.detection_enabled', 'true', 'bool', '是否启用事故检测', 'accident'),
('accident.alert_threshold', '0.7', 'float', '事故警报阈值', 'accident'),
('notification.email_enabled', 'false', 'bool', '是否启用邮件通知', 'notification'),
('notification.webhook_enabled', 'false', 'bool', '是否启用Webhook通知', 'notification'),
('storage.max_file_size', '104857600', 'int', '最大文件上传大小(100MB)', 'storage'),
('storage.retention_days', '30', 'int', '数据保留天数', 'storage'),
('performance.log_level', 'INFO', 'string', '日志级别', 'performance'),
('performance.metrics_interval', '60', 'int', '性能指标收集间隔(秒)', 'performance');

-- 插入模型管理数据
INSERT INTO `model_management` (`model_name`, `model_path`, `model_type`, `model_version`, `model_size`, `input_size`, `classes`, `accuracy`, `speed`, `description`, `is_active`) VALUES
('yolov8n.pt', './models/yolov8n.pt', 'yolo', '8.0', 6237728, '640x640', '["person", "bicycle", "car", "motorcycle", "airplane", "bus", "train", "truck"]', 0.85, 2.5, 'YOLOv8 Nano模型，速度快，适合实时检测', 1),
('yolov8s.pt', './models/yolov8s.pt', 'yolo', '8.0', 21518272, '640x640', '["person", "bicycle", "car", "motorcycle", "airplane", "bus", "train", "truck"]', 0.89, 4.2, 'YOLOv8 Small模型，平衡速度和精度', 0),
('yolov8m.pt', './models/yolov8m.pt', 'yolo', '8.0', 49681536, '640x640', '["person", "bicycle", "car", "motorcycle", "airplane", "bus", "train", "truck"]', 0.92, 8.1, 'YOLOv8 Medium模型，高精度检测', 0),
('car.pt', './models/car.pt', 'yolo', '8.0', 14567890, '640x640', '["car", "truck", "bus", "motorcycle"]', 0.94, 3.8, '专门针对车辆检测优化的模型', 0);

-- 插入警报数据
INSERT INTO `alarm` (`monitor_id`, `alarm_type`, `alarm_level`, `title`, `content`, `location`, `status`, `create_time`) VALUES
(1, 'detection', 'medium', '车流量异常', '杭州收费站入口检测到异常车流量，当前车辆数量超过阈值', '杭州收费站入口', 'active', '2024-12-24 08:30:00'),
(3, 'accident', 'high', '疑似交通事故', '富阳互通检测到疑似车辆碰撞事故，需要立即处理', '富阳互通主线桥', 'active', '2024-12-24 09:15:00'),
(5, 'violation', 'medium', '超速违规', '桐庐互通检测到车辆超速行驶，速度120km/h，限速100km/h', '桐庐互通匝道口', 'handled', '2024-12-24 10:20:00'),
(8, 'system', 'low', '设备离线', '建德服务区监控设备连接异常，请检查网络连接', '建德服务区餐厅', 'active', '2024-12-24 11:45:00'),
(12, 'detection', 'high', '拥堵预警', '千岛湖收费站出现严重拥堵，车辆排队长度超过500米', '千岛湖收费站出口', 'active', '2024-12-24 14:30:00'),
(13, 'accident', 'critical', '隧道事故', '隧道内检测到严重交通事故，已通知应急救援', '隧道入口上方', 'handled', '2024-12-24 16:20:00'),
(15, 'violation', 'high', '应急车道占用', '应急车道检测到非紧急情况下的车辆占用', '应急车道中段', 'active', '2024-12-24 17:10:00');

-- 插入交通统计数据
INSERT INTO `traffic_statistics` (`monitor_id`, `stat_date`, `stat_hour`, `vehicle_count`, `car_count`, `truck_count`, `bus_count`, `avg_speed`, `max_speed`, `congestion_level`) VALUES
(1, '2024-12-24', 8, 245, 198, 35, 12, 85.5, 120.0, 'normal'),
(1, '2024-12-24', 9, 312, 251, 45, 16, 78.2, 115.0, 'congested'),
(1, '2024-12-24', 10, 189, 152, 28, 9, 92.1, 125.0, 'smooth'),
(3, '2024-12-24', 8, 156, 125, 22, 9, 88.7, 118.0, 'normal'),
(3, '2024-12-24', 9, 203, 162, 31, 10, 82.3, 112.0, 'normal'),
(3, '2024-12-24', 10, 134, 108, 19, 7, 95.4, 128.0, 'smooth'),
(5, '2024-12-24', 8, 178, 143, 26, 9, 91.2, 122.0, 'normal'),
(5, '2024-12-24', 9, 234, 187, 35, 12, 85.8, 119.0, 'normal'),
(5, '2024-12-24', 10, 167, 134, 24, 9, 93.6, 126.0, 'smooth'),
(12, '2024-12-24', 14, 456, 365, 67, 24, 65.2, 95.0, 'severe'),
(12, '2024-12-24', 15, 389, 312, 56, 21, 72.8, 105.0, 'congested'),
(12, '2024-12-24', 16, 298, 239, 43, 16, 81.5, 115.0, 'normal');

-- 插入算法配置数据
INSERT INTO `algorithm_config` (`monitor_id`, `config_type`, `algorithm_name`, `config_data`, `create_by`) VALUES
(NULL, 'detection', 'yolov8', '{"conf_threshold": 0.4, "iou_threshold": 0.5, "max_det": 300, "classes": [2, 3, 5, 7]}', 'admin'),
(NULL, 'tracking', 'bytetrack', '{"track_thresh": 0.5, "track_buffer": 30, "match_thresh": 0.8, "frame_rate": 30}', 'admin'),
(NULL, 'accident', 'collision_detection', '{"sensitivity": 0.7, "min_speed_diff": 20, "time_window": 2.0}', 'admin'),
(1, 'detection', 'yolov8', '{"conf_threshold": 0.3, "iou_threshold": 0.4, "max_det": 500}', 'admin'),
(12, 'tracking', 'bytetrack', '{"track_thresh": 0.6, "track_buffer": 50, "match_thresh": 0.9}', 'admin');

-- 插入事故记录数据
INSERT INTO `accident_record` (`record_id`, `monitor_id`, `accident_type`, `severity`, `location_x`, `location_y`, `description`, `confidence`, `status`, `detection_time`) VALUES
('ACC_20241224_001', 3, 'collision', 'high', 320.5, 240.8, '两车追尾事故，无人员伤亡，车辆轻微损坏', 0.89, 'resolved', '2024-12-24 09:15:23'),
('ACC_20241224_002', 13, 'sudden_stop', 'critical', 450.2, 180.6, '隧道内车辆紧急制动，后方车辆连环追尾', 0.95, 'resolved', '2024-12-24 16:20:15'),
('ACC_20241224_003', 8, 'congestion', 'medium', 280.1, 320.4, '服务区出口拥堵，车辆缓慢通行', 0.76, 'active', '2024-12-24 18:30:45'),
('ACC_20241224_004', 15, 'emergency', 'low', 150.8, 200.3, '应急车道临时停车，已处理完毕', 0.82, 'resolved', '2024-12-24 12:45:30');

-- 插入违规记录数据
INSERT INTO `violation_record` (`monitor_id`, `violation_type`, `track_id`, `violation_time`, `location`, `speed`, `speed_limit`, `confidence`, `severity`, `status`, `description`) VALUES
(5, 'speeding', 1001, '2024-12-24 10:20:15', '桐庐互通匝道口', 120.5, 100.0, 0.92, 'medium', 'confirmed', '车辆超速20.5km/h'),
(15, 'illegal_parking', 1002, '2024-12-24 17:10:30', '应急车道中段', 0.0, NULL, 0.88, 'high', 'pending', '非紧急情况占用应急车道'),
(7, 'wrong_way', 1003, '2024-12-24 15:45:20', '建德互通主线', 85.2, 120.0, 0.85, 'high', 'confirmed', '车辆逆向行驶'),
(2, 'illegal_lane_change', 1004, '2024-12-24 11:30:45', '杭州西湖服务区出入口', 78.5, 80.0, 0.79, 'low', 'dismissed', '违规变道，但情况轻微');

-- 插入追踪性能数据
INSERT INTO `tracking_performance` (`monitor_id`, `algorithm_name`, `stat_date`, `stat_hour`, `total_tracks`, `successful_tracks`, `lost_tracks`, `avg_track_length`, `fps`, `cpu_usage`, `memory_usage`, `accuracy`) VALUES
(1, 'bytetrack', '2024-12-24', 8, 245, 238, 7, 45.6, 28.5, 65.2, 78.9, 0.97),
(1, 'bytetrack', '2024-12-24', 9, 312, 301, 11, 42.3, 27.8, 68.4, 82.1, 0.96),
(3, 'bytetrack', '2024-12-24', 8, 156, 152, 4, 38.9, 29.2, 62.7, 75.3, 0.97),
(5, 'deepsort', '2024-12-24', 8, 178, 169, 9, 52.1, 22.4, 72.8, 85.6, 0.95),
(12, 'bytetrack', '2024-12-24', 14, 456, 432, 24, 35.7, 25.1, 78.9, 92.4, 0.95);

-- 插入通知数据
INSERT INTO `notification` (`user_id`, `title`, `content`, `type`, `priority`, `is_read`) VALUES
(1, '系统启动通知', '基于Yolov8与ByteTrack的高速公路智慧监控平台已成功启动，所有模块运行正常', 'success', 1, 0),
(1, '事故警报', '富阳互通检测到交通事故，请及时处理', 'warning', 3, 0),
(2, '设备离线提醒', '建德服务区监控设备连接异常，请检查设备状态', 'error', 2, 1),
(NULL, '系统维护通知', '系统将于今晚23:00-01:00进行例行维护，期间可能影响部分功能', 'info', 1, 0);

-- =====================================================
-- 12. 创建索引优化
-- =====================================================

-- 为高频查询字段创建复合索引
CREATE INDEX `idx_detection_task_status_time` ON `detection_task` (`status`, `create_time`);
CREATE INDEX `idx_tracking_target_monitor_status` ON `tracking_target` (`monitor_id`, `status`, `last_seen`);
CREATE INDEX `idx_accident_record_time_type` ON `accident_record` (`detection_time`, `accident_type`);
CREATE INDEX `idx_alarm_status_level_time` ON `alarm` (`status`, `alarm_level`, `create_time`);
CREATE INDEX `idx_traffic_stats_date_monitor` ON `traffic_statistics` (`stat_date`, `monitor_id`);

-- =====================================================
-- 13. 创建视图
-- =====================================================

-- 监控点状态视图
CREATE VIEW `v_monitor_status` AS
SELECT
    m.id,
    m.name,
    m.location,
    m.highway_section,
    m.connection_status,
    COUNT(DISTINCT dt.id) as active_tasks,
    COUNT(DISTINCT tt.id) as active_targets,
    COUNT(DISTINCT ar.id) as today_accidents,
    COUNT(DISTINCT a.id) as active_alarms
FROM monitor m
LEFT JOIN detection_task dt ON m.id = dt.monitor_id AND dt.status = 'running'
LEFT JOIN tracking_target tt ON m.id = tt.monitor_id AND tt.status = 'active'
LEFT JOIN accident_record ar ON m.id = ar.monitor_id AND DATE(ar.detection_time) = CURDATE()
LEFT JOIN alarm a ON m.id = a.monitor_id AND a.status = 'active'
GROUP BY m.id;

-- 系统概览视图
CREATE VIEW `v_system_overview` AS
SELECT
    (SELECT COUNT(*) FROM monitor WHERE connection_status = 'online') as online_monitors,
    (SELECT COUNT(*) FROM monitor) as total_monitors,
    (SELECT COUNT(*) FROM detection_task WHERE status = 'running') as running_tasks,
    (SELECT COUNT(*) FROM tracking_target WHERE status = 'active') as active_targets,
    (SELECT COUNT(*) FROM accident_record WHERE DATE(detection_time) = CURDATE()) as today_accidents,
    (SELECT COUNT(*) FROM alarm WHERE status = 'active') as active_alarms,
    (SELECT COUNT(*) FROM user WHERE status = 1) as active_users;

-- =====================================================
-- 数据库结构创建完成
-- =====================================================
