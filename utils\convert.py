import cv2
import numpy as np
import random

def plot_one_box(x, img, color=None, label=None, line_thickness=None):
    """在图像上绘制边界框
    
    Args:
        x: 边界框坐标 [x1, y1, x2, y2]
        img: 要绘制的图像
        color: 边界框颜色
        label: 标签文本
        line_thickness: 线条粗细
    """
    tl = line_thickness or round(0.002 * (img.shape[0] + img.shape[1]) / 2) + 1
    color = color or [random.randint(0, 255) for _ in range(3)]
    c1, c2 = (int(x[0]), int(x[1])), (int(x[2]), int(x[3]))
    cv2.rectangle(img, c1, c2, color, thickness=tl, lineType=cv2.LINE_AA)
    
    if label:
        tf = max(tl - 1, 1)
        t_size = cv2.getTextSize(label, 0, fontScale=tl / 3, thickness=tf)[0]
        c2 = c1[0] + t_size[0], c1[1] - t_size[1] - 3
        cv2.rectangle(img, c1, c2, color, -1, cv2.LINE_AA)
        cv2.putText(img, label, (c1[0], c1[1] - 2), 0, tl / 3, [225, 255, 255], thickness=tf, lineType=cv2.LINE_AA)

def ResizePicture(image, target_size):
    """调整图片大小，保持宽高比
    
    Args:
        image: 输入图像
        target_size: 目标尺寸 (width, height)
    
    Returns:
        调整后的图像
    """
    ih, iw = image.shape[:2]
    w, h = target_size
    scale = min(w/iw, h/ih)
    nw = int(iw * scale)
    nh = int(ih * scale)
    
    image = cv2.resize(image, (nw, nh), interpolation=cv2.INTER_CUBIC)
    new_image = np.zeros((h, w, 3), np.uint8)
    new_image.fill(128)
    
    # 将调整后的图像放在中心位置
    dx = (w - nw) // 2
    dy = (h - nh) // 2
    new_image[dy:dy+nh, dx:dx+nw] = image
    
    return new_image