# -*- coding: utf-8 -*-
# @Description : 违规行为检测实现

import cv2
import numpy as np
import time
from collections import defaultdict, deque

class ViolationDetector:
    """
    违规行为检测类，负责检测和记录交通违规行为，包括：
    1. 超速行为检测
    2. 逆行检测
    3. 车辆变道检测
    """
    def __init__(self, fps=30, pixels_per_meter=10, speed_limit=120, trajectory_buffer=30):
        # 基本参数
        self.fps = fps
        self.pixels_per_meter = pixels_per_meter
        self.speed_limit = speed_limit
        self.trajectory_buffer = trajectory_buffer
        
        # 检测开关
        self.enable_speed_detection = True
        self.enable_direction_detection = True
        self.enable_lane_change_detection = True
        
        # 车道线变化阈值
        self.lane_change_threshold = 3
        
        # 存储各类违规行为
        self.violations = defaultdict(list)
        
        # 存储车辆轨迹，用于检测违规行为
        self.trajectories = defaultdict(lambda: deque(maxlen=self.trajectory_buffer))
        
        # 检测方向，默认为右侧驾驶，从左到右为正向
        self.main_direction = 'right'
        self.direction_vector = np.array([1, 0])
        
        # 存储车道线位置（可由用户绘制或自动检测）
        self.lanes = []
        
        # 记录当前帧
        self.current_frame = None
        
        # 检测区域（ROI）
        self.detection_roi = None
        
        # 违规记录
        self.violation_records = defaultdict(list)  # 记录违规的车辆ID和违规类型
        self.violation_count = {"超速": 0, "逆行": 0, "违规变道": 0}
        
    def update_trajectory(self, object_id, position, timestamp, class_id, speed=None):
        """
        更新车辆轨迹数据
        """
        x, y = position
        data = {"position": position, "timestamp": timestamp, "class_id": class_id}
        if speed is not None:
            data["speed"] = speed
            
        self.trajectories[object_id].append(data)
        
    def detect_violations(self, frame, current_objects, speeds=None):
        """
        检测当前帧中的违规行为
        """
        self.current_frame = frame
        current_violations = []
        
        # 逐个对象检测违规
        for obj_id, obj_data in current_objects.items():
            # 获取对象位置和类别
            pos = obj_data.get("position")
            class_id = obj_data.get("class_id")
            
            if pos and class_id in [2, 3, 5, 7]:  # 只检测车辆
                # 检查是否有足够的轨迹数据
                if obj_id in self.trajectories and len(self.trajectories[obj_id]) > 5:
                    # 1. 检测超速行为
                    if speeds and obj_id in speeds and speeds[obj_id] > self.speed_limit and self.enable_speed_detection:
                        self._record_violation(obj_id, "超速", speeds[obj_id])
                        current_violations.append((obj_id, "超速", speeds[obj_id]))
                    
                    # 2. 检测逆行行为
                    if self._check_wrong_direction(obj_id) and self.enable_direction_detection:
                        self._record_violation(obj_id, "逆行")
                        current_violations.append((obj_id, "逆行"))
                    
                    # 3. 检测违规变道
                    if self.lanes and self._check_illegal_lane_change(obj_id) and self.enable_lane_change_detection:
                        self._record_violation(obj_id, "违规变道")
                        current_violations.append((obj_id, "违规变道"))
                        
        return current_violations
    
    def _check_wrong_direction(self, obj_id):
        """
        检查车辆是否逆行
        """
        if len(self.trajectories[obj_id]) < 10:  # 需要足够的历史数据才能确定方向
            return False
            
        # 计算车辆移动矢量
        start_pos = self.trajectories[obj_id][0]["position"]
        current_pos = self.trajectories[obj_id][-1]["position"]
        
        movement_vector = np.array(current_pos) - np.array(start_pos)
        if np.linalg.norm(movement_vector) < 20:  # 移动距离太小，忽略
            return False
            
        # 计算与主方向的夹角
        cos_angle = np.dot(movement_vector, self.direction_vector) / (np.linalg.norm(movement_vector) * np.linalg.norm(self.direction_vector))
        
        # 若夹角大于120度，判定为逆行
        return cos_angle < -0.5  # cos(120°) = -0.5
    
    def _check_illegal_lane_change(self, obj_id):
        """
        检查车辆是否违规变道 - 改进版，不依赖预设车道线
        """
        if len(self.trajectories[obj_id]) < 15:
            return False

        positions = [data["position"] for data in self.trajectories[obj_id]]

        # 如果有预设车道线，使用原有逻辑
        if self.lanes:
            lane_crossings = 0
            last_lane = None

            for pos in positions:
                current_lane = self._get_lane(pos)
                if last_lane is not None and current_lane != last_lane:
                    lane_crossings += 1
                last_lane = current_lane

            return lane_crossings >= self.lane_change_threshold

        # 没有预设车道线时，使用轨迹分析检测急剧横向移动
        return self._detect_rapid_lateral_movement(positions)
    
    def _get_lane(self, position):
        """
        根据位置确定车道
        """
        x, y = position
        for i, lane in enumerate(self.lanes):
            if lane[0] <= y <= lane[1]:
                return i
        return -1  # 不在任何车道内

    def _detect_rapid_lateral_movement(self, positions):
        """
        检测急剧横向移动（违规变道的替代检测方法）
        """
        if len(positions) < 10:
            return False

        # 计算横向移动距离
        lateral_movements = []
        for i in range(1, len(positions)):
            prev_x, prev_y = positions[i-1]
            curr_x, curr_y = positions[i]

            # 计算横向移动（假设车辆主要沿x轴移动）
            lateral_movement = abs(curr_y - prev_y)
            lateral_movements.append(lateral_movement)

        # 检查是否有连续的大幅横向移动
        consecutive_large_movements = 0
        max_consecutive = 0

        for movement in lateral_movements:
            if movement > 15:  # 横向移动超过15像素认为是变道
                consecutive_large_movements += 1
                max_consecutive = max(max_consecutive, consecutive_large_movements)
            else:
                consecutive_large_movements = 0

        # 如果连续大幅横向移动超过5帧，认为是违规变道
        return max_consecutive >= 5
    
    def _record_violation(self, obj_id, violation_type, value=None):
        """
        记录违规行为
        """
        # 防止重复记录相同类型的违规
        for vid, vtype, _ in self.violation_records[obj_id]:
            if vtype == violation_type and time.time() - vid < 5:  # 5秒内不重复记录
                return
                
        # 记录新违规
        violation_id = time.time()
        self.violation_records[obj_id].append((violation_id, violation_type, value))
        self.violation_count[violation_type] += 1
        
    def draw_violations(self, frame, detections, speed_estimator=None):
        """
        在图像上绘制违规标记
        """
        if frame is None:
            return frame

        # 导入PIL库以支持中文
        from PIL import Image, ImageDraw, ImageFont
        import numpy as np
        
        # 创建一个PIL图像用于绘制文字
        pil_img = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(pil_img)
        
        # 尝试加载中文字体
        try:
            # 首先尝试微软雅黑字体
            font = ImageFont.truetype("msyh.ttc", 24)
        except:
            try:
                # 再尝试宋体
                font = ImageFont.truetype("simsun.ttc", 24)
            except:
                try:
                    # 如果仍然失败，尝试使用系统默认字体
                    font = ImageFont.truetype("simhei.ttf", 24)
                except:
                    # 无法加载任何中文字体，将回退到PIL默认字体
                    font = ImageFont.load_default()
        
        # 小字体用于统计信息
        try:
            small_font = ImageFont.truetype("msyh.ttc", 20)
        except:
            small_font = font
            
        for i, (_, box, _, class_id, tracker_id) in enumerate(detections):
            if tracker_id in self.violation_records and class_id in [2, 3, 5, 7]:  # 只处理车辆
                for _, violation_type, value in self.violation_records[tracker_id]:
                    x1, y1, x2, y2 = box
                    x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2)
                    
                    # 绘制违规标记
                    if violation_type == "超速":
                        cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 0, 255), 2)
                        text = f"超速: {value:.1f}km/h"
                        draw.text((x1, y1-30), text, font=font, fill=(255, 0, 0))
                        
                    elif violation_type == "逆行":
                        cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 165, 255), 2)
                        draw.text((x1, y1-30), "逆行", font=font, fill=(255, 165, 0))
                        
                    elif violation_type == "违规变道":
                        cv2.rectangle(frame, (x1, y1), (x2, y2), (255, 0, 255), 2)
                        draw.text((x1, y1-30), "违规变道", font=font, fill=(255, 0, 255))
        
        # 绘制统计信息
        draw.text((30, 30), f"超速: {self.violation_count['超速']}", font=small_font, fill=(255, 0, 0))
        draw.text((30, 60), f"逆行: {self.violation_count['逆行']}", font=small_font, fill=(255, 165, 0))
        draw.text((30, 90), f"违规变道: {self.violation_count['违规变道']}", font=small_font, fill=(255, 0, 255))
        draw.text((30, 120), f"总违规数: {sum(self.violation_count.values())}", font=small_font, fill=(0, 0, 255))
        
        # 转换回OpenCV格式
        frame = cv2.cvtColor(np.array(pil_img), cv2.COLOR_RGB2BGR)
        return frame
    
    def set_direction(self, direction):
        """
        设置主行驶方向
        direction: 'right', 'left', 'up', 'down'
        """
        self.main_direction = direction
        if direction == 'right':
            self.direction_vector = np.array([1, 0])
        elif direction == 'left':
            self.direction_vector = np.array([-1, 0])
        elif direction == 'up':
            self.direction_vector = np.array([0, -1])
        elif direction == 'down':
            self.direction_vector = np.array([0, 1])
    
    def set_lanes(self, lanes):
        """
        设置车道线
        lanes: 列表，每个元素为(y_min, y_max)表示车道的y坐标范围
        """
        self.lanes = lanes
    
    def set_detection_roi(self, roi):
        """
        设置检测区域
        roi: (x_min, y_min, x_max, y_max)
        """
        self.detection_roi = roi
    
    def set_speed_limit(self, limit):
        """
        设置速度限制
        """
        self.speed_limit = limit
    
    def reset_statistics(self):
        """
        重置统计数据
        """
        self.violation_count = {"超速": 0, "逆行": 0, "违规变道": 0}
        self.violation_records.clear()
