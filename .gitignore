# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
PIPFILE.lock

# PyTorch
*.pth
*.pt
*.onnx

# Virtual Environment
venv/
env/
ENV/
env.bak/
venv.bak/
venv_crack/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log
error_log.txt

# Database
*.db
*.sqlite
*.sqlite3

# Config files with sensitive data
*.env
config/*.env

# Uploads and temporary files
uploads/
temp/
static/after_img/
static/before_img/
results/
exports/
backups/

# Test files
test*.py
*test*.py
testImg.jpg
testVideo.mp4

# Model files (large files)
models/*.pt
models/*.onnx
*.pt
*.onnx
once2.pt
once2.onnx

# Cache
.cache/
*.cache

# Jupyter Notebook
.ipynb_checkpoints

# Documentation builds
docs/_build/

# PyInstaller
*.manifest
*.spec

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Flask
instance/
.webassets-cache

# Scrapy
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# Vercel
.vercel/

# Large files and directories
ultralytics/
qt_material/
pre_labels/
pre_result/

# Specific project files
query
python
0.16.0
0.25
8.0.0