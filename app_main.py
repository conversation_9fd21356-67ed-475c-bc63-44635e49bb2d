# -*- coding: utf-8 -*-
# @Description : 基于Yolov8与ByteTrack的高速公路智慧监控平台 - 主应用入口（整合版）
# @Date : 2025年6月20日

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(override=True, dotenv_path='config/end-back.env')

print("=" * 80)
print("基于Yolov8与ByteTrack的高速公路智慧监控平台")
print("=" * 80)

# 尝试使用增强版后端
try:
    print("正在启动增强版后端服务...")
    
    # 添加backend目录到Python路径
    backend_path = os.path.join(os.path.dirname(__file__), 'backend')
    if backend_path not in sys.path:
        sys.path.insert(0, backend_path)
    
    # 导入增强版应用
    from backend.app_enhanced import app, socketio
    from backend.websocket.events import register_websocket_events
    from backend.tasks.background_tasks import init_background_tasks
    
    # 注册WebSocket事件
    register_websocket_events(socketio)
    
    # 初始化后台任务
    task_manager = init_background_tasks(socketio)
    
    print("✓ 增强版后端服务加载成功")
    print("✓ WebSocket事件已注册")
    print("✓ 后台任务已启动")
    
    # 添加原有的路由到新应用
    print("正在集成原有功能...")
    
    # 导入原有的工具函数
    try:
        from utils.flask_utils import *
        from utils.rtsp_utils import RTSPUtils
        print("✓ 原有工具函数导入成功")
    except ImportError as e:
        print(f"⚠ 原有工具函数导入失败: {e}")
    
    # 导入原有的YOLO模型
    try:
        from ultralytics import YOLO
        import supervision as sv
        
        # 加载模型
        model_path = "./models/car.pt"
        if os.path.exists(model_path):
            model = YOLO(model_path)
            print(f"✓ YOLO模型加载成功: {model_path}")
        else:
            model = YOLO("yolov8n.pt")  # 使用默认模型
            print("⚠ 使用默认YOLOv8n模型")
        
        box_annotator = sv.BoxAnnotator(
            thickness=2,
            text_thickness=1,
            text_scale=0.5
        )
        
        # 将模型添加到应用上下文
        app.config['YOLO_MODEL'] = model
        app.config['BOX_ANNOTATOR'] = box_annotator
        
    except ImportError as e:
        print(f"⚠ YOLO模型加载失败: {e}")
    
    enhanced_mode = True
    
except ImportError as e:
    print(f"增强版后端加载失败: {e}")
    print("回退到原有系统...")
    
    # 使用原有的app.py
    try:
        from app import app, socketio
        print("✓ 原有系统加载成功")
        enhanced_mode = False
    except ImportError as e2:
        print(f"原有系统也无法加载: {e2}")
        print("创建最小化应用...")
        
        from flask import Flask, jsonify
        from flask_cors import CORS
        from datetime import datetime
        
        app = Flask(__name__, static_folder='static')
        app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'yolo-highway-monitoring-system-2025')
        app.config['JSON_AS_ASCII'] = False
        
        CORS(app, supports_credentials=True)
        
        @app.route('/')
        def index():
            return jsonify({
                'message': '基于Yolov8与ByteTrack的高速公路智慧监控平台（最小化模式）',
                'version': '1.0.0',
                'status': 'running (minimal mode)',
                'timestamp': datetime.now().isoformat(),
                'note': '请检查依赖包安装和配置文件'
            })
        
        @app.route('/health')
        def health():
            return jsonify({
                'status': 'healthy',
                'mode': 'minimal',
                'timestamp': datetime.now().isoformat()
            })
        
        socketio = None
        enhanced_mode = False

# 添加通用路由
@app.route('/api/system/info')
def system_info():
    """系统信息"""
    return jsonify({
        'system_name': '基于Yolov8与ByteTrack的高速公路智慧监控平台',
        'version': '1.0.0',
        'mode': 'enhanced' if enhanced_mode else 'legacy',
        'features': {
            'websocket': socketio is not None,
            'yolo_detection': 'YOLO_MODEL' in app.config,
            'database': True,
            'rtsp_support': True,
            'background_tasks': enhanced_mode
        },
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/test')
def test_api():
    """API测试"""
    return jsonify({
        'status': 'success',
        'message': 'API测试成功',
        'mode': 'enhanced' if enhanced_mode else 'legacy',
        'timestamp': datetime.now().isoformat()
    })

if __name__ == '__main__':
    # 获取配置
    host = os.getenv('HOST_NAME', '127.0.0.1')
    port = int(os.getenv('PORT', 5500))
    debug = os.getenv('DEBUG', 'false').lower() == 'true'
    
    print("-" * 80)
    print(f"服务地址: http://{host}:{port}")
    print(f"调试模式: {debug}")
    print(f"运行模式: {'增强版' if enhanced_mode else '兼容版'}")
    print(f"WebSocket: {'启用' if socketio else '禁用'}")
    print(f"环境配置: {os.path.abspath('config/end-back.env')}")
    print("-" * 80)
    print("系统启动中...")
    
    try:
        # 启动应用
        if socketio and enhanced_mode:
            # 使用SocketIO启动增强版
            socketio.run(
                app,
                host=host,
                port=port,
                debug=debug,
                allow_unsafe_werkzeug=True
            )
        else:
            # 使用基础Flask启动
            app.run(
                host=host,
                port=port,
                debug=debug
            )
    except KeyboardInterrupt:
        print("\n系统正在关闭...")
        if enhanced_mode:
            try:
                from backend.tasks.background_tasks import stop_background_tasks
                stop_background_tasks()
                print("✓ 后台任务已停止")
            except:
                pass
        print("✓ 系统已关闭")
    except Exception as e:
        print(f"系统启动失败: {e}")
        sys.exit(1)
