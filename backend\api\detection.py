# -*- coding: utf-8 -*-
# @Description : 检测相关API接口
# @Date : 2025年6月20日

import base64
import cv2
import numpy as np
import json
from datetime import datetime
from flask import request, jsonify
from werkzeug.utils import secure_filename
from . import api_v1
from .auth import token_required
from utils.response import success_response, error_response
from utils.database import get_db_connection
from services.yolo_service import YOLOService
from utils.file_utils import save_image, allowed_file

# 初始化YOLO服务
yolo_service = YOLOService()

@api_v1.route('/detection/image', methods=['POST'])
@token_required
def detect_image(current_user_id):
    """图像检测"""
    try:
        # 检查是否有文件上传
        if 'file' not in request.files:
            return error_response('没有上传文件')
        
        file = request.files['file']
        if file.filename == '':
            return error_response('没有选择文件')
        
        if not allowed_file(file.filename):
            return error_response('不支持的文件格式')
        
        # 读取图像
        image_data = file.read()
        nparr = np.frombuffer(image_data, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if image is None:
            return error_response('无法读取图像文件')
        
        # 获取检测参数
        conf_threshold = float(request.form.get('conf_threshold', 0.4))
        iou_threshold = float(request.form.get('iou_threshold', 0.5))
        show_labels = request.form.get('show_labels', 'true').lower() == 'true'
        
        # 执行检测
        results = yolo_service.detect_image(
            image, 
            conf_threshold=conf_threshold,
            iou_threshold=iou_threshold
        )
        
        # 保存原始图像
        original_path = save_image(image, 'original')
        
        # 绘制检测结果
        annotated_image = yolo_service.draw_detections(
            image, 
            results, 
            show_labels=show_labels
        )
        
        # 保存检测结果图像
        result_path = save_image(annotated_image, 'detection')
        
        # 统计检测结果
        detection_stats = yolo_service.get_detection_stats(results)
        
        return success_response({
            'original_image': original_path,
            'result_image': result_path,
            'detections': results['detections'],
            'statistics': detection_stats,
            'parameters': {
                'conf_threshold': conf_threshold,
                'iou_threshold': iou_threshold,
                'show_labels': show_labels
            }
        }, '图像检测成功')
        
    except Exception as e:
        return error_response(f'图像检测失败: {str(e)}')

@api_v1.route('/detection/base64', methods=['POST'])
@token_required
def detect_base64_image(current_user_id):
    """Base64图像检测"""
    try:
        data = request.get_json()
        image_data = data.get('image')
        
        if not image_data:
            return error_response('缺少图像数据')
        
        # 解码Base64图像
        if image_data.startswith('data:image'):
            image_data = image_data.split(',')[1]
        
        image_bytes = base64.b64decode(image_data)
        nparr = np.frombuffer(image_bytes, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if image is None:
            return error_response('无法解码图像数据')
        
        # 获取检测参数
        conf_threshold = float(data.get('conf_threshold', 0.4))
        iou_threshold = float(data.get('iou_threshold', 0.5))
        show_labels = data.get('show_labels', True)
        
        # 执行检测
        results = yolo_service.detect_image(
            image,
            conf_threshold=conf_threshold,
            iou_threshold=iou_threshold
        )
        
        # 绘制检测结果
        annotated_image = yolo_service.draw_detections(
            image,
            results,
            show_labels=show_labels
        )
        
        # 将结果图像转换为Base64
        _, buffer = cv2.imencode('.jpg', annotated_image)
        result_base64 = base64.b64encode(buffer).decode('utf-8')
        
        # 统计检测结果
        detection_stats = yolo_service.get_detection_stats(results)
        
        return success_response({
            'result_image': f'data:image/jpeg;base64,{result_base64}',
            'detections': results['detections'],
            'statistics': detection_stats,
            'parameters': {
                'conf_threshold': conf_threshold,
                'iou_threshold': iou_threshold,
                'show_labels': show_labels
            }
        }, 'Base64图像检测成功')
        
    except Exception as e:
        return error_response(f'Base64图像检测失败: {str(e)}')

@api_v1.route('/detection/video', methods=['POST'])
@token_required
def detect_video(current_user_id):
    """视频检测"""
    try:
        # 检查是否有文件上传
        if 'file' not in request.files:
            return error_response('没有上传文件')
        
        file = request.files['file']
        if file.filename == '':
            return error_response('没有选择文件')
        
        # 保存上传的视频文件
        filename = secure_filename(file.filename)
        video_path = f'uploads/videos/{datetime.now().strftime("%Y%m%d_%H%M%S")}_{filename}'
        file.save(video_path)
        
        # 获取检测参数
        conf_threshold = float(request.form.get('conf_threshold', 0.4))
        iou_threshold = float(request.form.get('iou_threshold', 0.5))
        show_labels = request.form.get('show_labels', 'true').lower() == 'true'
        save_result = request.form.get('save_result', 'true').lower() == 'true'
        
        # 执行视频检测
        result_data = yolo_service.detect_video(
            video_path,
            conf_threshold=conf_threshold,
            iou_threshold=iou_threshold,
            show_labels=show_labels,
            save_result=save_result
        )
        
        return success_response(result_data, '视频检测任务已启动')
        
    except Exception as e:
        return error_response(f'视频检测失败: {str(e)}')

@api_v1.route('/detection/rtsp', methods=['POST'])
@token_required
def detect_rtsp(current_user_id):
    """RTSP流检测"""
    try:
        data = request.get_json()
        rtsp_url = data.get('rtsp_url')
        
        if not rtsp_url:
            return error_response('RTSP URL不能为空')
        
        # 获取检测参数
        conf_threshold = float(data.get('conf_threshold', 0.4))
        iou_threshold = float(data.get('iou_threshold', 0.5))
        show_labels = data.get('show_labels', True)
        monitor_id = data.get('monitor_id')
        
        # 启动RTSP流检测
        task_id = yolo_service.start_rtsp_detection(
            rtsp_url,
            conf_threshold=conf_threshold,
            iou_threshold=iou_threshold,
            show_labels=show_labels,
            monitor_id=monitor_id,
            user_id=current_user_id
        )
        
        return success_response({
            'task_id': task_id,
            'rtsp_url': rtsp_url,
            'status': 'started'
        }, 'RTSP流检测已启动')
        
    except Exception as e:
        return error_response(f'RTSP流检测失败: {str(e)}')

@api_v1.route('/detection/rtsp/<task_id>/stop', methods=['POST'])
@token_required
def stop_rtsp_detection(current_user_id, task_id):
    """停止RTSP流检测"""
    try:
        success = yolo_service.stop_rtsp_detection(task_id)
        
        if success:
            return success_response(None, 'RTSP流检测已停止')
        else:
            return error_response('停止RTSP流检测失败')
        
    except Exception as e:
        return error_response(f'停止RTSP流检测失败: {str(e)}')

@api_v1.route('/detection/tasks', methods=['GET'])
@token_required
def get_detection_tasks(current_user_id):
    """获取检测任务列表"""
    try:
        tasks = yolo_service.get_user_tasks(current_user_id)
        
        return success_response({
            'tasks': tasks
        }, '获取检测任务列表成功')
        
    except Exception as e:
        return error_response(f'获取检测任务列表失败: {str(e)}')

@api_v1.route('/detection/task/<task_id>/status', methods=['GET'])
@token_required
def get_task_status(current_user_id, task_id):
    """获取检测任务状态"""
    try:
        status = yolo_service.get_task_status(task_id)
        
        if status:
            return success_response(status, '获取任务状态成功')
        else:
            return error_response('任务不存在')
        
    except Exception as e:
        return error_response(f'获取任务状态失败: {str(e)}')

@api_v1.route('/detection/models', methods=['GET'])
def get_available_models():
    """获取可用的检测模型"""
    try:
        models = yolo_service.get_available_models()
        
        return success_response({
            'models': models
        }, '获取可用模型成功')
        
    except Exception as e:
        return error_response(f'获取可用模型失败: {str(e)}')

@api_v1.route('/detection/model', methods=['POST'])
@token_required
def switch_model(current_user_id):
    """切换检测模型"""
    try:
        data = request.get_json()
        model_name = data.get('model_name')
        
        if not model_name:
            return error_response('模型名称不能为空')
        
        success = yolo_service.switch_model(model_name)
        
        if success:
            return success_response(None, '模型切换成功')
        else:
            return error_response('模型切换失败')
        
    except Exception as e:
        return error_response(f'模型切换失败: {str(e)}')
