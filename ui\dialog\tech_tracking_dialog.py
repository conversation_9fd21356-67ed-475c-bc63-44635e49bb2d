# 高科技风格的单目标跟踪对话框界面
from PySide6.QtCore import (QCoreApplication, QMetaObject, QObject, QPoint, QRect,
    QSize, Qt, QPropertyAnimation, QEasingCurve, Signal, Property)
from PySide6.QtGui import (QBrush, QColor, QConicalGradient, QCursor, QFont,
    QFontDatabase, QGradient, QIcon, QLinearGradient, QPainter, QPalette,
    QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QFrame, QLabel, QLineEdit, QPushButton,
    QSizePolicy, QVBoxLayout, QHBoxLayout, QWidget, QGraphicsDropShadowEffect)

class TechTrackingForm(object):
    def setupUi(self, Form):
        if not Form.objectName():
            Form.setObjectName(u"TechTrackingForm")
        
        # 设置窗口大小和样式
        Form.resize(580, 420)
        Form.setMinimumSize(QSize(580, 420))
        Form.setMaximumSize(QSize(580, 420))
        Form.setStyleSheet(u"#TechTrackingForm {\n"
                           "    background-color: qlineargradient(x0:0, y0:0, x1:1, y1:1, \n"
                           "                      stop:0 rgb(20, 50, 80), stop:1 rgb(30, 90, 120));\n"
                           "    border-radius: 15px;\n"
                           "    border: 2px solid rgba(0, 150, 255, 0.5);\n"
                           "}")
        
        # 设置无边框窗口
        Form.setWindowFlags(Qt.FramelessWindowHint)
        Form.setAttribute(Qt.WA_TranslucentBackground)
        
        # 主布局
        self.verticalLayout = QVBoxLayout(Form)
        self.verticalLayout.setSpacing(10)
        self.verticalLayout.setContentsMargins(15, 15, 15, 15)
        
        # 标题栏
        self.titleBar = QFrame(Form)
        self.titleBar.setMinimumSize(QSize(0, 40))
        self.titleBar.setMaximumSize(QSize(16777215, 40))
        self.titleBar.setStyleSheet(u"QFrame {\n"
                                   "    border-radius: 8px;\n"
                                   "    background-color: rgba(0, 60, 120, 180);\n"
                                   "}")
        
        # 标题栏布局
        self.horizontalLayout_title = QHBoxLayout(self.titleBar)
        self.horizontalLayout_title.setSpacing(6)
        self.horizontalLayout_title.setContentsMargins(15, 0, 15, 0)
        
        # 标题标签
        self.titleLabel = QLabel(self.titleBar)
        self.titleLabel.setStyleSheet(u"QLabel {\n"
                                    "    color: rgb(230, 230, 230);\n"
                                    "    font: 700 14pt \"Segoe UI\";\n"
                                    "    background-color: transparent;\n"
                                    "}")
        self.horizontalLayout_title.addWidget(self.titleLabel)
        
        # 标题栏空白区域
        self.horizontalSpacer = QWidget()
        self.horizontalSpacer.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.horizontalLayout_title.addWidget(self.horizontalSpacer)
        
        # 关闭按钮
        self.closeButton = QPushButton(self.titleBar)
        self.closeButton.setMinimumSize(QSize(30, 30))
        self.closeButton.setMaximumSize(QSize(30, 30))
        self.closeButton.setStyleSheet(u"QPushButton {\n"
                                     "    background-color: transparent;\n"
                                     "    color: white;\n"
                                     "    border-radius: 15px;\n"
                                     "    font: 700 14pt \"Segoe UI\";\n"
                                     "}\n"
                                     "QPushButton:hover {\n"
                                     "    background-color: rgba(255, 50, 50, 150);\n"
                                     "}\n"
                                     "QPushButton:pressed {\n"
                                     "    background-color: rgba(200, 0, 0, 200);\n"
                                     "}")
        self.horizontalLayout_title.addWidget(self.closeButton)
        self.verticalLayout.addWidget(self.titleBar)
        
        # 内容区域
        self.contentFrame = QFrame(Form)
        self.contentFrame.setStyleSheet(u"QFrame {\n"
                                      "    background-color: rgba(0, 40, 80, 150);\n"
                                      "    border-radius: 10px;\n"
                                      "}")
        
        # 内容布局
        self.verticalLayout_content = QVBoxLayout(self.contentFrame)
        self.verticalLayout_content.setSpacing(15)
        self.verticalLayout_content.setContentsMargins(20, 20, 20, 20)
        
        # 跟踪目标预览区域
        self.previewFrame = QFrame(self.contentFrame)
        self.previewFrame.setMinimumSize(QSize(0, 180))
        self.previewFrame.setStyleSheet(u"QFrame {\n"
                                       "    background-color: rgba(0, 20, 40, 100);\n"
                                       "    border-radius: 8px;\n"
                                       "    border: 1px solid rgba(0, 150, 255, 0.8);\n"
                                       "}")
        
        # 预览区布局
        self.verticalLayout_preview = QVBoxLayout(self.previewFrame)
        self.verticalLayout_preview.setSpacing(5)
        self.verticalLayout_preview.setContentsMargins(10, 10, 10, 10)
        
        # 预览区标签
        self.previewLabel = QLabel(self.previewFrame)
        self.previewLabel.setAlignment(Qt.AlignCenter)
        self.previewLabel.setStyleSheet(u"QLabel {\n"
                                      "    color: rgb(200, 200, 200);\n"
                                      "    font: 11pt \"Segoe UI\";\n"
                                      "    background-color: transparent;\n"
                                      "}")
        self.verticalLayout_preview.addWidget(self.previewLabel)
        
        # 目标图像显示区域
        self.imageLabel = QLabel(self.previewFrame)
        self.imageLabel.setAlignment(Qt.AlignCenter)
        self.imageLabel.setMinimumSize(QSize(160, 120))
        self.imageLabel.setStyleSheet(u"QLabel {\n"
                                    "    background-color: rgba(0, 0, 0, 60);\n"
                                    "    border-radius: 5px;\n"
                                    "}")
        self.verticalLayout_preview.addWidget(self.imageLabel)
        self.verticalLayout_content.addWidget(self.previewFrame)
        
        # 控制区域
        self.controlFrame = QFrame(self.contentFrame)
        self.controlFrame.setStyleSheet(u"QFrame {\n"
                                       "    background-color: rgba(0, 30, 60, 100);\n"
                                       "    border-radius: 8px;\n"
                                       "}")
        
        # 控制区布局
        self.verticalLayout_control = QVBoxLayout(self.controlFrame)
        self.verticalLayout_control.setSpacing(12)
        self.verticalLayout_control.setContentsMargins(15, 15, 15, 15)
        
        # ID输入区域
        self.idFrame = QFrame(self.controlFrame)
        self.idFrame.setMinimumSize(QSize(0, 45))
        self.idFrame.setMaximumSize(QSize(16777215, 45))
        self.idFrame.setStyleSheet(u"QFrame {\n"
                                  "    background-color: transparent;\n"
                                  "}")
        
        # ID输入区布局
        self.horizontalLayout_id = QHBoxLayout(self.idFrame)
        self.horizontalLayout_id.setSpacing(10)
        self.horizontalLayout_id.setContentsMargins(0, 0, 0, 0)
        
        # ID标签
        self.idLabel = QLabel(self.idFrame)
        self.idLabel.setMinimumSize(QSize(120, 0))
        self.idLabel.setMaximumSize(QSize(120, 16777215))
        self.idLabel.setStyleSheet(u"QLabel {\n"
                                 "    color: rgb(220, 220, 220);\n"
                                 "    font: 500 12pt \"Segoe UI\";\n"
                                 "    background-color: transparent;\n"
                                 "}")
        self.horizontalLayout_id.addWidget(self.idLabel)
        
        # ID输入框
        self.idEdit = QLineEdit(self.idFrame)
        self.idEdit.setMinimumSize(QSize(0, 35))
        self.idEdit.setStyleSheet(u"QLineEdit {\n"
                                "    background-color: rgba(0, 10, 30, 120);\n"
                                "    border: 1px solid rgba(0, 150, 255, 0.5);\n"
                                "    border-radius: 5px;\n"
                                "    padding-left: 10px;\n"
                                "    color: rgb(220, 220, 220);\n"
                                "    font: 500 12pt \"Segoe UI\";\n"
                                "}\n"
                                "QLineEdit:focus {\n"
                                "    border: 1px solid rgba(0, 200, 255, 0.8);\n"
                                "}")
        self.horizontalLayout_id.addWidget(self.idEdit)
        self.verticalLayout_control.addWidget(self.idFrame)
        
        # 状态信息区域
        self.statusFrame = QFrame(self.controlFrame)
        self.statusFrame.setMinimumSize(QSize(0, 35))
        self.statusFrame.setMaximumSize(QSize(16777215, 35))
        self.statusFrame.setStyleSheet(u"QFrame {\n"
                                      "    background-color: rgba(0, 15, 35, 120);\n"
                                      "    border-radius: 5px;\n"
                                      "}")
        
        # 状态区布局
        self.horizontalLayout_status = QHBoxLayout(self.statusFrame)
        self.horizontalLayout_status.setSpacing(0)
        self.horizontalLayout_status.setContentsMargins(15, 0, 15, 0)
        
        # 状态标签
        self.statusLabel = QLabel(self.statusFrame)
        self.statusLabel.setStyleSheet(u"QLabel {\n"
                                     "    color: rgb(0, 200, 100);\n"
                                     "    font: 500 10pt \"Segoe UI\";\n"
                                     "    background-color: transparent;\n"
                                     "}")
        self.horizontalLayout_status.addWidget(self.statusLabel)
        self.verticalLayout_control.addWidget(self.statusFrame)
        
        # 按钮区域
        self.buttonFrame = QFrame(self.controlFrame)
        self.buttonFrame.setMinimumSize(QSize(0, 45))
        self.buttonFrame.setMaximumSize(QSize(16777215, 45))
        self.buttonFrame.setStyleSheet(u"QFrame {\n"
                                     "    background-color: transparent;\n"
                                     "}")
        
        # 按钮区布局
        self.horizontalLayout_button = QHBoxLayout(self.buttonFrame)
        self.horizontalLayout_button.setSpacing(15)
        self.horizontalLayout_button.setContentsMargins(0, 0, 0, 0)
        
        # 空白区
        self.horizontalSpacer_btn = QWidget()
        self.horizontalSpacer_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.horizontalLayout_button.addWidget(self.horizontalSpacer_btn)
        
        # 跟踪按钮
        self.trackButton = QPushButton(self.buttonFrame)
        self.trackButton.setMinimumSize(QSize(120, 38))
        self.trackButton.setMaximumSize(QSize(120, 38))
        self.trackButton.setStyleSheet(u"QPushButton {\n"
                                     "    background-color: qlineargradient(x0:0, y0:0, x1:1, y1:0, \n"
                                     "                      stop:0 rgba(0, 80, 150, 200), stop:1 rgba(0, 120, 180, 200));\n"
                                     "    color: white;\n"
                                     "    border-radius: 8px;\n"
                                     "    font: 600 11pt \"Segoe UI\";\n"
                                     "}\n"
                                     "QPushButton:hover {\n"
                                     "    background-color: qlineargradient(x0:0, y0:0, x1:1, y1:0, \n"
                                     "                      stop:0 rgba(0, 100, 180, 220), stop:1 rgba(0, 140, 200, 220));\n"
                                     "}\n"
                                     "QPushButton:pressed {\n"
                                     "    background-color: qlineargradient(x0:0, y0:0, x1:1, y1:0, \n"
                                     "                      stop:0 rgba(0, 60, 120, 200), stop:1 rgba(0, 90, 150, 200));\n"
                                     "}")
        
        # 添加阴影效果
        trackButtonShadow = QGraphicsDropShadowEffect()
        trackButtonShadow.setBlurRadius(15)
        trackButtonShadow.setColor(QColor(0, 150, 255, 100))
        trackButtonShadow.setOffset(0, 2)
        self.trackButton.setGraphicsEffect(trackButtonShadow)
        
        self.horizontalLayout_button.addWidget(self.trackButton)
        
        # 取消按钮
        self.cancelButton = QPushButton(self.buttonFrame)
        self.cancelButton.setMinimumSize(QSize(120, 38))
        self.cancelButton.setMaximumSize(QSize(120, 38))
        self.cancelButton.setStyleSheet(u"QPushButton {\n"
                                      "    background-color: rgba(60, 60, 70, 180);\n"
                                      "    color: white;\n"
                                      "    border-radius: 8px;\n"
                                      "    font: 600 11pt \"Segoe UI\";\n"
                                      "}\n"
                                      "QPushButton:hover {\n"
                                      "    background-color: rgba(80, 80, 90, 180);\n"
                                      "}\n"
                                      "QPushButton:pressed {\n"
                                      "    background-color: rgba(50, 50, 60, 180);\n"
                                      "}")
        
        # 添加阴影效果
        cancelButtonShadow = QGraphicsDropShadowEffect()
        cancelButtonShadow.setBlurRadius(15)
        cancelButtonShadow.setColor(QColor(0, 0, 0, 60))
        cancelButtonShadow.setOffset(0, 2)
        self.cancelButton.setGraphicsEffect(cancelButtonShadow)
        
        self.horizontalLayout_button.addWidget(self.cancelButton)
        self.verticalLayout_control.addWidget(self.buttonFrame)
        self.verticalLayout_content.addWidget(self.controlFrame)
        self.verticalLayout.addWidget(self.contentFrame)
        
        # 设置阴影效果
        contentShadow = QGraphicsDropShadowEffect()
        contentShadow.setBlurRadius(20)
        contentShadow.setColor(QColor(0, 0, 0, 80))
        contentShadow.setOffset(0, 2)
        self.contentFrame.setGraphicsEffect(contentShadow)
        
        # 添加一个覆盖底部可能出现的乱码的透明标签
        self.hiddenLabel = QLabel(Form)
        self.hiddenLabel.setGeometry(QRect(0, 390, 580, 30))
        self.hiddenLabel.setStyleSheet("background-color: rgba(20, 50, 80, 0.8);")
        self.hiddenLabel.setText("")
        self.hiddenLabel.raise_()
        
        # 设置字体渲染
        self.retranslateUi(Form)
        QMetaObject.connectSlotsByName(Form)

    def retranslateUi(self, Form):
        Form.setWindowTitle(QCoreApplication.translate("Form", u"高级单目标追踪", None))
        self.titleLabel.setText(QCoreApplication.translate("Form", u"高级单目标追踪系统", None))
        self.closeButton.setText(QCoreApplication.translate("Form", u"×", None))
        self.previewLabel.setText(QCoreApplication.translate("Form", u"跟踪目标预览", None))
        self.imageLabel.setText(QCoreApplication.translate("Form", u"尚未选择目标", None))
        self.idLabel.setText(QCoreApplication.translate("Form", u"目标 ID:", None))
        self.idEdit.setPlaceholderText(QCoreApplication.translate("Form", u"输入要跟踪的目标ID", None))
        self.statusLabel.setText(QCoreApplication.translate("Form", u"就绪: 等待输入目标ID", None))
        self.trackButton.setText(QCoreApplication.translate("Form", u"开始跟踪", None))
        self.cancelButton.setText(QCoreApplication.translate("Form", u"取消", None))

# 实现窗口类
class TechTrackingWindow(QWidget, TechTrackingForm):
    # 定义信号
    idConfirmed = Signal(int)
    
    def __init__(self):
        super(TechTrackingWindow, self).__init__()
        self.setupUi(self)
        
        # 设置窗口无边框和透明背景
        self.setWindowFlags(Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # 连接信号槽
        self.closeButton.clicked.connect(self.close)
        self.cancelButton.clicked.connect(self.close)
        self.trackButton.clicked.connect(self.confirmId)
        
        # 变量初始化
        self.targetPixmap = None
    
    # 确认ID并发送信号
    def confirmId(self):
        try:
            target_id = int(self.idEdit.text())
            self.statusLabel.setText("正在追踪目标 ID: " + str(target_id))
            self.statusLabel.setStyleSheet("color: rgb(0, 200, 100); font: 500 10pt \"Segoe UI\";")
            self.idConfirmed.emit(target_id)
        except ValueError:
            self.statusLabel.setText("错误: 请输入有效的ID数字")
            self.statusLabel.setStyleSheet("color: rgb(255, 100, 100); font: 500 10pt \"Segoe UI\";")
    
    # 更新目标预览图像
    def updateTargetImage(self, pixmap):
        if pixmap:
            self.targetPixmap = pixmap
            # 调整图像大小以适应显示区域
            scaled_pixmap = pixmap.scaled(160, 120, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            self.imageLabel.setPixmap(scaled_pixmap)
            self.imageLabel.setText("")
        else:
            self.imageLabel.setText("无可用目标图像")
    
    # 鼠标拖动窗口实现
    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton and self.titleBar.geometry().contains(event.pos()):
            self.dragPos = event.globalPos()
            event.accept()
    
    def mouseMoveEvent(self, event):
        if event.buttons() == Qt.LeftButton and hasattr(self, 'dragPos'):
            self.move(self.pos() + event.globalPos() - self.dragPos)
            self.dragPos = event.globalPos()
            event.accept()
