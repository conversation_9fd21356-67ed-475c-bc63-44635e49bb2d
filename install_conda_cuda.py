# -*- coding: utf-8 -*-
# @Description : Conda环境CUDA依赖安装脚本 (Python 3.9.16)
# @Date : 2025年6月20日

import subprocess
import sys
import os
import platform

def run_command(command, description, check_error=True):
    """运行命令并显示结果"""
    print(f"\n{description}...")
    print(f"执行命令: {command}")
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✓ {description}成功")
            if result.stdout.strip():
                print(f"输出: {result.stdout.strip()}")
        else:
            print(f"✗ {description}失败")
            if result.stderr.strip():
                print(f"错误: {result.stderr.strip()}")
            if check_error:
                return False
    except Exception as e:
        print(f"✗ {description}异常: {e}")
        if check_error:
            return False
    
    return True

def check_conda():
    """检查Conda是否可用"""
    print("=" * 80)
    print("检查Conda环境")
    print("=" * 80)
    
    try:
        result = subprocess.run("conda --version", shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ Conda已安装: {result.stdout.strip()}")
            return True
        else:
            print("✗ Conda未安装或不在PATH中")
            return False
    except Exception as e:
        print(f"✗ Conda检查失败: {e}")
        return False

def check_existing_env():
    """检查ByteTrack环境是否存在"""
    print("\n检查ByteTrack环境...")
    
    try:
        result = subprocess.run("conda env list", shell=True, capture_output=True, text=True)
        if "ByteTrack" in result.stdout:
            print("⚠ ByteTrack环境已存在")
            response = input("是否删除现有环境并重新创建? (y/n): ")
            if response.lower() == 'y':
                run_command("conda env remove -n ByteTrack -y", "删除现有ByteTrack环境")
                return False
            else:
                print("将在现有环境中安装依赖")
                return True
        else:
            print("✓ ByteTrack环境不存在，将创建新环境")
            return False
    except Exception as e:
        print(f"环境检查失败: {e}")
        return False

def create_conda_env():
    """创建Conda环境"""
    print("\n" + "=" * 80)
    print("创建Conda环境 (Python 3.9.16)")
    print("=" * 80)
    
    # 创建环境
    if not run_command(
        "conda create -n ByteTrack python=3.9.16 -y", 
        "创建ByteTrack环境"
    ):
        return False
    
    print("✓ ByteTrack环境创建成功")
    return True

def install_cuda_pytorch_conda():
    """使用Conda安装CUDA PyTorch"""
    print("\n" + "=" * 80)
    print("安装CUDA PyTorch (Conda方式)")
    print("=" * 80)
    
    # 激活环境并安装PyTorch
    commands = [
        # 安装PyTorch CUDA版本 (兼容CUDA 11.8)
        "conda activate ByteTrack && conda install pytorch==2.1.2 torchvision==0.16.2 torchaudio==2.1.2 pytorch-cuda=11.8 -c pytorch -c nvidia -y",
        
        # 验证安装
        "conda activate ByteTrack && python -c \"import torch; print(f'PyTorch: {torch.__version__}'); print(f'CUDA: {torch.cuda.is_available()}')\""
    ]
    
    for i, cmd in enumerate(commands):
        if not run_command(cmd, f"PyTorch安装步骤 {i+1}"):
            return False
    
    return True

def install_local_pytorch():
    """安装本地PyTorch wheel文件"""
    print("\n" + "=" * 80)
    print("安装本地PyTorch wheel文件")
    print("=" * 80)
    
    # 本地wheel文件路径
    torch_wheel = r"C:\Users\<USER>\Downloads\torch-2.1.2+cu118-cp311-cp311-win_amd64.whl"
    torchvision_wheel = r"C:\Users\<USER>\Downloads\torchvision-0.16.1+cu118-cp311-cp311-win_amd64.whl"
    
    # 检查文件是否存在
    if not os.path.exists(torch_wheel):
        print(f"✗ PyTorch wheel文件不存在: {torch_wheel}")
        print("注意: 您的wheel文件是为Python 3.11编译的，但您使用Python 3.9")
        print("建议使用Conda安装兼容版本")
        return False
    
    if not os.path.exists(torchvision_wheel):
        print(f"✗ TorchVision wheel文件不存在: {torchvision_wheel}")
        return False
    
    print("⚠ 警告: wheel文件是为Python 3.11编译的，可能与Python 3.9不兼容")
    response = input("是否仍要尝试安装? (y/n): ")
    if response.lower() != 'y':
        return False
    
    # 安装wheel文件
    commands = [
        f"conda activate ByteTrack && pip install \"{torch_wheel}\"",
        f"conda activate ByteTrack && pip install \"{torchvision_wheel}\""
    ]
    
    for cmd in commands:
        if not run_command(cmd, "安装本地wheel文件", check_error=False):
            print("本地wheel安装失败，建议使用Conda方式")
            return False
    
    return True

def install_compatible_packages():
    """安装兼容的其他包"""
    print("\n" + "=" * 80)
    print("安装兼容的其他包")
    print("=" * 80)
    
    # Python 3.9.16兼容的包版本
    packages = [
        # 基础科学计算包
        "numpy==1.24.3",
        "scipy==1.10.1",
        "matplotlib==3.7.2",
        
        # 计算机视觉包
        "opencv-python==********",
        "pillow==9.5.0",
        
        # 机器学习包
        "ultralytics>=8.0.0",
        "supervision>=0.16.0",
        
        # Web框架
        "flask==2.3.3",
        "flask-cors==4.0.0",
        
        # 数据库
        "pymysql==1.1.0",
        
        # 工具包
        "python-dotenv==1.0.0",
        "pandas==2.0.3",
        "psutil==5.9.6",
        "tqdm==4.66.1",
        "requests==2.31.0"
    ]
    
    # 分批安装以避免冲突
    batch_size = 3
    for i in range(0, len(packages), batch_size):
        batch = packages[i:i+batch_size]
        package_str = " ".join(batch)
        
        if not run_command(
            f"conda activate ByteTrack && pip install {package_str}",
            f"安装包批次 {i//batch_size + 1}"
        ):
            print(f"批次 {i//batch_size + 1} 安装失败，尝试单独安装...")
            for package in batch:
                run_command(
                    f"conda activate ByteTrack && pip install {package}",
                    f"安装 {package}",
                    check_error=False
                )
    
    return True

def install_optional_packages():
    """安装可选包"""
    print("\n安装可选包...")
    
    optional_packages = [
        "flask-socketio==5.3.6",
        "redis==5.0.1",
        "apscheduler==3.10.4",
        "gunicorn==21.2.0",
        "eventlet==0.33.3"
    ]
    
    for package in optional_packages:
        run_command(
            f"conda activate ByteTrack && pip install {package}",
            f"安装可选包 {package}",
            check_error=False
        )

def test_installation():
    """测试安装结果"""
    print("\n" + "=" * 80)
    print("测试安装结果")
    print("=" * 80)
    
    test_script = '''
import sys
print(f"Python版本: {sys.version}")

try:
    import torch
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
except Exception as e:
    print(f"PyTorch测试失败: {e}")

try:
    import torchvision
    print(f"TorchVision版本: {torchvision.__version__}")
except Exception as e:
    print(f"TorchVision测试失败: {e}")

try:
    import cv2
    print(f"OpenCV版本: {cv2.__version__}")
except Exception as e:
    print(f"OpenCV测试失败: {e}")

try:
    import numpy as np
    print(f"NumPy版本: {np.__version__}")
except Exception as e:
    print(f"NumPy测试失败: {e}")

try:
    from ultralytics import YOLO
    print("Ultralytics YOLO导入成功")
    
    # 简单的YOLO测试
    model = YOLO("yolov8n.pt")
    print("YOLO模型加载成功")
    
    if torch.cuda.is_available():
        model.to("cuda")
        print("YOLO模型已移至GPU")
except Exception as e:
    print(f"YOLO测试失败: {e}")

try:
    import flask
    print(f"Flask版本: {flask.__version__}")
except Exception as e:
    print(f"Flask测试失败: {e}")
'''
    
    # 将测试脚本写入临时文件
    with open("temp_test.py", "w", encoding="utf-8") as f:
        f.write(test_script)
    
    # 运行测试
    success = run_command(
        "conda activate ByteTrack && python temp_test.py",
        "运行安装测试",
        check_error=False
    )
    
    # 清理临时文件
    try:
        os.remove("temp_test.py")
    except:
        pass
    
    return success

def create_activation_script():
    """创建环境激活脚本"""
    print("\n创建环境激活脚本...")
    
    # Windows批处理脚本
    bat_content = '''@echo off
echo 激活ByteTrack Conda环境...
call conda activate ByteTrack
echo 环境已激活！
echo.
echo 可用命令:
echo   python start_server.py    - 启动服务器
echo   python test_cuda.py       - 测试CUDA
echo   python test_db.py         - 测试数据库
echo.
cmd /k
'''
    
    with open("activate_bytetrack.bat", "w", encoding="utf-8") as f:
        f.write(bat_content)
    
    # PowerShell脚本
    ps1_content = '''Write-Host "激活ByteTrack Conda环境..." -ForegroundColor Green
conda activate ByteTrack
Write-Host "环境已激活！" -ForegroundColor Green
Write-Host ""
Write-Host "可用命令:" -ForegroundColor Yellow
Write-Host "  python start_server.py    - 启动服务器" -ForegroundColor Cyan
Write-Host "  python test_cuda.py       - 测试CUDA" -ForegroundColor Cyan
Write-Host "  python test_db.py         - 测试数据库" -ForegroundColor Cyan
Write-Host ""
'''
    
    with open("activate_bytetrack.ps1", "w", encoding="utf-8") as f:
        f.write(ps1_content)
    
    print("✓ 激活脚本已创建:")
    print("  - activate_bytetrack.bat (Windows批处理)")
    print("  - activate_bytetrack.ps1 (PowerShell)")

def update_config_for_conda():
    """更新配置文件以适配Conda环境"""
    print("\n更新配置文件...")
    
    # 更新环境配置
    config_file = "config/end-back.env"
    if os.path.exists(config_file):
        with open(config_file, "r", encoding="utf-8") as f:
            content = f.read()
        
        # 添加Conda环境配置
        conda_config = '''
#---------------------------------------------------Conda环境配置
CONDA_ENV_NAME=ByteTrack
PYTHON_VERSION=3.9.16
USE_CONDA=true

#---------------------------------------------------CUDA配置 (Conda)
USE_CUDA=true
CUDA_DEVICE=0
YOLO_DEVICE=cuda
YOLO_HALF_PRECISION=true
YOLO_BATCH_SIZE=1
TORCH_CUDNN_BENCHMARK=true
'''
        
        if "CONDA_ENV_NAME" not in content:
            with open(config_file, "a", encoding="utf-8") as f:
                f.write(conda_config)
            print("✓ 配置文件已更新")
        else:
            print("✓ 配置文件已包含Conda配置")

def main():
    """主函数"""
    print("=" * 80)
    print("基于Yolov8与ByteTrack的高速公路智慧监控平台 - Conda CUDA环境配置")
    print("Python 3.9.16 + ByteTrack环境")
    print("=" * 80)
    
    # 检查Conda
    if not check_conda():
        print("\n请先安装Anaconda或Miniconda:")
        print("https://www.anaconda.com/products/distribution")
        return
    
    # 检查现有环境
    env_exists = check_existing_env()
    
    # 创建环境（如果不存在）
    if not env_exists:
        if not create_conda_env():
            print("环境创建失败，退出")
            return
    
    # 选择PyTorch安装方式
    print("\n选择PyTorch安装方式:")
    print("1. 使用Conda安装 (推荐，自动处理兼容性)")
    print("2. 使用本地wheel文件 (可能有兼容性问题)")
    
    choice = input("请选择 (1/2): ").strip()
    
    if choice == "2":
        if not install_local_pytorch():
            print("本地wheel安装失败，回退到Conda方式")
            if not install_cuda_pytorch_conda():
                print("PyTorch安装失败，退出")
                return
    else:
        if not install_cuda_pytorch_conda():
            print("PyTorch安装失败，退出")
            return
    
    # 安装其他包
    install_compatible_packages()
    install_optional_packages()
    
    # 测试安装
    if test_installation():
        print("\n" + "=" * 80)
        print("✓ Conda CUDA环境配置成功！")
        print("=" * 80)
        
        # 创建激活脚本
        create_activation_script()
        
        # 更新配置
        update_config_for_conda()
        
        print("\n下一步操作:")
        print("1. 激活环境: conda activate ByteTrack")
        print("2. 或双击: activate_bytetrack.bat")
        print("3. 测试CUDA: python test_cuda.py")
        print("4. 测试数据库: python test_db.py")
        print("5. 启动系统: python start_server.py")
        
        print("\n环境信息:")
        print(f"  环境名称: ByteTrack")
        print(f"  Python版本: 3.9.16")
        print(f"  PyTorch: CUDA 11.8版本")
        print(f"  激活命令: conda activate ByteTrack")
        
    else:
        print("\n" + "=" * 80)
        print("✗ 环境配置验证失败")
        print("=" * 80)
        print("请检查:")
        print("1. Conda环境是否正确创建")
        print("2. PyTorch是否正确安装")
        print("3. CUDA驱动是否正确安装")

if __name__ == "__main__":
    main()
