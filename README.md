# YOLO目标检测系统

基于YOLOv8与ByteTrack的高速公路智慧监控平台

## 项目简介

这是一个基于YOLOv8深度学习模型的智能目标检测系统，集成了多目标追踪、实时监控、Web API接口等功能。系统支持图像、视频和实时摄像头检测，适用于交通监控、安防监控等场景。

## 主要功能

- 🎯 **目标检测**: 基于YOLOv8模型的高精度目标检测
- 🔄 **多目标追踪**: 集成ByteTrack算法实现稳定的目标追踪
- 📹 **多源输入**: 支持图像、视频文件、摄像头、RTSP流
- 🌐 **Web API**: 提供RESTful API接口
- 💾 **数据管理**: MySQL数据库存储检测结果
- 🎨 **可视化界面**: PyQt5图形用户界面
- ⚡ **实时处理**: 支持实时视频流处理

## 技术栈

- **深度学习**: YOLOv8, PyTorch
- **计算机视觉**: OpenCV, Supervision
- **后端框架**: Flask, Flask-SocketIO
- **数据库**: MySQL, SQLAlchemy
- **前端界面**: PyQt5
- **目标追踪**: ByteTrack
- **缓存**: Redis

## 系统要求

- Python 3.8+
- CUDA 11.0+ (可选，用于GPU加速)
- MySQL 8.0+
- Redis (可选)

## 安装指南

### 1. 克隆项目
```bash
git clone <repository-url>
cd yolo
```

### 2. 创建虚拟环境
```bash
python -m venv venv_crack
venv_crack\Scripts\activate  # Windows
# source venv_crack/bin/activate  # Linux/Mac
```

### 3. 安装依赖
```bash
pip install -r requirements.txt
```

### 4. 数据库配置
- 安装MySQL 8.0
- 导入数据库结构：`MySQL8-最终版数据库.sql`
- 配置数据库连接信息

### 5. 配置文件
- 复制并修改 `config/config.json`
- 设置数据库连接参数
- 配置模型路径和检测参数

## 使用方法

### 启动主程序
```bash
python main.py
```

### 启动Web API服务
```bash
python app.py
```

### 启动后端服务
```bash
python start_backend.py
```

## 项目结构

```
yolo/
├── classes/           # 核心检测类
├── ui/               # 用户界面
├── backend/          # 后端API
├── models/           # 模型文件
├── config/           # 配置文件
├── utils/            # 工具函数
├── static/           # 静态资源
├── uploads/          # 上传文件
└── logs/             # 日志文件
```

## API接口

详细的API文档请参考：
- `API接口文档-完整版.md`
- `API接口文档-前端对接版.md`

## 配置说明

- **CUDA配置**: 参考 `CUDA安装指南.md`
- **Conda环境**: 参考 `Conda环境配置指南.md`
- **数据库导入**: 参考 `数据库导入指南.md`

## 开发文档

- `前端功能规划-最终版.md`
- `环境配置指南.md`
- `快速启动指南.md`

## 许可证

本项目仅供学习和研究使用。

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 联系方式

如有问题请通过Issue联系。