# -*- coding: utf-8 -*-
# @Description : 实时数据处理相关API接口
# @Date : 2025年6月20日

import json
from datetime import datetime
from flask import request, jsonify
from flask_socketio import emit, join_room, leave_room, rooms
try:
    from flask_socketio import SocketIO
    socketio = None  # 将在主应用中初始化
except ImportError:
    socketio = None
from . import api_v1
from .auth import token_required
from utils.response import success_response, error_response
from utils.database import get_db_connection
from services.realtime_service import RealtimeService

# 初始化实时服务
realtime_service = RealtimeService()

@api_v1.route('/realtime/status', methods=['GET'])
@token_required
def get_realtime_status(current_user_id):
    """获取实时系统状态"""
    try:
        status = realtime_service.get_system_status()
        
        return success_response(status, '获取实时系统状态成功')
        
    except Exception as e:
        return error_response(f'获取实时系统状态失败: {str(e)}')

@api_v1.route('/realtime/monitors/active', methods=['GET'])
@token_required
def get_active_monitors(current_user_id):
    """获取活跃监控点"""
    try:
        with get_db_connection() as db:
            monitors = db.get_list(
                """SELECT id, location, highway_section, camera_position, 
                          connection_status, is_alarm, threshold, conf_threshold
                   FROM monitor 
                   WHERE is_alarm='开启' AND connection_status='connected'
                   ORDER BY location"""
            )
        
        # 获取每个监控点的实时数据
        for monitor in monitors:
            realtime_data = realtime_service.get_monitor_realtime_data(monitor['id'])
            monitor.update(realtime_data)
        
        return success_response({
            'monitors': monitors,
            'total_active': len(monitors)
        }, '获取活跃监控点成功')
        
    except Exception as e:
        return error_response(f'获取活跃监控点失败: {str(e)}')

@api_v1.route('/realtime/monitor/<int:monitor_id>/stream', methods=['GET'])
@token_required
def get_monitor_stream_info(current_user_id, monitor_id):
    """获取监控点流信息"""
    try:
        with get_db_connection() as db:
            monitor = db.get_one(
                "SELECT id, url, rtsp_format, connection_status FROM monitor WHERE id=%s",
                (monitor_id,)
            )
        
        if not monitor:
            return error_response('监控点不存在')
        
        stream_info = realtime_service.get_stream_info(monitor_id)
        
        return success_response({
            'monitor': monitor,
            'stream_info': stream_info
        }, '获取监控点流信息成功')
        
    except Exception as e:
        return error_response(f'获取监控点流信息失败: {str(e)}')

@api_v1.route('/realtime/monitor/<int:monitor_id>/latest-detection', methods=['GET'])
@token_required
def get_latest_detection(current_user_id, monitor_id):
    """获取最新检测结果"""
    try:
        detection_data = realtime_service.get_latest_detection(monitor_id)
        
        return success_response(detection_data, '获取最新检测结果成功')
        
    except Exception as e:
        return error_response(f'获取最新检测结果失败: {str(e)}')

@api_v1.route('/realtime/alerts/recent', methods=['GET'])
@token_required
def get_recent_alerts(current_user_id):
    """获取最近警报"""
    try:
        limit = int(request.args.get('limit', 10))
        
        with get_db_connection() as db:
            alerts = db.get_list(
                """SELECT a.id, a.location, a.highway_section, a.description, 
                          a.vehicle_count, a.confidence_level, a.create_time,
                          m.person as monitor_person
                   FROM alarm a
                   LEFT JOIN monitor m ON a.pid = m.id
                   ORDER BY a.create_time DESC
                   LIMIT %s""",
                (limit,)
            )
        
        return success_response({
            'alerts': alerts,
            'count': len(alerts)
        }, '获取最近警报成功')
        
    except Exception as e:
        return error_response(f'获取最近警报失败: {str(e)}')

@api_v1.route('/realtime/statistics/live', methods=['GET'])
@token_required
def get_live_statistics(current_user_id):
    """获取实时统计数据"""

@api_v1.route('/realtime/multichannel/streams', methods=['GET'])
@token_required
def get_multichannel_streams(current_user_id):
    """获取多路视频流信息"""
    try:
        streams = realtime_service.get_all_active_streams()
        return success_response(streams, '获取多路视频流成功')
    except Exception as e:
        return error_response(f'获取多路视频流失败: {str(e)}')

    try:
        stats = realtime_service.get_live_statistics()
        
        return success_response(stats, '获取实时统计数据成功')
        
    except Exception as e:
        return error_response(f'获取实时统计数据失败: {str(e)}')

# WebSocket事件处理
# 注意：这里需要在主应用中导入socketio实例

# 暂时注释掉socketio事件处理，避免启动错误
# @socketio.on('connect')
# def handle_connect():
#     """客户端连接"""
#     print(f'客户端已连接: {request.sid}')
#     emit('connected', {'message': '连接成功'})

# @socketio.on('disconnect')
# def handle_disconnect():
#     """客户端断开连接"""
#     print(f'客户端已断开: {request.sid}')

# @socketio.on('join_monitor')
# def handle_join_monitor(data):
#     """加入监控点房间"""
#     try:
#         monitor_id = data.get('monitor_id')
#         if monitor_id:
#             room = f'monitor_{monitor_id}'
#             join_room(room)
#             emit('joined_monitor', {
#                 'monitor_id': monitor_id,
#                 'message': f'已加入监控点 {monitor_id} 的实时数据推送'
#             })
#             
#             # 发送当前监控点状态
#             current_data = realtime_service.get_monitor_realtime_data(monitor_id)
#             emit('monitor_data', current_data, room=room)
#     except Exception as e:
#         emit('error', {'message': f'加入监控点失败: {str(e)}'})

# @socketio.on('leave_monitor')
# def handle_leave_monitor(data):
#     """离开监控点房间"""
#     try:
#         monitor_id = data.get('monitor_id')
#         if monitor_id:
#             room = f'monitor_{monitor_id}'
#             leave_room(room)
#             emit('left_monitor', {
#                 'monitor_id': monitor_id,
#                 'message': f'已离开监控点 {monitor_id} 的实时数据推送'
#             })
#     except Exception as e:
#         emit('error', {'message': f'离开监控点失败: {str(e)}'})

# @socketio.on('join_alerts')
# def handle_join_alerts():
#     """加入警报房间"""
#     try:
#         join_room('alerts')
#         emit('joined_alerts', {'message': '已加入警报推送'})
#     except Exception as e:
#         emit('error', {'message': f'加入警报推送失败: {str(e)}'})

# @socketio.on('leave_alerts')
# def handle_leave_alerts():
#     """离开警报房间"""
#     try:
#         leave_room('alerts')
#         emit('left_alerts', {'message': '已离开警报推送'})
#     except Exception as e:
#         emit('error', {'message': f'离开警报推送失败: {str(e)}'})

# @socketio.on('get_monitor_list')
# def handle_get_monitor_list():
#     """获取监控点列表"""
#     try:
#         with get_db_connection() as db:
#             monitors = db.get_list(
#                 """SELECT id, location, highway_section, connection_status, is_alarm
#                    FROM monitor 
#                    ORDER BY location"""
#             )
#         
#         emit('monitor_list', {'monitors': monitors})
#     except Exception as e:
#         emit('error', {'message': f'获取监控点列表失败: {str(e)}'})

# @socketio.on('request_monitor_data')
# def handle_request_monitor_data(data):
#     """请求监控点数据"""
#     try:
#         monitor_id = data.get('monitor_id')
#         if monitor_id:
#             monitor_data = realtime_service.get_monitor_realtime_data(monitor_id)
#             emit('monitor_data', monitor_data)
#     except Exception as e:
#         emit('error', {'message': f'获取监控点数据失败: {str(e)}'})

# 实时数据推送函数（由后台任务调用）
# def broadcast_monitor_data(monitor_id, data):
#     """广播监控点数据"""
#     room = f'monitor_{monitor_id}'
#     socketio.emit('monitor_data', data, room=room)

# def broadcast_alert(alert_data):
#     """广播警报"""
#     socketio.emit('new_alert', alert_data, room='alerts')

# def broadcast_system_status(status_data):
#     """广播系统状态"""
#     socketio.emit('system_status', status_data, broadcast=True)

# 实时数据处理API
@api_v1.route('/realtime/push/monitor-data', methods=['POST'])
def push_monitor_data():
    """推送监控点数据（内部API）"""
    try:
        data = request.get_json()
        monitor_id = data.get('monitor_id')
        detection_data = data.get('detection_data')
        
        if monitor_id and detection_data:
            # 保存到实时数据缓存
            realtime_service.update_monitor_data(monitor_id, detection_data)
            
            # 广播给订阅的客户端
            broadcast_monitor_data(monitor_id, detection_data)
            
            # 检查是否需要触发警报
            if realtime_service.should_trigger_alert(monitor_id, detection_data):
                alert_data = realtime_service.create_alert(monitor_id, detection_data)
                broadcast_alert(alert_data)
        
        return success_response(None, '数据推送成功')
        
    except Exception as e:
        return error_response(f'数据推送失败: {str(e)}')

@api_v1.route('/realtime/push/alert', methods=['POST'])
def push_alert():
    """推送警报（内部API）"""
    try:
        alert_data = request.get_json()
        
        # 保存警报到数据库
        with get_db_connection() as db:
            alert_id = db.create(
                """INSERT INTO alarm (location, highway_section, description, vehicle_count,
                                    detection_details, confidence_level, threshold, photo, pid, create_time)
                   VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)""",
                (
                    alert_data['location'],
                    alert_data.get('highway_section', ''),
                    alert_data['description'],
                    alert_data['vehicle_count'],
                    json.dumps(alert_data.get('detection_details', {})),
                    alert_data.get('confidence_level', 0.0),
                    alert_data['threshold'],
                    alert_data.get('photo', ''),
                    alert_data['monitor_id'],
                    datetime.now()
                )
            )
        
        alert_data['id'] = alert_id
        
        # 广播警报
        broadcast_alert(alert_data)
        
        return success_response({'alert_id': alert_id}, '警报推送成功')
        
    except Exception as e:
        return error_response(f'警报推送失败: {str(e)}')
