# 高科技风格的行人检测对话框界面
from PySide6.QtCore import (QCoreApplication, QMetaObject, QObject, QPoint, QRect,
    QSize, Qt, QPropertyAnimation, QEasingCurve, Signal, Property, QTimer)
from PySide6.QtGui import (QBrush, QColor, QConicalGradient, QCursor, QFont,
    QFontDatabase, QGradient, QIcon, QLinearGradient, QPainter, QPalette,
    QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QFrame, QLabel, QLineEdit, QPushButton,
    QSizePolicy, QVBoxLayout, QHBoxLayout, QWidget, QGraphicsDropShadowEffect,
    QCheckBox, QScrollArea, QGridLayout, QMessageBox)
import time

class PedestrianDetectionForm(object):
    def setupUi(self, Form):
        if not Form.objectName():
            Form.setObjectName(u"PedestrianDetectionForm")
        
        # 设置窗口大小和样式 - 增加窗口高度
        Form.resize(700, 620)
        Form.setMinimumSize(QSize(700, 620))
        Form.setMaximumSize(QSize(700, 620))
        Form.setStyleSheet(u"#PedestrianDetectionForm {\n"
                           "    background-color: rgb(173, 216, 230);\n"
                           "    border-radius: 15px;\n"
                           "    border: 2px solid rgb(100, 149, 237);\n"
                           "}")
        
        # 设置无边框窗口
        Form.setWindowFlags(Qt.FramelessWindowHint)
        
        # 主布局
        self.verticalLayout = QVBoxLayout(Form)
        self.verticalLayout.setSpacing(15)
        self.verticalLayout.setContentsMargins(20, 20, 20, 20)
        
        # 标题栏
        self.titleBar = QFrame(Form)
        self.titleBar.setMinimumSize(QSize(0, 50))
        self.titleBar.setMaximumSize(QSize(16777215, 50))
        self.titleBar.setStyleSheet(u"QFrame {\n"
                                   "    border-top-left-radius: 15px;\n"
                                   "    border-top-right-radius: 15px;\n"
                                   "    background-color: rgb(100, 149, 237);\n"
                                   "    border-bottom: 2px solid rgb(70, 130, 180);\n"
                                   "}")
        
        # 标题栏布局
        self.horizontalLayout_title = QHBoxLayout(self.titleBar)
        self.horizontalLayout_title.setSpacing(10)
        self.horizontalLayout_title.setContentsMargins(20, 0, 20, 0)
        
        # 标题图标
        self.titleIcon = QLabel(self.titleBar)
        self.titleIcon.setMinimumSize(QSize(32, 32))
        self.titleIcon.setMaximumSize(QSize(32, 32))
        self.titleIcon.setStyleSheet(u"QLabel {\n"
                                    "    background-color: transparent;\n"
                                    "    background-image: url(./ui/img/pedestrian.png);\n"
                                    "    background-repeat: no-repeat;\n"
                                    "    background-position: center;\n"
                                    "    border: none;\n"
                                    "}")
        self.horizontalLayout_title.addWidget(self.titleIcon)
        
        # 标题标签
        self.titleLabel = QLabel(self.titleBar)
        self.titleLabel.setStyleSheet(u"QLabel {\n"
                                     "    color: rgb(240, 240, 255);\n"
                                     "    font: 700 16pt \"Microsoft YaHei UI\";\n"
                                     "    background-color: transparent;\n"
                                     "    border: none;\n"
                                     "}")
        self.titleLabel.setText("行人检测系统")
        self.horizontalLayout_title.addWidget(self.titleLabel)
        
        # 添加弹性空间
        spacerItem = QWidget()
        spacerItem.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.horizontalLayout_title.addWidget(spacerItem)
        
        # 关闭按钮
        self.closeButton = QPushButton(self.titleBar)
        self.closeButton.setMinimumSize(QSize(28, 28))
        self.closeButton.setMaximumSize(QSize(28, 28))
        self.closeButton.setCursor(QCursor(Qt.PointingHandCursor))
        self.closeButton.setStyleSheet(u"QPushButton {\n"
                                      "    border-radius: 14px;\n"
                                      "    background-color: rgba(239, 68, 68, 0.8);\n"
                                      "    border: none;\n"
                                      "}\n"
                                      "\n"
                                      "QPushButton:hover {\n"
                                      "    background-color: rgb(239, 68, 68);\n"
                                      "}")
        self.closeButton.setText("")
        self.horizontalLayout_title.addWidget(self.closeButton)
        
        # 添加标题栏到主布局
        self.verticalLayout.addWidget(self.titleBar)
        
        # 内容区域
        self.contentArea = QFrame(Form)
        self.contentArea.setStyleSheet(u"QFrame {\n"
                                     "    border-radius: 15px;\n"
                                     "    background-color: rgb(220, 240, 255);\n"
                                     "    border: 1px solid rgb(100, 149, 237);\n"
                                     "}")
        
        # 内容区域布局
        self.verticalLayout_content = QVBoxLayout(self.contentArea)
        self.verticalLayout_content.setSpacing(15)  # 减小整体间距
        self.verticalLayout_content.setContentsMargins(25, 25, 25, 25)
        
        # 添加标题
        self.settingsTitle = QLabel(self.contentArea)
        self.settingsTitle.setStyleSheet(u"QLabel {\n"
                                       "    color: rgb(51, 65, 85);\n"
                                       "    font: 700 14pt \"Microsoft YaHei UI\";\n"
                                       "    background-color: transparent;\n"
                                       "    border: none;\n"
                                       "    margin-bottom: 10px;\n"
                                       "}")
        self.settingsTitle.setText("请选择要检测的行人类型:")
        self.verticalLayout_content.addWidget(self.settingsTitle)
        
        # 检测设置区域
        self.detectionFrame = QFrame(self.contentArea)
        self.detectionFrame.setMinimumSize(QSize(0, 320))  # 进一步增加高度解决遮挡
        self.detectionFrame.setStyleSheet(u"QFrame {\n"
                                        "    border-radius: 10px;\n"
                                        "    background-color: rgb(240, 248, 255);\n"
                                        "    border: 1px solid rgb(100, 149, 237);\n"
                                        "}")
        
        # 创建网格布局 - 进一步优化间距解决字体重叠
        self.gridLayout = QGridLayout(self.detectionFrame)
        self.gridLayout.setHorizontalSpacing(25)
        self.gridLayout.setVerticalSpacing(80)  # 进一步增加垂直间距
        self.gridLayout.setContentsMargins(30, 50, 30, 50)  # 增加更多内边距
        
        # 普通行人棄测选项
        self.normalPedestrianCheck = QCheckBox(self.detectionFrame)
        self.normalPedestrianCheck.setStyleSheet(u"QCheckBox {\n"
                                             "    color: rgb(51, 65, 85);\n"
                                             "    font: 12pt \"Microsoft YaHei UI\";\n"
                                             "    background-color: transparent;\n"
                                             "    border: none;\n"
                                             "    padding: 8px;\n"
                                             "    min-height: 30px;\n"
                                             "}\n"
                                             "QCheckBox::indicator {\n"
                                             "    width: 18px;\n"
                                             "    height: 18px;\n"
                                             "    border: 2px solid rgb(100, 149, 237);\n"
                                             "    border-radius: 3px;\n"
                                             "    background-color: white;\n"
                                             "}\n"
                                             "QCheckBox::indicator:checked {\n"
                                             "    background-color: rgb(59, 130, 246);\n"
                                             "    border-color: rgb(59, 130, 246);\n"
                                             "}")
        self.normalPedestrianCheck.setText("普通行人检测")
        self.normalPedestrianCheck.setChecked(True)
        self.gridLayout.addWidget(self.normalPedestrianCheck, 0, 0)
        
        # 置信度阈值标签
        self.confidenceLabel = QLabel(self.detectionFrame)
        self.confidenceLabel.setStyleSheet(u"QLabel {\n"
                                          "    color: rgb(51, 65, 85);\n"
                                          "    font: 12pt \"Microsoft YaHei UI\";\n"
                                          "    background-color: transparent;\n"
                                          "    border: none;\n"
                                          "}")
        self.confidenceLabel.setText("置信度阈值:")
        self.gridLayout.addWidget(self.confidenceLabel, 0, 1)
        
        # 置信度阈值输入框
        self.confidenceEdit = QLineEdit(self.detectionFrame)
        self.confidenceEdit.setMinimumSize(QSize(0, 38))
        self.confidenceEdit.setStyleSheet(u"QLineEdit {\n"
                                         "    border-radius: 5px;\n"
                                         "    background-color: white;\n"
                                         "    color: rgb(51, 65, 85);\n"
                                         "    font: 12pt \"Microsoft YaHei UI\";\n"
                                         "    padding: 5px 8px;\n"
                                         "    border: 1px solid rgb(100, 149, 237);\n"
                                         "}\n"
                                         "QLineEdit:focus {\n"
                                         "    border: 2px solid rgb(59, 130, 246);\n"
                                         "}")
        self.confidenceEdit.setText("0.5")
        self.confidenceEdit.setAlignment(Qt.AlignCenter)
        self.gridLayout.addWidget(self.confidenceEdit, 0, 2)
        
        # 特殊行人检测选项
        self.specialPedestrianCheck = QCheckBox(self.detectionFrame)
        self.specialPedestrianCheck.setStyleSheet(u"QCheckBox {\n"
                                             "    color: rgb(51, 65, 85);\n"
                                             "    font: 12pt \"Microsoft YaHei UI\";\n"
                                             "    background-color: transparent;\n"
                                             "    border: none;\n"
                                             "    padding: 8px;\n"
                                             "    min-height: 30px;\n"
                                             "}\n"
                                             "QCheckBox::indicator {\n"
                                             "    width: 18px;\n"
                                             "    height: 18px;\n"
                                             "    border: 2px solid rgb(100, 149, 237);\n"
                                             "    border-radius: 3px;\n"
                                             "    background-color: white;\n"
                                             "}\n"
                                             "QCheckBox::indicator:checked {\n"
                                             "    background-color: rgb(59, 130, 246);\n"
                                             "    border-color: rgb(59, 130, 246);\n"
                                             "}")
        self.specialPedestrianCheck.setText("特殊行人检测(老人/儿童)")
        self.specialPedestrianCheck.setChecked(True)
        self.gridLayout.addWidget(self.specialPedestrianCheck, 1, 0, 1, 3)
        
        # 行人密度检测选项
        self.densityCheck = QCheckBox(self.detectionFrame)
        self.densityCheck.setStyleSheet(u"QCheckBox {\n"
                                         "    color: rgb(51, 65, 85);\n"
                                         "    font: 12pt \"Microsoft YaHei UI\";\n"
                                         "    background-color: transparent;\n"
                                         "    border: none;\n"
                                         "    padding: 8px;\n"
                                         "    min-height: 30px;\n"
                                         "}\n"
                                         "QCheckBox::indicator {\n"
                                         "    width: 18px;\n"
                                         "    height: 18px;\n"
                                         "    border: 2px solid rgb(100, 149, 237);\n"
                                         "    border-radius: 3px;\n"
                                         "    background-color: white;\n"
                                         "}\n"
                                         "QCheckBox::indicator:checked {\n"
                                         "    background-color: rgb(59, 130, 246);\n"
                                         "    border-color: rgb(59, 130, 246);\n"
                                         "}")
        self.densityCheck.setText("行人密度检测")
        self.densityCheck.setChecked(True)
        self.gridLayout.addWidget(self.densityCheck, 2, 0)
        
        # 密度阈值标签
        self.densityThresholdLabel = QLabel(self.detectionFrame)
        self.densityThresholdLabel.setStyleSheet(u"QLabel {\n"
                                                "    color: rgb(51, 65, 85);\n"
                                                "    font: 12pt \"Microsoft YaHei UI\";\n"
                                                "    background-color: transparent;\n"
                                                "    border: none;\n"
                                                "}")
        self.densityThresholdLabel.setText("密度阈值:")
        self.gridLayout.addWidget(self.densityThresholdLabel, 2, 1)
        
        # 密度阈值输入框
        self.densityThresholdEdit = QLineEdit(self.detectionFrame)
        self.densityThresholdEdit.setMinimumSize(QSize(0, 38))
        self.densityThresholdEdit.setStyleSheet(u"QLineEdit {\n"
                                               "    border-radius: 5px;\n"
                                               "    background-color: white;\n"
                                               "    color: rgb(51, 65, 85);\n"
                                               "    font: 12pt \"Microsoft YaHei UI\";\n"
                                               "    padding: 5px 8px;\n"
                                               "    border: 1px solid rgb(100, 149, 237);\n"
                                               "}\n"
                                               "QLineEdit:focus {\n"
                                               "    border: 2px solid rgb(59, 130, 246);\n"
                                               "}")
        self.densityThresholdEdit.setText("5")
        self.densityThresholdEdit.setAlignment(Qt.AlignCenter)
        self.gridLayout.addWidget(self.densityThresholdEdit, 2, 2)
        
        # 添加检测设置区域到主布局
        self.verticalLayout_content.addWidget(self.detectionFrame)
        
        # 添加统计信息标题
        self.statsTitle = QLabel(self.contentArea)
        self.statsTitle.setStyleSheet(u"QLabel {\n"
                                    "    color: rgb(51, 65, 85);\n"
                                    "    font: 700 14pt \"Microsoft YaHei UI\";\n"
                                    "    background-color: transparent;\n"
                                    "    border: none;\n"
                                    "    margin-top: 15px;\n"
                                    "    margin-bottom: 10px;\n"
                                    "}")
        self.statsTitle.setText("检测统计:")
        self.verticalLayout_content.addWidget(self.statsTitle)
        
        # 统计信息区域
        self.statsFrame = QFrame(self.contentArea)
        self.statsFrame.setMinimumSize(QSize(0, 150))
        self.statsFrame.setStyleSheet(u"QFrame {\n"
                                    "    border-radius: 10px;\n"
                                    "    background-color: rgb(240, 248, 255);\n"
                                    "    border: 1px solid rgb(100, 149, 237);\n"
                                    "}")
        
        # 创建统计信息网格布局
        self.gridLayout_stats = QGridLayout(self.statsFrame)
        self.gridLayout_stats.setHorizontalSpacing(20)
        self.gridLayout_stats.setVerticalSpacing(35)  # 增加统计区域垂直间距
        self.gridLayout_stats.setContentsMargins(30, 30, 30, 30)
        
        # 普通行人统计标签
        self.normalPedestrianLabel = QLabel(self.statsFrame)
        self.normalPedestrianLabel.setStyleSheet(u"QLabel {\n"
                                               "    color: rgb(51, 65, 85);\n"
                                               "    font: 12pt \"Microsoft YaHei UI\";\n"
                                               "    background-color: transparent;\n"
                                               "    border: none;\n"
                                               "}")
        self.normalPedestrianLabel.setText("普通行人:")
        self.gridLayout_stats.addWidget(self.normalPedestrianLabel, 0, 0)
        
        # 普通行人统计数值
        self.normalPedestrianCount = QLabel(self.statsFrame)
        self.normalPedestrianCount.setStyleSheet(u"QLabel {\n"
                                               "    color: rgb(59, 130, 246);\n"
                                               "    font: 700 14pt \"Microsoft YaHei UI\";\n"
                                               "    background-color: transparent;\n"
                                               "    border: none;\n"
                                               "}")
        self.normalPedestrianCount.setText("0")
        self.gridLayout_stats.addWidget(self.normalPedestrianCount, 0, 1)
        
        # 特殊行人统计标签
        self.specialPedestrianLabel = QLabel(self.statsFrame)
        self.specialPedestrianLabel.setStyleSheet(u"QLabel {\n"
                                                "    color: rgb(51, 65, 85);\n"
                                                "    font: 12pt \"Microsoft YaHei UI\";\n"
                                                "    background-color: transparent;\n"
                                                "    border: none;\n"
                                                "}")
        self.specialPedestrianLabel.setText("特殊行人:")
        self.gridLayout_stats.addWidget(self.specialPedestrianLabel, 0, 2)
        
        # 特殊行人统计数值
        self.specialPedestrianCount = QLabel(self.statsFrame)
        self.specialPedestrianCount.setStyleSheet(u"QLabel {\n"
                                                "    color: rgb(59, 130, 246);\n"
                                                "    font: 700 14pt \"Microsoft YaHei UI\";\n"
                                                "    background-color: transparent;\n"
                                                "    border: none;\n"
                                                "}")
        self.specialPedestrianCount.setText("0")
        self.gridLayout_stats.addWidget(self.specialPedestrianCount, 0, 3)
        
        # 密度警告统计标签
        self.densityAlertLabel = QLabel(self.statsFrame)
        self.densityAlertLabel.setStyleSheet(u"QLabel {\n"
                                           "    color: rgb(51, 65, 85);\n"
                                           "    font: 12pt \"Microsoft YaHei UI\";\n"
                                           "    background-color: transparent;\n"
                                           "    border: none;\n"
                                           "}")
        self.densityAlertLabel.setText("密度警告:")
        self.gridLayout_stats.addWidget(self.densityAlertLabel, 1, 0)
        
        # 密度警告统计数值
        self.densityAlertCount = QLabel(self.statsFrame)
        self.densityAlertCount.setStyleSheet(u"QLabel {\n"
                                           "    color: rgb(59, 130, 246);\n"
                                           "    font: 700 14pt \"Microsoft YaHei UI\";\n"
                                           "    background-color: transparent;\n"
                                           "    border: none;\n"
                                           "}")
        self.densityAlertCount.setText("0")
        self.gridLayout_stats.addWidget(self.densityAlertCount, 1, 1)
        
        # 总计统计标签
        self.totalLabel = QLabel(self.statsFrame)
        self.totalLabel.setStyleSheet(u"QLabel {\n"
                                    "    color: rgb(51, 65, 85);\n"
                                    "    font: 12pt \"Microsoft YaHei UI\";\n"
                                    "    background-color: transparent;\n"
                                    "    border: none;\n"
                                    "}")
        self.totalLabel.setText("总计:")
        self.gridLayout_stats.addWidget(self.totalLabel, 1, 2)
        
        # 总计统计数值
        self.totalCount = QLabel(self.statsFrame)
        self.totalCount.setStyleSheet(u"QLabel {\n"
                                    "    color: rgb(59, 130, 246);\n"
                                    "    font: 700 14pt \"Microsoft YaHei UI\";\n"
                                    "    background-color: transparent;\n"
                                    "    border: none;\n"
                                    "}")
        self.totalCount.setText("0")
        self.gridLayout_stats.addWidget(self.totalCount, 1, 3)
        
        # 添加统计信息区域到主布局
        self.verticalLayout_content.addWidget(self.statsFrame)
        
        # 按钮区域
        self.buttonFrame = QFrame(self.contentArea)
        self.buttonFrame.setMinimumSize(QSize(0, 70))
        self.buttonFrame.setMaximumSize(QSize(16777215, 70))
        self.buttonFrame.setStyleSheet(u"QFrame {\n"
                                     "    border-radius: 10px;\n"
                                     "    background-color: transparent;\n"
                                     "    border: none;\n"
                                     "}")
        
        # 按钮布局
        self.horizontalLayout_buttons = QHBoxLayout(self.buttonFrame)
        self.horizontalLayout_buttons.setSpacing(20)
        self.horizontalLayout_buttons.setContentsMargins(0, 0, 0, 0)
        
        # 添加空间
        spacerItem1 = QWidget()
        spacerItem1.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.horizontalLayout_buttons.addWidget(spacerItem1)
        
        # 取消按钮
        self.cancelButton = QPushButton(self.buttonFrame)
        self.cancelButton.setMinimumSize(QSize(140, 45))
        self.cancelButton.setCursor(QCursor(Qt.PointingHandCursor))
        self.cancelButton.setStyleSheet(u"QPushButton {\n"
                                     "    border-radius: 8px;\n"
                                     "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n"
                                     "        stop:0 rgb(148, 163, 184),\n"
                                     "        stop:1 rgb(107, 114, 128));\n"
                                     "    color: white;\n"
                                     "    font: 500 12pt \"Microsoft YaHei UI\";\n"
                                     "    border: none;\n"
                                     "    padding: 8px 16px;\n"
                                     "}\n"
                                     "\n"
                                     "QPushButton:hover {\n"
                                     "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n"
                                     "        stop:0 rgb(107, 114, 128),\n"
                                     "        stop:1 rgb(75, 85, 99));\n"
                                     "}\n"
                                     "\n"
                                     "QPushButton:pressed {\n"
                                     "    background: rgb(75, 85, 99);\n"
                                     "}")
        self.cancelButton.setText("取消")
        self.horizontalLayout_buttons.addWidget(self.cancelButton)
        
        # 应用按钮
        self.applyButton = QPushButton(self.buttonFrame)
        self.applyButton.setMinimumSize(QSize(180, 45))
        self.applyButton.setCursor(QCursor(Qt.PointingHandCursor))
        self.applyButton.setStyleSheet(u"QPushButton {\n"
                                      "    border-radius: 8px;\n"
                                      "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n"
                                      "        stop:0 rgb(59, 130, 246),\n"
                                      "        stop:1 rgb(37, 99, 235));\n"
                                      "    color: white;\n"
                                      "    font: 500 12pt \"Microsoft YaHei UI\";\n"
                                      "    border: none;\n"
                                      "    padding: 8px 16px;\n"
                                      "}\n"
                                      "\n"
                                      "QPushButton:hover {\n"
                                      "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n"
                                      "        stop:0 rgb(37, 99, 235),\n"
                                      "        stop:1 rgb(29, 78, 216));\n"
                                      "}\n"
                                      "\n"
                                      "QPushButton:pressed {\n"
                                      "    background: rgb(29, 78, 216);\n"
                                      "}\n"
                                      "\n"
                                      "QPushButton:disabled {\n"
                                      "    background: rgba(148, 163, 184, 0.5);\n"
                                      "    color: rgba(255, 255, 255, 0.7);\n"
                                      "}")
        self.applyButton.setText("启用行人检测")
        self.horizontalLayout_buttons.addWidget(self.applyButton)

        # 停止按钮
        self.stopButton = QPushButton(self.buttonFrame)
        self.stopButton.setObjectName(u"stopButton")
        self.stopButton.setMinimumSize(QSize(0, 35))
        self.stopButton.setStyleSheet(u"QPushButton {\n"
                                    "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n"
                                    "        stop:0 rgb(239, 68, 68),\n"
                                    "        stop:1 rgb(220, 38, 38));\n"
                                    "    color: white;\n"
                                    "    font: 500 12pt \"Microsoft YaHei UI\";\n"
                                    "    border: none;\n"
                                    "    border-radius: 8px;\n"
                                    "    padding: 8px 16px;\n"
                                    "}\n"
                                    "QPushButton:hover {\n"
                                    "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n"
                                    "        stop:0 rgb(248, 113, 113),\n"
                                    "        stop:1 rgb(239, 68, 68));\n"
                                    "}\n"
                                    "QPushButton:pressed {\n"
                                    "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n"
                                    "        stop:0 rgb(220, 38, 38),\n"
                                    "        stop:1 rgb(185, 28, 28));\n"
                                    "}\n"
                                    "QPushButton:disabled {\n"
                                    "    background: rgba(148, 163, 184, 0.5);\n"
                                    "    color: rgba(255, 255, 255, 0.7);\n"
                                    "}")
        self.stopButton.setText("停止检测")
        self.stopButton.setEnabled(False)  # 初始状态为禁用
        self.horizontalLayout_buttons.addWidget(self.stopButton)
        
        # 添加按钮区域到主布局
        self.verticalLayout_content.addWidget(self.buttonFrame)
        
        # 添加内容区域到主布局
        self.verticalLayout.addWidget(self.contentArea)
        
        # 设置阴影效果
        self.addShadow(self.contentArea)
        self.addShadow(self.detectionFrame)
        self.addShadow(self.statsFrame)
        
        # 设置标签的对齐方式
        self.normalPedestrianLabel.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.specialPedestrianLabel.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.densityAlertLabel.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.totalLabel.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.confidenceLabel.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.densityThresholdLabel.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        
        # 调整标签大小
        self.normalPedestrianCount.setMinimumWidth(50)
        self.specialPedestrianCount.setMinimumWidth(50)
        self.densityAlertCount.setMinimumWidth(50)
        self.totalCount.setMinimumWidth(50)
        
        # 按钮连接信号
        self.closeButton.clicked.connect(Form.close)
        self.cancelButton.clicked.connect(Form.close)
    
    def addShadow(self, widget):
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 0)
        widget.setGraphicsEffect(shadow)


# 实现窗口类
class PedestrianDetectionWindow(QWidget):
    # 定义信号
    configConfirmed = Signal(dict)
    statsUpdated = Signal(dict)
    detectionStarted = Signal()
    detectionStopped = Signal()
    alertTriggered = Signal(dict)  # 新增：警报触发信号

    def __init__(self, yolo_predictor=None):
        super(PedestrianDetectionWindow, self).__init__()

        # 保存YOLO预测器引用，用于算法调用
        self.yolo_predictor = yolo_predictor

        # 设置UI
        self.ui = PedestrianDetectionForm()
        self.ui.setupUi(self)

        # 设置窗口标题和样式
        self.setWindowTitle("行人闯入检测系统")
        self.setup_modern_style()

        # 连接信号和槽
        self.ui.closeButton.clicked.connect(self.close)
        self.ui.cancelButton.clicked.connect(self.close)
        self.ui.applyButton.clicked.connect(self.start_detection)
        self.ui.stopButton.clicked.connect(self.stop_detection)  # 新增停止按钮

        # 初始化拖动变量
        self.dragPos = QPoint()

        # 初始化检测状态
        self.is_detecting = False
        self.detection_timer = QTimer()
        self.detection_timer.timeout.connect(self.update_detection_stats)

        # 初始化统计数据
        self.stats = {
            "普通行人": 0,
            "特殊行人": 0,
            "密度警告": 0,
            "总数": 0,
            "检测帧数": 0,
            "警报次数": 0
        }

        # 初始化检测配置
        self.detection_config = {
            "normal_pedestrian": True,
            "special_pedestrian": True,
            "density_detection": True,
            "confidence_threshold": 0.5,
            "density_threshold": 5,
            "alert_enabled": True,
            "auto_save": True
        }
    
    def setup_modern_style(self):
        """设置简洁的浅蓝色样式"""
        pass  # 样式已在UI组件中单独设置

    def start_detection(self):
        """启动行人检测"""
        if not self.yolo_predictor:
            self.show_warning("错误", "YOLO预测器未初始化，无法启动检测")
            return

        # 获取配置
        self.detection_config = {
            "normal_pedestrian": self.ui.normalPedestrianCheck.isChecked(),
            "special_pedestrian": self.ui.specialPedestrianCheck.isChecked(),
            "density_detection": self.ui.densityCheck.isChecked(),
            "confidence_threshold": float(self.ui.confidenceEdit.text()),
            "density_threshold": int(self.ui.densityThresholdEdit.text()),
            "alert_enabled": True,
            "auto_save": True
        }

        # 配置YOLO预测器的行人检测
        self.configure_yolo_pedestrian_detection()

        # 启动检测
        self.is_detecting = True
        self.detection_timer.start(1000)  # 每秒更新一次统计

        # 更新UI状态
        self.ui.applyButton.setText("检测中...")
        self.ui.applyButton.setEnabled(False)
        self.ui.stopButton.setEnabled(True)

        # 发送信号
        self.configConfirmed.emit(self.detection_config)
        self.detectionStarted.emit()

        self.show_info("成功", "行人检测已启动")

    def stop_detection(self):
        """停止行人检测"""
        self.is_detecting = False
        self.detection_timer.stop()

        # 停止YOLO预测器的行人检测
        if self.yolo_predictor:
            self.yolo_predictor.pedestrian_detection_enabled = False

        # 更新UI状态
        self.ui.applyButton.setText("开始检测")
        self.ui.applyButton.setEnabled(True)
        self.ui.stopButton.setEnabled(False)

        # 发送信号
        self.detectionStopped.emit()

        self.show_info("成功", "行人检测已停止")

    def configure_yolo_pedestrian_detection(self):
        """配置YOLO预测器的行人检测功能"""
        if not self.yolo_predictor:
            return

        # 启用行人检测
        self.yolo_predictor.pedestrian_detection_enabled = True
        self.yolo_predictor.pedestrian_config = self.detection_config

        # 设置行人检测的类别ID (COCO数据集中person的ID是0)
        self.yolo_predictor.pedestrian_classes = [0]  # person类别

        # 设置检测回调函数
        self.yolo_predictor.pedestrian_callback = self.process_pedestrian_detection

        # 初始化统计数据
        self.yolo_predictor.pedestrian_stats = self.stats.copy()
    
    def updateStats(self, stats):
        # 更新统计数据
        self.stats = stats
        
        # 更新UI显示
        self.ui.normalPedestrianCount.setText(str(stats['普通行人']))
        self.ui.specialPedestrianCount.setText(str(stats['特殊行人']))
        self.ui.densityAlertCount.setText(str(stats['密度警告']))
        self.ui.totalCount.setText(str(stats['总数']))
        
        # 发送统计信号
        self.statsUpdated.emit(stats)
    
    def mousePressEvent(self, event):
        # 实现窗口拖动
        if event.button() == Qt.LeftButton:
            self.dragPos = event.globalPosition().toPoint()
    
    def mouseMoveEvent(self, event):
        # 实现窗口拖动
        if event.buttons() == Qt.LeftButton:
            self.move(self.pos() + event.globalPosition().toPoint() - self.dragPos)
            self.dragPos = event.globalPosition().toPoint()
            event.accept()

    def process_pedestrian_detection(self, detections, frame):
        """处理行人检测结果 - 核心算法调用"""
        if not self.is_detecting or not detections:
            return

        pedestrians = []
        special_pedestrians = []

        # 遍历检测结果
        for detection in detections:
            # 检查是否是人类 (class_id = 0 in COCO)
            if detection.get('class_id') == 0:  # person
                confidence = detection.get('confidence', 0)
                bbox = detection.get('bbox', [])

                # 置信度过滤
                if confidence < self.detection_config['confidence_threshold']:
                    continue

                # 分析行人类型
                pedestrian_info = {
                    'bbox': bbox,
                    'confidence': confidence,
                    'center': [(bbox[0] + bbox[2]) / 2, (bbox[1] + bbox[3]) / 2] if len(bbox) >= 4 else [0, 0],
                    'area': (bbox[2] - bbox[0]) * (bbox[3] - bbox[1]) if len(bbox) >= 4 else 0,
                    'timestamp': time.time()
                }

                # 判断是否为特殊行人 (基于大小、位置等特征)
                if self.is_special_pedestrian(pedestrian_info):
                    special_pedestrians.append(pedestrian_info)
                else:
                    pedestrians.append(pedestrian_info)

        # 更新统计数据
        self.stats["普通行人"] = len(pedestrians)
        self.stats["特殊行人"] = len(special_pedestrians)
        self.stats["总数"] = len(pedestrians) + len(special_pedestrians)
        self.stats["检测帧数"] += 1

        # 密度检测
        if self.detection_config['density_detection']:
            if self.stats["总数"] > self.detection_config['density_threshold']:
                self.stats["密度警告"] += 1
                self.trigger_density_alert(self.stats["总数"])

        # 特殊行人警报
        if special_pedestrians and self.detection_config['special_pedestrian']:
            self.trigger_special_pedestrian_alert(special_pedestrians)

        # 更新UI统计
        self.updateStats(self.stats)

    def is_special_pedestrian(self, pedestrian_info):
        """判断是否为特殊行人 (儿童、老人、残疾人等)"""
        # 基于检测框大小判断 (简化算法)
        area = pedestrian_info['area']

        # 如果检测框明显较小，可能是儿童
        if area < 5000:  # 可调整阈值
            return True

        # 可以添加更多特征判断逻辑
        # 例如：姿态分析、移动速度、辅助设备检测等

        return False

    def trigger_density_alert(self, pedestrian_count):
        """触发密度警报"""
        alert_data = {
            'type': 'density_warning',
            'message': f'检测到高密度行人聚集：{pedestrian_count}人',
            'severity': 'high' if pedestrian_count > self.detection_config['density_threshold'] * 2 else 'medium',
            'timestamp': time.time(),
            'count': pedestrian_count
        }

        self.alertTriggered.emit(alert_data)
        self.show_road_warning_dialog(alert_data)

    def trigger_special_pedestrian_alert(self, special_pedestrians):
        """触发特殊行人警报"""
        alert_data = {
            'type': 'special_pedestrian',
            'message': f'检测到{len(special_pedestrians)}名特殊行人',
            'severity': 'medium',
            'timestamp': time.time(),
            'pedestrians': special_pedestrians
        }

        self.alertTriggered.emit(alert_data)
        self.show_road_warning_dialog(alert_data)

    def show_road_warning_dialog(self, alert_data):
        """显示道路预警对话框"""
        try:
            from ui.dialog.road_warning_dialog import RoadWarningDialog

            warning_dialog = RoadWarningDialog(self)
            warning_dialog.set_warning_data({
                'title': '行人闯入预警',
                'icon': '🚶',
                'warnings': [
                    {
                        'icon': '⚠️',
                        'title': alert_data['type'].replace('_', ' ').title(),
                        'description': alert_data['message'],
                        'type': 'danger' if alert_data['severity'] == 'high' else 'warning'
                    },
                    {
                        'icon': '📍',
                        'title': '检测位置',
                        'description': '高速公路主干道',
                        'type': 'info'
                    },
                    {
                        'icon': '🕐',
                        'title': '检测时间',
                        'description': time.strftime('%H:%M:%S', time.localtime(alert_data['timestamp'])),
                        'type': 'info'
                    }
                ]
            })
            warning_dialog.show()

        except ImportError:
            # 如果新的对话框不可用，使用简单的消息框
            self.show_warning("行人闯入预警", alert_data['message'])

    def update_detection_stats(self):
        """定时更新检测统计"""
        if not self.is_detecting:
            return

        # 更新UI显示
        self.updateStats(self.stats)

    def show_warning(self, title, message):
        """显示警告消息"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(QMessageBox.Warning)
        msg_box.exec()

    def show_info(self, title, message):
        """显示信息消息"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(QMessageBox.Information)
        msg_box.exec()