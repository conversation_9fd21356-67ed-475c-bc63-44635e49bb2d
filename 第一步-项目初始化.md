# 第一步：项目初始化 - Vue 3 + Arco Design

## 🎯 目标
创建基于Yolov8与ByteTrack的高速公路智慧监控平台前端项目的基础架构，包括Vue 3、TypeScript、Arco Design的集成配置。

## 📋 执行步骤

### 1. 创建Vue 3项目

```bash
# 使用Vite创建Vue 3 + TypeScript项目
npm create vue@latest highway-monitoring-frontend

# 选择配置项：
# ✅ TypeScript
# ✅ Router
# ✅ Pinia
# ✅ ESLint
# ✅ Prettier
# ❌ Vitest (后续添加)
# ❌ Cypress (后续添加)
# ❌ Playwright (不需要)

cd highway-monitoring-frontend
npm install
```

### 2. 安装Arco Design Vue

```bash
# 安装Arco Design Vue
npm install @arco-design/web-vue

# 安装图标库
npm install @arco-design/web-vue-icons

# 安装其他依赖
npm install axios socket.io-client echarts video.js
npm install @types/video.js -D
```

### 3. 配置Vite (vite.config.ts)

```typescript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:5500',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '/api')
      }
    }
  },
  build: {
    outDir: 'dist',
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          arco: ['@arco-design/web-vue'],
          charts: ['echarts']
        }
      }
    }
  }
})
```

### 4. 配置TypeScript (tsconfig.json)

```json
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    }
  },
  "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
```

### 5. 主入口文件 (src/main.ts)

```typescript
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ArcoVue from '@arco-design/web-vue'
import ArcoVueIcon from '@arco-design/web-vue-icons'
import '@arco-design/web-vue/dist/arco.css'

import App from './App.vue'
import router from './router'
import './styles/global.css'

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(ArcoVue)
app.use(ArcoVueIcon)

app.mount('#app')
```

### 6. 全局样式 (src/styles/global.css)

```css
/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

#app {
  height: 100%;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 工具类 */
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.text-center {
  text-align: center;
}

/* 响应式断点 */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none !important;
  }
}

@media (min-width: 769px) {
  .desktop-hidden {
    display: none !important;
  }
}
```

### 7. 类型定义 (src/types/index.ts)

```typescript
// 通用响应类型
export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  code?: number
}

// 分页响应类型
export interface PaginatedResponse<T> {
  success: boolean
  message: string
  data: {
    items: T[]
    total: number
    page: number
    size: number
    total_pages: number
  }
}

// 用户类型
export interface User {
  id: number
  username: string
  email?: string
  role: string
  avatar?: string
  created_at: string
}

// 登录响应类型
export interface LoginResponse {
  token: string
  user: User
}

// 监控点类型
export interface Monitor {
  id: number
  name: string
  location: string
  road_section: string
  rtsp_url: string
  status: 'online' | 'offline'
  is_alarm: boolean
  threshold: number
  created_at: string
  updated_at?: string
}

// 检测结果类型
export interface Detection {
  id: number
  bbox: [number, number, number, number]
  confidence: number
  class_id: number
  class_name: string
}

// 检测统计类型
export interface DetectionStats {
  total_objects: number
  class_counts: Record<string, number>
  avg_confidence: number
  max_confidence: number
  min_confidence: number
}

// 追踪目标类型
export interface TrackingTarget {
  id: number
  type: string
  confidence: number
  bbox: [number, number, number, number]
  monitor_id: number
  track_id: number
}

// 事故记录类型
export interface AccidentRecord {
  record_id: number
  monitor_id: string
  accident_type: 'collision' | 'sudden_stop' | 'congestion'
  severity: 'low' | 'medium' | 'high'
  location: { x: number; y: number }
  description: string
  detection_time: string
  confidence: number
  status: string
  metadata?: Record<string, any>
}

// WebSocket事件类型
export interface WSDetectionResult {
  monitor_id: number
  detections: Detection[]
  timestamp: string
  frame_id: number
}

export interface WSAccidentAlert {
  monitor_id: number
  accident_type: string
  severity: string
  location: { x: number; y: number }
  timestamp: string
  confidence: number
}

export interface WSTrackingData {
  monitor_id: number
  targets: TrackingTarget[]
  timestamp: string
  algorithm: string
}
```

### 8. 路由配置 (src/router/index.ts)

```typescript
import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/Login/index.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/',
      name: 'Layout',
      component: () => import('@/components/Layout/index.vue'),
      meta: { requiresAuth: true },
      redirect: '/dashboard',
      children: [
        {
          path: '/dashboard',
          name: 'Dashboard',
          component: () => import('@/views/Dashboard/index.vue'),
          meta: { title: '仪表板', icon: 'icon-dashboard' }
        },
        {
          path: '/monitor',
          name: 'Monitor',
          component: () => import('@/views/Monitor/index.vue'),
          meta: { title: '监控点管理', icon: 'icon-camera' }
        },
        {
          path: '/detection',
          name: 'Detection',
          component: () => import('@/views/Detection/index.vue'),
          meta: { title: '检测管理', icon: 'icon-eye' }
        },
        {
          path: '/tracking',
          name: 'Tracking',
          component: () => import('@/views/Tracking/index.vue'),
          meta: { title: '目标追踪', icon: 'icon-location' }
        },
        {
          path: '/accident',
          name: 'Accident',
          component: () => import('@/views/Accident/index.vue'),
          meta: { title: '事故检测', icon: 'icon-exclamation-circle' }
        },
        {
          path: '/analysis',
          name: 'Analysis',
          component: () => import('@/views/Analysis/index.vue'),
          meta: { title: '数据分析', icon: 'icon-bar-chart' }
        },
        {
          path: '/system',
          name: 'System',
          component: () => import('@/views/System/index.vue'),
          meta: { title: '系统管理', icon: 'icon-settings' }
        }
      ]
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('@/views/NotFound/index.vue')
    }
  ]
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  
  if (to.meta.requiresAuth !== false && !authStore.isAuthenticated) {
    next('/login')
  } else if (to.path === '/login' && authStore.isAuthenticated) {
    next('/dashboard')
  } else {
    next()
  }
})

export default router
```

## ✅ 验证步骤

1. **启动开发服务器**
```bash
npm run dev
```

2. **检查项目结构**
确保所有文件都已创建并且没有TypeScript错误

3. **访问应用**
打开 http://localhost:3000 确保应用正常启动

4. **检查Arco Design**
确保Arco Design组件样式正常加载

## 🎯 下一步

项目基础架构完成后，下一步将实现：
1. **认证系统** - 登录页面和用户状态管理
2. **布局组件** - 主要的应用布局结构
3. **API封装** - 统一的HTTP请求处理

**准备好进行下一步了吗？**
