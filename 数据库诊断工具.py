# -*- coding: utf-8 -*-
# 数据库诊断工具 - 全面检查数据库状态和问题

import os
import sys
import pymysql
import json
from datetime import datetime, timedelta

class DatabaseDiagnostic:
    def __init__(self):
        self.config = {
            'host': '127.0.0.1',
            'port': 3306,
            'user': 'root',
            'password': '123456',
            'database': 'yolo',
            'charset': 'utf8mb4'
        }
        self.connection = None
        self.cursor = None
    
    def connect(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(**self.config)
            self.cursor = self.connection.cursor()
            return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
    
    def check_mysql_service(self):
        """检查MySQL服务状态"""
        print("🔍 检查MySQL服务状态...")
        
        try:
            # 尝试连接MySQL服务器（不指定数据库）
            config_no_db = self.config.copy()
            del config_no_db['database']
            
            conn = pymysql.connect(**config_no_db)
            cursor = conn.cursor()
            
            # 获取MySQL版本
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()[0]
            print(f"✅ MySQL服务运行正常")
            print(f"   版本: {version}")
            
            # 检查数据库是否存在
            cursor.execute("SHOW DATABASES LIKE 'yolo'")
            db_exists = cursor.fetchone()
            
            if db_exists:
                print(f"✅ 数据库 'yolo' 存在")
            else:
                print(f"❌ 数据库 'yolo' 不存在")
                print(f"💡 解决方案: 运行 '导入数据库.bat' 或 'python 数据库完整导入.py'")
            
            cursor.close()
            conn.close()
            return True
            
        except Exception as e:
            print(f"❌ MySQL服务检查失败: {e}")
            print(f"💡 解决方案:")
            print(f"   1. 启动MySQL服务")
            print(f"   2. 检查MySQL配置")
            print(f"   3. 确认用户名密码正确")
            return False
    
    def check_table_structure(self):
        """检查表结构完整性"""
        print("\n📋 检查表结构完整性...")
        
        if not self.connect():
            return False
        
        try:
            # 期望的表列表
            expected_tables = {
                'user': '用户表',
                'monitor': '监控点表',
                'detection_task': '检测任务表',
                'detection_result': '检测结果表',
                'tracking_target': '追踪目标表',
                'tracking_performance': '追踪性能表',
                'accident_record': '事故记录表',
                'violation_record': '违规记录表',
                'algorithm_config': '算法配置表',
                'system_config': '系统配置表',
                'model_management': '模型管理表',
                'alarm': '警报表',
                'notification': '通知表',
                'traffic_statistics': '交通统计表',
                'system_log': '系统日志表',
                'performance_metrics': '性能指标表',
                'realtime_data': '实时数据表',
                'websocket_session': 'WebSocket会话表',
                'file_upload': '文件上传表',
                'api_access_log': 'API访问日志表'
            }
            
            # 获取现有表
            self.cursor.execute("SHOW TABLES")
            existing_tables = [table[0] for table in self.cursor.fetchall()]
            
            missing_tables = []
            for table, desc in expected_tables.items():
                if table in existing_tables:
                    print(f"✅ {desc} ({table})")
                else:
                    print(f"❌ {desc} ({table}) - 缺失")
                    missing_tables.append(table)
            
            if missing_tables:
                print(f"\n⚠️ 发现 {len(missing_tables)} 个缺失的表")
                print(f"💡 解决方案: 重新导入完整数据库结构")
                return False
            else:
                print(f"\n✅ 所有 {len(expected_tables)} 个表都存在")
                return True
                
        except Exception as e:
            print(f"❌ 表结构检查失败: {e}")
            return False
        finally:
            self.disconnect()
    
    def check_data_integrity(self):
        """检查数据完整性"""
        print("\n📊 检查数据完整性...")
        
        if not self.connect():
            return False
        
        try:
            # 检查关键表的数据
            data_checks = [
                ('user', '用户数据', 'SELECT COUNT(*) FROM user'),
                ('monitor', '监控点数据', 'SELECT COUNT(*) FROM monitor'),
                ('system_config', '系统配置', 'SELECT COUNT(*) FROM system_config'),
                ('model_management', '模型管理', 'SELECT COUNT(*) FROM model_management'),
                ('alarm', '警报数据', 'SELECT COUNT(*) FROM alarm'),
                ('traffic_statistics', '交通统计', 'SELECT COUNT(*) FROM traffic_statistics')
            ]
            
            all_good = True
            for table, desc, query in data_checks:
                try:
                    self.cursor.execute(query)
                    count = self.cursor.fetchone()[0]
                    if count > 0:
                        print(f"✅ {desc}: {count} 条记录")
                    else:
                        print(f"⚠️ {desc}: 无数据")
                        if table in ['user', 'system_config']:
                            all_good = False
                except Exception as e:
                    print(f"❌ {desc}: 检查失败 - {e}")
                    all_good = False
            
            # 检查用户账号
            print(f"\n👥 检查用户账号...")
            self.cursor.execute("SELECT username, grade, status FROM user")
            users = self.cursor.fetchall()
            
            if users:
                for username, grade, status in users:
                    status_text = "启用" if status == 1 else "禁用"
                    print(f"   - {username} ({grade}) - {status_text}")
            else:
                print(f"❌ 没有用户账号，无法登录系统")
                all_good = False
            
            # 检查监控点配置
            print(f"\n📹 检查监控点配置...")
            self.cursor.execute("SELECT name, location, connection_status FROM monitor LIMIT 5")
            monitors = self.cursor.fetchall()
            
            if monitors:
                for name, location, status in monitors:
                    print(f"   - {name} ({location}) - {status}")
                
                self.cursor.execute("SELECT COUNT(*) FROM monitor")
                total = self.cursor.fetchone()[0]
                if total > 5:
                    print(f"   ... 还有 {total - 5} 个监控点")
            else:
                print(f"⚠️ 没有监控点配置")
            
            return all_good
            
        except Exception as e:
            print(f"❌ 数据完整性检查失败: {e}")
            return False
        finally:
            self.disconnect()
    
    def check_indexes_and_views(self):
        """检查索引和视图"""
        print("\n🔍 检查索引和视图...")
        
        if not self.connect():
            return False
        
        try:
            # 检查视图
            self.cursor.execute("SHOW FULL TABLES WHERE Table_type = 'VIEW'")
            views = self.cursor.fetchall()
            
            if views:
                print(f"✅ 找到 {len(views)} 个视图:")
                for view in views:
                    print(f"   - {view[0]}")
            else:
                print(f"⚠️ 没有找到视图")
            
            # 检查重要索引
            print(f"\n📇 检查重要索引...")
            important_indexes = [
                ('user', 'uk_username'),
                ('monitor', 'idx_location'),
                ('detection_task', 'uk_task_id'),
                ('tracking_target', 'idx_monitor_id'),
                ('accident_record', 'uk_record_id')
            ]
            
            for table, index_name in important_indexes:
                self.cursor.execute(f"""
                    SELECT COUNT(*) FROM information_schema.STATISTICS 
                    WHERE TABLE_SCHEMA = 'yolo' AND TABLE_NAME = '{table}' AND INDEX_NAME = '{index_name}'
                """)
                exists = self.cursor.fetchone()[0] > 0
                
                if exists:
                    print(f"✅ {table}.{index_name}")
                else:
                    print(f"⚠️ {table}.{index_name} - 缺失")
            
            return True
            
        except Exception as e:
            print(f"❌ 索引和视图检查失败: {e}")
            return False
        finally:
            self.disconnect()
    
    def test_backend_compatibility(self):
        """测试后端兼容性"""
        print("\n🧪 测试后端兼容性...")
        
        if not self.connect():
            return False
        
        try:
            # 测试后端常用查询
            test_queries = [
                ("SELECT COUNT(*) FROM user WHERE status = 1", "活跃用户查询"),
                ("SELECT COUNT(*) FROM monitor WHERE connection_status = 'online'", "在线监控点查询"),
                ("SELECT * FROM v_system_overview", "系统概览视图查询"),
                ("SELECT COUNT(*) FROM alarm WHERE status = 'active'", "活跃警报查询"),
                ("SELECT COUNT(*) FROM detection_task WHERE status = 'running'", "运行中任务查询")
            ]
            
            all_passed = True
            for query, desc in test_queries:
                try:
                    self.cursor.execute(query)
                    result = self.cursor.fetchone()
                    print(f"✅ {desc}: {result[0] if result else 'NULL'}")
                except Exception as e:
                    print(f"❌ {desc}: {e}")
                    all_passed = False
            
            return all_passed
            
        except Exception as e:
            print(f"❌ 后端兼容性测试失败: {e}")
            return False
        finally:
            self.disconnect()
    
    def generate_report(self):
        """生成诊断报告"""
        print("\n" + "=" * 80)
        print("📋 数据库诊断报告")
        print("=" * 80)
        
        # 执行所有检查
        checks = [
            ("MySQL服务检查", self.check_mysql_service),
            ("表结构检查", self.check_table_structure),
            ("数据完整性检查", self.check_data_integrity),
            ("索引和视图检查", self.check_indexes_and_views),
            ("后端兼容性测试", self.test_backend_compatibility)
        ]
        
        results = {}
        for check_name, check_func in checks:
            print(f"\n{'='*20} {check_name} {'='*20}")
            results[check_name] = check_func()
        
        # 总结
        print(f"\n" + "=" * 80)
        print("📊 诊断总结")
        print("=" * 80)
        
        passed = sum(results.values())
        total = len(results)
        
        for check_name, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{status} {check_name}")
        
        print(f"\n📈 总体状态: {passed}/{total} 项检查通过")
        
        if passed == total:
            print("🎉 数据库状态良好，系统可以正常运行！")
            print("\n🎯 下一步:")
            print("   1. 启动后端服务: python backend/app_enhanced.py")
            print("   2. 开始前端开发")
        else:
            print("⚠️ 数据库存在问题，需要修复")
            print("\n🔧 建议操作:")
            print("   1. 运行 '导入数据库.bat' 重新导入数据库")
            print("   2. 检查MySQL服务和配置")
            print("   3. 确认用户权限设置")
        
        print("=" * 80)

def main():
    """主函数"""
    print("🔧 基于Yolov8与ByteTrack的高速公路智慧监控平台 - 数据库诊断工具")
    print(f"⏰ 诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    diagnostic = DatabaseDiagnostic()
    diagnostic.generate_report()

if __name__ == "__main__":
    main()
