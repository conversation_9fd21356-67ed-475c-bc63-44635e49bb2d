<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="900" viewBox="0 0 1400 900" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradients -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f7fafc;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="successGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#48bb78;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#38a169;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="warningGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ed8936;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#dd6b20;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="dangerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#f56565;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e53e3e;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="infoGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#4299e1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3182ce;stop-opacity:1" />
    </linearGradient>
    
    <!-- Filters -->
    <filter id="cardShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="8" stdDeviation="16" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
    <filter id="iconShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#000000" flood-opacity="0.15"/>
    </filter>
    <filter id="glowEffect" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/> 
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background with subtle pattern -->
  <rect width="1400" height="900" fill="#f7fafc"/>
  <defs>
    <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
      <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#e2e8f0" stroke-width="0.5" opacity="0.3"/>
    </pattern>
  </defs>
  <rect width="1400" height="900" fill="url(#grid)"/>
  
  <!-- Header Section -->
  <rect width="1400" height="80" fill="url(#primaryGradient)" filter="url(#cardShadow)"/>
  
  <!-- Header Content -->
  <g transform="translate(30, 20)">
    <!-- Logo and Title -->
    <circle cx="25" cy="20" r="18" fill="#ffffff" opacity="0.2"/>
    <text x="20" y="27" font-family="Arial, sans-serif" font-size="16" fill="#ffffff">🏠</text>
    <text x="60" y="30" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#ffffff">基于Yolov8与ByteTrack的高速公路智慧监控平台</text>
    
    <!-- Navigation Menu -->
    <g transform="translate(400, 0)">
      <rect x="0" y="10" width="80" height="30" rx="15" fill="#ffffff" opacity="0.2"/>
      <text x="40" y="30" font-family="Arial, sans-serif" font-size="12" fill="#ffffff" text-anchor="middle">概览</text>
      
      <rect x="100" y="10" width="80" height="30" rx="15" fill="transparent" stroke="#ffffff" stroke-width="1" opacity="0.6"/>
      <text x="140" y="30" font-family="Arial, sans-serif" font-size="12" fill="#ffffff" text-anchor="middle">监控</text>
      
      <rect x="200" y="10" width="80" height="30" rx="15" fill="transparent" stroke="#ffffff" stroke-width="1" opacity="0.6"/>
      <text x="240" y="30" font-family="Arial, sans-serif" font-size="12" fill="#ffffff" text-anchor="middle">分析</text>
      
      <rect x="300" y="10" width="80" height="30" rx="15" fill="transparent" stroke="#ffffff" stroke-width="1" opacity="0.6"/>
      <text x="340" y="30" font-family="Arial, sans-serif" font-size="12" fill="#ffffff" text-anchor="middle">设置</text>
    </g>
    
    <!-- User Menu -->
    <g transform="translate(1250, 0)">
      <circle cx="20" cy="20" r="16" fill="#ffffff" opacity="0.2"/>
      <text x="15" y="27" font-family="Arial, sans-serif" font-size="14" fill="#ffffff">👤</text>
      <text x="45" y="18" font-family="Arial, sans-serif" font-size="12" fill="#ffffff">admin</text>
      <text x="45" y="32" font-family="Arial, sans-serif" font-size="10" fill="#ffffff" opacity="0.8">管理员</text>
      <text x="100" y="25" font-family="Arial, sans-serif" font-size="12" fill="#ffffff">▼</text>
    </g>
  </g>
  
  <!-- Main Content Area -->
  <rect x="30" y="100" width="1340" height="780" rx="16" fill="#ffffff" filter="url(#cardShadow)"/>
  
  <!-- Statistics Section -->
  <g transform="translate(50, 130)">
    <text x="0" y="0" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#2d3748">📊 系统概览</text>
    <text x="0" y="25" font-family="Arial, sans-serif" font-size="14" fill="#718096">实时监控系统运行状态</text>
    
    <!-- Statistics Cards -->
    <!-- Card 1: 监控点总数 -->
    <g transform="translate(0, 50)">
      <rect width="300" height="140" rx="16" fill="url(#cardGradient)" filter="url(#cardShadow)"/>
      <circle cx="40" cy="40" r="24" fill="url(#infoGradient)" filter="url(#iconShadow)"/>
      <text x="35" y="48" font-family="Arial, sans-serif" font-size="18" fill="#ffffff">📍</text>
      
      <text x="80" y="30" font-family="Arial, sans-serif" font-size="14" fill="#718096">监控点总数</text>
      <text x="80" y="60" font-family="Arial, sans-serif" font-size="36" font-weight="bold" fill="#2d3748">50</text>
      <text x="80" y="80" font-family="Arial, sans-serif" font-size="12" fill="#48bb78">↗ +2 较昨日</text>
      
      <!-- Mini chart -->
      <g transform="translate(200, 20)">
        <polyline points="0,60 20,50 40,45 60,40 80,35" fill="none" stroke="#4299e1" stroke-width="2"/>
        <circle cx="80" cy="35" r="3" fill="#4299e1"/>
      </g>
    </g>
    
    <!-- Card 2: 活跃监控点 -->
    <g transform="translate(320, 50)">
      <rect width="300" height="140" rx="16" fill="url(#cardGradient)" filter="url(#cardShadow)"/>
      <circle cx="40" cy="40" r="24" fill="url(#successGradient)" filter="url(#iconShadow)"/>
      <text x="35" y="48" font-family="Arial, sans-serif" font-size="18" fill="#ffffff">🟢</text>
      
      <text x="80" y="30" font-family="Arial, sans-serif" font-size="14" fill="#718096">活跃监控点</text>
      <text x="80" y="60" font-family="Arial, sans-serif" font-size="36" font-weight="bold" fill="#2d3748">45</text>
      <text x="80" y="80" font-family="Arial, sans-serif" font-size="12" fill="#48bb78">90% 在线率</text>
      
      <!-- Status indicator -->
      <g transform="translate(200, 30)">
        <circle cx="20" cy="20" r="15" fill="none" stroke="#48bb78" stroke-width="3" stroke-dasharray="70 30" transform="rotate(-90 20 20)">
          <animateTransform attributeName="transform" attributeType="XML" type="rotate" from="-90 20 20" to="270 20 20" dur="2s" repeatCount="indefinite"/>
        </circle>
        <text x="20" y="26" font-family="Arial, sans-serif" font-size="10" fill="#48bb78" text-anchor="middle">90%</text>
      </g>
    </g>
    
    <!-- Card 3: 今日警报 -->
    <g transform="translate(640, 50)">
      <rect width="300" height="140" rx="16" fill="url(#cardGradient)" filter="url(#cardShadow)"/>
      <circle cx="40" cy="40" r="24" fill="url(#warningGradient)" filter="url(#iconShadow)"/>
      <text x="35" y="48" font-family="Arial, sans-serif" font-size="18" fill="#ffffff">⚠️</text>
      
      <text x="80" y="30" font-family="Arial, sans-serif" font-size="14" fill="#718096">今日警报</text>
      <text x="80" y="60" font-family="Arial, sans-serif" font-size="36" font-weight="bold" fill="#2d3748">35</text>
      <text x="80" y="80" font-family="Arial, sans-serif" font-size="12" fill="#ed8936">↗ +5 较昨日</text>
      
      <!-- Alert pulse effect -->
      <circle cx="250" cy="40" r="8" fill="#ed8936" opacity="0.6">
        <animate attributeName="r" values="8;12;8" dur="2s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.6;0.2;0.6" dur="2s" repeatCount="indefinite"/>
      </circle>
    </g>
    
    <!-- Card 4: 处理中事故 -->
    <g transform="translate(960, 50)">
      <rect width="300" height="140" rx="16" fill="url(#cardGradient)" filter="url(#cardShadow)"/>
      <circle cx="40" cy="40" r="24" fill="url(#dangerGradient)" filter="url(#iconShadow)"/>
      <text x="35" y="48" font-family="Arial, sans-serif" font-size="18" fill="#ffffff">🚨</text>
      
      <text x="80" y="30" font-family="Arial, sans-serif" font-size="14" fill="#718096">处理中事故</text>
      <text x="80" y="60" font-family="Arial, sans-serif" font-size="36" font-weight="bold" fill="#2d3748">8</text>
      <text x="80" y="80" font-family="Arial, sans-serif" font-size="12" fill="#f56565">紧急处理中</text>
      
      <!-- Emergency indicator -->
      <g transform="translate(220, 20)">
        <rect x="0" y="0" width="60" height="20" rx="10" fill="#f56565" opacity="0.8">
          <animate attributeName="opacity" values="0.8;0.4;0.8" dur="1s" repeatCount="indefinite"/>
        </rect>
        <text x="30" y="14" font-family="Arial, sans-serif" font-size="10" fill="#ffffff" text-anchor="middle">紧急</text>
      </g>
    </g>
  </g>
  
  <!-- Chart Section -->
  <g transform="translate(50, 350)">
    <text x="0" y="0" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#2d3748">📈 交通流量分析</text>
    
    <!-- Chart Container -->
    <rect x="0" y="20" width="800" height="320" rx="16" fill="url(#cardGradient)" filter="url(#cardShadow)"/>
    
    <!-- Chart Header -->
    <g transform="translate(20, 40)">
      <text x="0" y="20" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2d3748">交通流量趋势图 (最近24小时)</text>
      
      <!-- Time range selector -->
      <g transform="translate(500, 0)">
        <rect x="0" y="5" width="60" height="25" rx="12" fill="#667eea" opacity="0.8"/>
        <text x="30" y="22" font-family="Arial, sans-serif" font-size="11" fill="#ffffff" text-anchor="middle">24H</text>
        
        <rect x="70" y="5" width="60" height="25" rx="12" fill="#e2e8f0"/>
        <text x="100" y="22" font-family="Arial, sans-serif" font-size="11" fill="#718096" text-anchor="middle">7D</text>
        
        <rect x="140" y="5" width="60" height="25" rx="12" fill="#e2e8f0"/>
        <text x="170" y="22" font-family="Arial, sans-serif" font-size="11" fill="#718096" text-anchor="middle">30D</text>
      </g>
    </g>
    
    <!-- Chart Grid -->
    <g transform="translate(60, 80)" stroke="#e2e8f0" stroke-width="1" opacity="0.5">
      <!-- Vertical grid lines -->
      <line x1="0" y1="0" x2="0" y2="200"/>
      <line x1="120" y1="0" x2="120" y2="200"/>
      <line x1="240" y1="0" x2="240" y2="200"/>
      <line x1="360" y1="0" x2="360" y2="200"/>
      <line x1="480" y1="0" x2="480" y2="200"/>
      <line x1="600" y1="0" x2="600" y2="200"/>
      <line x1="720" y1="0" x2="720" y2="200"/>
      
      <!-- Horizontal grid lines -->
      <line x1="0" y1="0" x2="720" y2="0"/>
      <line x1="0" y1="50" x2="720" y2="50"/>
      <line x1="0" y1="100" x2="720" y2="100"/>
      <line x1="0" y1="150" x2="720" y2="150"/>
      <line x1="0" y1="200" x2="720" y2="200"/>
    </g>
    
    <!-- Y-axis labels -->
    <g transform="translate(40, 85)" font-family="Arial, sans-serif" font-size="12" fill="#718096">
      <text x="0" y="5" text-anchor="end">300</text>
      <text x="0" y="55" text-anchor="end">225</text>
      <text x="0" y="105" text-anchor="end">150</text>
      <text x="0" y="155" text-anchor="end">75</text>
      <text x="0" y="205" text-anchor="end">0</text>
    </g>
    
    <!-- X-axis labels -->
    <g transform="translate(60, 300)" font-family="Arial, sans-serif" font-size="12" fill="#718096">
      <text x="0" y="0" text-anchor="middle">00:00</text>
      <text x="120" y="0" text-anchor="middle">04:00</text>
      <text x="240" y="0" text-anchor="middle">08:00</text>
      <text x="360" y="0" text-anchor="middle">12:00</text>
      <text x="480" y="0" text-anchor="middle">16:00</text>
      <text x="600" y="0" text-anchor="middle">20:00</text>
      <text x="720" y="0" text-anchor="middle">24:00</text>
    </g>
    
    <!-- Traffic Flow Chart -->
    <g transform="translate(60, 80)">
      <!-- Area fill -->
      <path d="M 0,180 L 60,160 L 120,120 L 180,80 L 240,75 L 300,85 L 360,95 L 420,110 L 480,125 L 540,140 L 600,155 L 660,165 L 720,170 L 720,200 L 0,200 Z" 
            fill="url(#primaryGradient)" opacity="0.2"/>
      
      <!-- Main line -->
      <polyline points="0,180 60,160 120,120 180,80 240,75 300,85 360,95 420,110 480,125 540,140 600,155 660,165 720,170" 
                fill="none" stroke="url(#primaryGradient)" stroke-width="3" stroke-linecap="round"/>
      
      <!-- Data points -->
      <circle cx="0" cy="180" r="4" fill="#667eea" filter="url(#glowEffect)"/>
      <circle cx="60" cy="160" r="4" fill="#667eea" filter="url(#glowEffect)"/>
      <circle cx="120" cy="120" r="4" fill="#667eea" filter="url(#glowEffect)"/>
      <circle cx="180" cy="80" r="4" fill="#667eea" filter="url(#glowEffect)"/>
      <circle cx="240" cy="75" r="4" fill="#667eea" filter="url(#glowEffect)"/>
      <circle cx="300" cy="85" r="4" fill="#667eea" filter="url(#glowEffect)"/>
      <circle cx="360" cy="95" r="4" fill="#667eea" filter="url(#glowEffect)"/>
      <circle cx="420" cy="110" r="4" fill="#667eea" filter="url(#glowEffect)"/>
      <circle cx="480" cy="125" r="4" fill="#667eea" filter="url(#glowEffect)"/>
      <circle cx="540" cy="140" r="4" fill="#667eea" filter="url(#glowEffect)"/>
      <circle cx="600" cy="155" r="4" fill="#667eea" filter="url(#glowEffect)"/>
      <circle cx="660" cy="165" r="4" fill="#667eea" filter="url(#glowEffect)"/>
      <circle cx="720" cy="170" r="4" fill="#667eea" filter="url(#glowEffect)"/>
    </g>
  </g>
  
  <!-- Real-time Metrics Panel -->
  <g transform="translate(870, 350)">
    <rect x="0" y="20" width="390" height="320" rx="16" fill="url(#cardGradient)" filter="url(#cardShadow)"/>
    
    <text x="20" y="0" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#2d3748">🔄 实时指标</text>
    
    <!-- Current Traffic -->
    <g transform="translate(20, 60)">
      <text x="0" y="0" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2d3748">当前交通量</text>
      <text x="0" y="30" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="#667eea">245</text>
      <text x="80" y="30" font-family="Arial, sans-serif" font-size="14" fill="#718096">辆/小时</text>
      
      <!-- Progress bar -->
      <rect x="0" y="40" width="300" height="8" rx="4" fill="#e2e8f0"/>
      <rect x="0" y="40" width="196" height="8" rx="4" fill="url(#primaryGradient)"/>
      <text x="0" y="60" font-family="Arial, sans-serif" font-size="12" fill="#718096">容量: 65%</text>
    </g>
    
    <!-- Average Speed -->
    <g transform="translate(20, 140)">
      <text x="0" y="0" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2d3748">平均车速</text>
      <text x="0" y="30" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="#48bb78">85</text>
      <text x="60" y="30" font-family="Arial, sans-serif" font-size="14" fill="#718096">km/h</text>
      
      <!-- Speed gauge -->
      <g transform="translate(200, 0)">
        <circle cx="40" cy="25" r="30" fill="none" stroke="#e2e8f0" stroke-width="6"/>
        <circle cx="40" cy="25" r="30" fill="none" stroke="#48bb78" stroke-width="6" 
                stroke-dasharray="126" stroke-dashoffset="37" transform="rotate(-90 40 25)"/>
        <text x="40" y="30" font-family="Arial, sans-serif" font-size="12" fill="#48bb78" text-anchor="middle">85</text>
      </g>
    </g>
    
    <!-- Detection Accuracy -->
    <g transform="translate(20, 220)">
      <text x="0" y="0" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2d3748">检测精度</text>
      <text x="0" y="30" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="#ed8936">94.2</text>
      <text x="80" y="30" font-family="Arial, sans-serif" font-size="14" fill="#718096">%</text>
      
      <!-- Accuracy indicator -->
      <g transform="translate(200, 10)">
        <rect x="0" y="0" width="100" height="20" rx="10" fill="#e2e8f0"/>
        <rect x="0" y="0" width="94" height="20" rx="10" fill="#ed8936"/>
        <text x="50" y="14" font-family="Arial, sans-serif" font-size="10" fill="#ffffff" text-anchor="middle">94.2%</text>
      </g>
    </g>
  </g>
  
  <!-- Latest Alerts Section -->
  <g transform="translate(50, 700)">
    <text x="0" y="0" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#2d3748">🚨 最新警报</text>
    
    <!-- Alerts Table -->
    <rect x="0" y="20" width="1210" height="140" rx="16" fill="url(#cardGradient)" filter="url(#cardShadow)"/>
    
    <!-- Table Header -->
    <g transform="translate(20, 50)">
      <rect x="0" y="0" width="1170" height="35" rx="8" fill="#f7fafc" stroke="#e2e8f0" stroke-width="1"/>
      <text x="20" y="25" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2d3748">时间</text>
      <text x="150" y="25" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2d3748">监控点</text>
      <text x="300" y="25" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2d3748">类型</text>
      <text x="450" y="25" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2d3748">严重程度</text>
      <text x="600" y="25" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2d3748">状态</text>
      <text x="750" y="25" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2d3748">处理人员</text>
      <text x="950" y="25" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2d3748">操作</text>
    </g>
    
    <!-- Alert Rows -->
    <!-- Row 1 -->
    <g transform="translate(20, 85)">
      <rect x="0" y="0" width="1170" height="25" fill="#ffffff" opacity="0.8"/>
      <text x="20" y="18" font-family="Arial, sans-serif" font-size="12" fill="#4a5568">14:30:25</text>
      <text x="150" y="18" font-family="Arial, sans-serif" font-size="12" fill="#4a5568">监控点001</text>
      <text x="300" y="18" font-family="Arial, sans-serif" font-size="12" fill="#4a5568">车辆超速</text>
      
      <rect x="450" y="5" width="70" height="18" rx="9" fill="#ed8936"/>
      <text x="485" y="16" font-family="Arial, sans-serif" font-size="10" fill="#ffffff" text-anchor="middle">中等</text>
      
      <rect x="600" y="5" width="70" height="18" rx="9" fill="#4299e1"/>
      <text x="635" y="16" font-family="Arial, sans-serif" font-size="10" fill="#ffffff" text-anchor="middle">处理中</text>
      
      <text x="750" y="18" font-family="Arial, sans-serif" font-size="12" fill="#4a5568">张三</text>
      
      <rect x="950" y="5" width="60" height="18" rx="9" fill="#48bb78"/>
      <text x="980" y="16" font-family="Arial, sans-serif" font-size="10" fill="#ffffff" text-anchor="middle">查看</text>
    </g>
    
    <!-- Row 2 -->
    <g transform="translate(20, 110)">
      <rect x="0" y="0" width="1170" height="25" fill="#f7fafc" opacity="0.8"/>
      <text x="20" y="18" font-family="Arial, sans-serif" font-size="12" fill="#4a5568">14:28:15</text>
      <text x="150" y="18" font-family="Arial, sans-serif" font-size="12" fill="#4a5568">监控点003</text>
      <text x="300" y="18" font-family="Arial, sans-serif" font-size="12" fill="#4a5568">违章变道</text>
      
      <rect x="450" y="5" width="70" height="18" rx="9" fill="#48bb78"/>
      <text x="485" y="16" font-family="Arial, sans-serif" font-size="10" fill="#ffffff" text-anchor="middle">低</text>
      
      <rect x="600" y="5" width="70" height="18" rx="9" fill="#48bb78"/>
      <text x="635" y="16" font-family="Arial, sans-serif" font-size="10" fill="#ffffff" text-anchor="middle">已处理</text>
      
      <text x="750" y="18" font-family="Arial, sans-serif" font-size="12" fill="#4a5568">李四</text>
      
      <rect x="950" y="5" width="60" height="18" rx="9" fill="#718096"/>
      <text x="980" y="16" font-family="Arial, sans-serif" font-size="10" fill="#ffffff" text-anchor="middle">完成</text>
    </g>
    
    <!-- Row 3 -->
    <g transform="translate(20, 135)">
      <rect x="0" y="0" width="1170" height="25" fill="#ffffff" opacity="0.8"/>
      <text x="20" y="18" font-family="Arial, sans-serif" font-size="12" fill="#4a5568">14:25:40</text>
      <text x="150" y="18" font-family="Arial, sans-serif" font-size="12" fill="#4a5568">监控点002</text>
      <text x="300" y="18" font-family="Arial, sans-serif" font-size="12" fill="#4a5568">事故检测</text>
      
      <rect x="450" y="5" width="70" height="18" rx="9" fill="#f56565"/>
      <text x="485" y="16" font-family="Arial, sans-serif" font-size="10" fill="#ffffff" text-anchor="middle">高</text>
      
      <rect x="600" y="5" width="70" height="18" rx="9" fill="#f56565"/>
      <text x="635" y="16" font-family="Arial, sans-serif" font-size="10" fill="#ffffff" text-anchor="middle">紧急</text>
      
      <text x="750" y="18" font-family="Arial, sans-serif" font-size="12" fill="#4a5568">王五</text>
      
      <rect x="950" y="5" width="60" height="18" rx="9" fill="#f56565"/>
      <text x="980" y="16" font-family="Arial, sans-serif" font-size="10" fill="#ffffff" text-anchor="middle">处理</text>
    </g>
  </g>
</svg>