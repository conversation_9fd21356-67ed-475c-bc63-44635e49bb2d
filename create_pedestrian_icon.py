# 创建新的行人图标
import os
import math
from PIL import Image, ImageDraw

# 创建一个32x32的透明RGBA图像
img = Image.new('RGBA', (32, 32), (0, 0, 0, 0))
draw = ImageDraw.Draw(img)

# 创建一个更漂亮的行人图标
# 圆形背景
draw.ellipse((0, 0, 32, 32), fill=(0, 120, 220, 180))

# 头部
draw.ellipse((11, 3, 21, 13), fill=(255, 255, 255, 255))

# 身体
draw.rectangle((14, 12, 18, 22), fill=(255, 255, 255, 255))

# 双臂
draw.pieslice((6, 12, 18, 24), start=180, end=270, fill=(255, 255, 255, 255))
draw.pieslice((14, 12, 26, 24), start=270, end=360, fill=(255, 255, 255, 255))

# 双腿
draw.line((14, 22, 11, 29), fill=(255, 255, 255, 255), width=3)
draw.line((18, 22, 21, 29), fill=(255, 255, 255, 255), width=3)

# 保存图像
output_path = "ui/img/pedestrian.png"
img.save(output_path)

print(f"图标已保存到 {output_path}")

# 打印出图标的尺寸和文件大小
file_size = os.path.getsize(output_path)
print(f"图标尺寸: 32x32, 文件大小: {file_size} 字节") 