# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'dialog.ui'
##
## Created by: Qt User Interface Compiler version 6.4.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt, Signal)
from PySide6.QtGui import (Q<PERSON><PERSON>, QColor, Q<PERSON><PERSON>al<PERSON><PERSON><PERSON>, Q<PERSON>ursor,
    <PERSON><PERSON><PERSON>, QFontDatabase, QGradient, QIcon,
    QImage, Q<PERSON>eySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QDialog, QHBoxLayout, QLabel, QLineEdit,
    QPushButton, QSizePolicy, QWidget, QComboBox, QVBoxLayout, QGridLayout, QMessageBox)
import cv2
from ui.toast.toast import DialogOver

class RTSPDialog(QDialog):
    id_confirmed = Signal(str)  # 添加信号用于传递RTSP地址
    
    def __init__(self):
        super(RTSPDialog, self).__init__()
        self.setupUi()
        self.setWindowTitle("RTSP/HTTP流地址")
        
        # 连接信号
        self.testButton.clicked.connect(self.test_connection)
        self.rtspButton.clicked.connect(self.confirm_rtsp)
        self.streamPathCombo.currentIndexChanged.connect(self.update_rtsp_path)
    
    def update_rtsp_path(self):
        # 当选择新的路径时更新完整URL
        if not self.rtspEdit.text().strip():
            return
            
        ip_port = self.rtspEdit.text().strip()
        # 如果已经是完整URL格式则不修改
        if ip_port.startswith(('rtsp://', 'http://', 'https://')):
            # 提取基本的ip:port部分
            if '://' in ip_port:
                protocol, rest = ip_port.split('://', 1)
                if '/' in rest:
                    ip_port = rest.split('/', 1)[0]
                else:
                    ip_port = rest
        
        # 获取选中的路径
        stream_path = self.streamPathCombo.currentText()
        # 组合完整的RTSP URL
        full_url = f"rtsp://{ip_port}{stream_path}"
        # 更新显示(注意我们不直接修改编辑框内容)
        print(f"已选择路径: {full_url}")
    
    def test_connection(self):
        """测试RTSP连接"""
        if not self.rtspEdit.text().strip():
            QMessageBox.warning(self, "警告", "请先输入服务器IP和端口")
            return
        
        # 构建完整URL
        ip_port = self.rtspEdit.text().strip()
        
        # 如果已经是完整URL则直接使用
        if ip_port.startswith(('rtsp://', 'http://', 'https://')):
            test_url = ip_port
        else:
            stream_path = self.streamPathCombo.currentText()
            username = self.usernameEdit.text().strip()
            password = self.passwordEdit.text().strip()
            
            # 构建包含用户名和密码的URL
            if username and password:
                test_url = f"rtsp://{username}:{password}@{ip_port}{stream_path}"
            else:
                test_url = f"rtsp://{ip_port}{stream_path}"
        
        print(f"已选择路径: {test_url}")
        
        try:
            # 尝试打开流
            print(f"测试连接: {test_url}")
            cap = cv2.VideoCapture(test_url)
            ret, frame = cap.read()
            cap.release()
            
            if ret:
                DialogOver(parent=self, text=f"连接成功! 获取到视频帧", title="测试成功", flags="success")
                # 如果测试成功，则更新编辑框
                self.rtspEdit.setText(test_url)
                return True
            else:
                DialogOver(parent=self, text=f"无法获取视频帧. 请检查URL.", title="连接失败", flags="warning")
                return False
        except Exception as e:
            DialogOver(parent=self, text=f"连接错误: {str(e)}", title="测试失败", flags="danger")
            return False
    
    def confirm_rtsp(self):
        """确认并发送RTSP地址"""
        if not self.rtspEdit.text().strip():
            QMessageBox.warning(self, "警告", "请先输入服务器IP和端口")
            return
        
        # 构建完整URL
        ip_port = self.rtspEdit.text().strip()
        if ip_port.startswith(('rtsp://', 'http://', 'https://')):
            rtsp_url = ip_port
        else:
            # 构建包含用户名和密码的URL
            stream_path = self.streamPathCombo.currentText()
            username = self.usernameEdit.text().strip()
            password = self.passwordEdit.text().strip()
            
            # 构建包含用户名和密码的URL
            if username and password:
                rtsp_url = f"rtsp://{username}:{password}@{ip_port}{stream_path}"
            else:
                rtsp_url = f"rtsp://{ip_port}{stream_path}"
        
        print(f"最新RTSP URL: {rtsp_url}")
        # 发送信号
        self.id_confirmed.emit(rtsp_url)
        self.close()
    
    def closeWindow(self):
        self.close()

class Ui_Form(object):
    def setupUi(self, Form):
        if not Form.objectName():
            Form.setObjectName("Form")
        Form.resize(600, 120)
        Form.setMinimumSize(QSize(600, 120))
        Form.setMaximumSize(QSize(750, 150))
        icon = QIcon()
        icon.addFile(":/all/img/logo.jpg", QSize(), QIcon.Normal, QIcon.Off)
        Form.setWindowIcon(icon)
        Form.setStyleSheet("#Form{\n"
"background:qlineargradient(x0:0, y0:1, x1:1, y1:1,stop:0.4  rgb(48, 167, 217), stop:1 rgb(5,150,229))\n"
"}")
        
        # 使用网格布局
        self.gridLayout = QGridLayout(Form)
        self.gridLayout.setObjectName("gridLayout")
        
        # 添加IP地址标签和输入框
        self.label = QLabel(Form)
        self.label.setObjectName("ipLabel")
        self.label.setStyleSheet("QLabel{font-family: \"Microsoft YaHei\";\nfont-size: 14px;\nfont-weight: bold;\ncolor:white;}")
        self.gridLayout.addWidget(self.label, 0, 0, 1, 1)
        
        self.rtspEdit = QLineEdit(Form)
        self.rtspEdit.setObjectName("rtspEdit")
        self.rtspEdit.setMinimumSize(QSize(200, 30))
        self.rtspEdit.setStyleSheet("background-color: rgb(207, 207, 207);")
        self.gridLayout.addWidget(self.rtspEdit, 0, 1, 1, 3)
        
        # 添加路径标签和选择框
        self.pathLabel = QLabel(Form)
        self.pathLabel.setObjectName("pathLabel")
        self.pathLabel.setStyleSheet("QLabel{font-family: \"Microsoft YaHei\";\nfont-size: 14px;\nfont-weight: bold;\ncolor:white;}")
        self.gridLayout.addWidget(self.pathLabel, 1, 0, 1, 1)
        
        self.streamPathCombo = QComboBox(Form)
        self.streamPathCombo.setObjectName("streamPathCombo")
        self.streamPathCombo.setMinimumSize(QSize(150, 30))
        self.streamPathCombo.setStyleSheet("background-color: rgb(207, 207, 207);")
        self.streamPathCombo.addItem("/h264_ulaw.sdp")
        self.streamPathCombo.addItem("/h264_pcm.sdp")
        self.streamPathCombo.addItem("/h264")
        self.streamPathCombo.addItem("/")
        self.streamPathCombo.setEditable(True)
        self.gridLayout.addWidget(self.streamPathCombo, 1, 1, 1, 3)
        
        # 添加用户名标签和输入框
        self.usernameLabel = QLabel(Form)
        self.usernameLabel.setObjectName("usernameLabel")
        self.usernameLabel.setStyleSheet("QLabel{font-family: \"Microsoft YaHei\";\nfont-size: 14px;\nfont-weight: bold;\ncolor:white;}")
        self.gridLayout.addWidget(self.usernameLabel, 2, 0, 1, 1)
        
        self.usernameEdit = QLineEdit(Form)
        self.usernameEdit.setObjectName("usernameEdit")
        self.usernameEdit.setMinimumSize(QSize(100, 30))
        self.usernameEdit.setStyleSheet("background-color: rgb(207, 207, 207);")
        self.usernameEdit.setText("admin")  # 默认用户名
        self.gridLayout.addWidget(self.usernameEdit, 2, 1, 1, 1)
        
        # 添加密码标签和输入框
        self.passwordLabel = QLabel(Form)
        self.passwordLabel.setObjectName("passwordLabel")
        self.passwordLabel.setStyleSheet("QLabel{font-family: \"Microsoft YaHei\";\nfont-size: 14px;\nfont-weight: bold;\ncolor:white;}")
        self.gridLayout.addWidget(self.passwordLabel, 2, 2, 1, 1)
        
        self.passwordEdit = QLineEdit(Form)
        self.passwordEdit.setObjectName("passwordEdit")
        self.passwordEdit.setMinimumSize(QSize(100, 30))
        self.passwordEdit.setStyleSheet("background-color: rgb(207, 207, 207);")
        self.passwordEdit.setEchoMode(QLineEdit.Password)  # 设置密码显示模式
        self.passwordEdit.setText("admin")  # 默认密码
        self.gridLayout.addWidget(self.passwordEdit, 2, 3, 1, 1)
        
        # 添加按钮布局
        self.buttonLayout = QHBoxLayout()
        self.buttonLayout.setObjectName("buttonLayout")
        
        # 添加测试按钮
        self.testButton = QPushButton(Form)
        self.testButton.setObjectName("testButton")
        self.testButton.setMinimumSize(QSize(100, 30))
        self.testButton.setStyleSheet("QPushButton{font-family: \"Microsoft YaHei\";\nfont-size: 14px;\nfont-weight: bold;\ncolor:white;\nbackground-color: rgb(50, 150, 250);\nborder-radius: 5px;}\nQPushButton:hover{\nbackground-color: rgb(100, 180, 250);}")
        self.buttonLayout.addWidget(self.testButton)
        
        # 添加确认按钮
        self.rtspButton = QPushButton(Form)
        self.rtspButton.setObjectName("rtspButton")
        self.rtspButton.setMinimumSize(QSize(100, 30))
        self.rtspButton.setStyleSheet("QPushButton{font-family: \"Microsoft YaHei\";\nfont-size: 14px;\nfont-weight: bold;\ncolor:white;\nbackground-color: rgb(50, 150, 250);\nborder-radius: 5px;}\nQPushButton:hover{\nbackground-color: rgb(100, 180, 250);}")
        self.buttonLayout.addWidget(self.rtspButton)
        
        # 将按钮布局添加到网格布局
        self.gridLayout.addLayout(self.buttonLayout, 3, 0, 1, 4)
        
        self.retranslateUi(Form)
        QMetaObject.connectSlotsByName(Form)
        self.passwordEdit.setMinimumSize(QSize(100, 30))
        self.passwordEdit.setStyleSheet("background-color: rgb(207, 207, 207);")
        self.passwordEdit.setEchoMode(QLineEdit.Password)
        self.passwordEdit.setText("admin")  # 默认密码
        self.gridLayout.addWidget(self.passwordEdit, 2, 3, 1, 1)
        
        # 添加按钮
        self.buttonLayout = QHBoxLayout()
        self.buttonLayout.setObjectName("buttonLayout")
        
        self.testButton = QPushButton(Form)
        self.testButton.setObjectName("testButton")
        self.testButton.setMinimumSize(QSize(100, 30))
        self.testButton.setStyleSheet("QPushButton{font-family: \"Microsoft YaHei\";\nfont-size: 14px;\ncolor:white;\nbackground-color: rgba(38, 160, 75, 0.5);\nborder-width: 0px;\nborder-radius: 3px;}\nQPushButton:hover{color:white;\nbackground-color: rgb(38, 160, 75);}\nQPushButton:pressed{color:white;\nbackground-color: rgb(38, 160, 75);}")
        self.buttonLayout.addWidget(self.testButton)
        
        self.rtspButton = QPushButton(Form)
        self.rtspButton.setObjectName("rtspButton")
        self.rtspButton.setMinimumSize(QSize(100, 30))
        self.rtspButton.setStyleSheet("QPushButton{font-family: \"Microsoft YaHei\";\nfont-size: 14px;\ncolor:white;\nbackground-color: rgba(11, 109, 207,0.5);\nborder-width: 0px;\nborder-radius: 3px;}\nQPushButton:hover{color:white;\nbackground-color: rgb(11, 109, 207);}\nQPushButton:pressed{color:white;\nbackground-color: rgb(11, 109, 207);}")
        self.buttonLayout.addWidget(self.rtspButton)
        
        self.gridLayout.addLayout(self.buttonLayout, 3, 0, 1, 4)
        
        self.retranslateUi(Form)
        QMetaObject.connectSlotsByName(Form)

    def retranslateUi(self, Form):
        Form.setWindowTitle(QCoreApplication.translate("Form", "RTSP摄像头连接", None))
        self.label.setText(QCoreApplication.translate("Form", "IP地址", None))
        self.rtspEdit.setPlaceholderText(QCoreApplication.translate("Form", "IP摄像头地址 (例如: *************:8080)", None))
        self.pathLabel.setText(QCoreApplication.translate("Form", "路径", None))
        self.usernameLabel.setText(QCoreApplication.translate("Form", "用户名", None))
        self.passwordLabel.setText(QCoreApplication.translate("Form", "密码", None))
        self.testButton.setText(QCoreApplication.translate("Form", "测试连接", None))
        self.rtspButton.setText(QCoreApplication.translate("Form", "确定", None))
