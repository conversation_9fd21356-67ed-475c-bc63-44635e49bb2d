# @Description : 事故检测API接口
# @Date : 2025年6月20日

import json
import time
from datetime import datetime, timedelta
from flask import request, jsonify
from . import api_v1
from .auth import token_required
from utils.response import success_response, error_response
from utils.database import get_db_connection
from services.accident_service import AccidentService

# 初始化事故检测服务
accident_service = AccidentService()

@api_v1.route('/accident/start', methods=['POST'])
@token_required
def start_accident_detection(current_user_id):
    """启动事故检测"""
    try:
        data = request.get_json()
        monitor_id = data.get('monitor_id')
        
        if not monitor_id:
            return error_response('监控点ID不能为空')
        
        # 启动事故检测
        result = accident_service.start_detection(str(monitor_id))
        
        if result['success']:
            return success_response({
                'monitor_id': monitor_id,
                'status': 'running',
                'start_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }, result['message'])
        else:
            return error_response(result['message'])
            
    except Exception as e:
        return error_response(f'启动事故检测失败: {str(e)}')

@api_v1.route('/accident/config', methods=['POST'])
@token_required
def configure_accident_detection(current_user_id):
    """配置事故检测参数"""
    try:
        data = request.get_json()
        monitor_id = data.get('monitor_id')
        config = {
            'enable_collision': data.get('enable_collision', True),
            'enable_sudden_stop': data.get('enable_sudden_stop', True),
            'enable_congestion': data.get('enable_congestion', True),
            'collision_threshold': data.get('collision_threshold', 0.8),
            'sudden_stop_threshold': data.get('sudden_stop_threshold', 5.0),
            'congestion_density_threshold': data.get('congestion_density_threshold', 0.7),
            'congestion_speed_threshold': data.get('congestion_speed_threshold', 20),
            'alert_cooldown': data.get('alert_cooldown', 300),
            'confidence_threshold': data.get('confidence_threshold', 0.85)
        }
        
        with get_db_connection() as db:
            # 检查是否已存在配置
            existing = db.get_one(
                "SELECT id FROM algorithm_configs WHERE monitor_id=%s AND config_type='accident'",
                (monitor_id,)
            )
            
            if existing:
                # 更新配置
                db.execute(
                    "UPDATE algorithm_configs SET config_data=%s, update_time=NOW() WHERE id=%s",
                    (json.dumps(config), existing['id'])
                )
            else:
                # 插入新配置
                db.execute(
                    "INSERT INTO algorithm_configs (monitor_id, config_type, algorithm_name, config_data, create_by) VALUES (%s, %s, %s, %s, %s)",
                    (monitor_id, 'accident', 'accident_detector', json.dumps(config), current_user_id)
                )
        
        return success_response(config, '事故检测配置更新成功')
        
    except Exception as e:
        return error_response(f'配置事故检测失败: {str(e)}')

@api_v1.route('/accident/statistics', methods=['GET'])
@token_required
def get_accident_statistics(current_user_id):
    """获取事故统计数据"""
    try:
        monitor_id = request.args.get('monitor_id')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        accident_type = request.args.get('accident_type')
        
        # 构建查询条件
        conditions = []
        params = []
        
        if monitor_id:
            conditions.append('a.monitor_id = %s')
            params.append(monitor_id)
            
        if start_date:
            conditions.append('a.accident_time >= %s')
            params.append(start_date)
            
        if end_date:
            conditions.append('a.accident_time <= %s')
            params.append(end_date)
            
        if accident_type:
            conditions.append('a.accident_type = %s')
            params.append(accident_type)
        
        where_clause = ' AND '.join(conditions) if conditions else '1=1'
        
        with get_db_connection() as db:
            # 获取事故统计
            stats_query = f"""
                SELECT 
                    a.accident_type,
                    a.severity_level,
                    COUNT(*) as count,
                    AVG(TIMESTAMPDIFF(MINUTE, a.accident_time, a.response_time)) as avg_response_time,
                    m.name as monitor_name
                FROM accident_records a
                LEFT JOIN monitor m ON a.monitor_id = m.id
                WHERE {where_clause}
                GROUP BY a.accident_type, a.severity_level, a.monitor_id
                ORDER BY count DESC
            """
            
            statistics = db.get_all(stats_query, params)
            
            # 获取总体统计
            total_query = f"""
                SELECT 
                    COUNT(*) as total_accidents,
                    COUNT(DISTINCT monitor_id) as affected_monitors,
                    SUM(CASE WHEN status = 'resolved' THEN 1 ELSE 0 END) as resolved_count,
                    AVG(TIMESTAMPDIFF(MINUTE, accident_time, response_time)) as avg_response_time
                FROM accident_records a
                WHERE {where_clause}
            """
            
            total_stats = db.get_one(total_query, params)
            
            # 获取严重程度分布
            severity_query = f"""
                SELECT 
                    severity_level,
                    COUNT(*) as count
                FROM accident_records a
                WHERE {where_clause}
                GROUP BY severity_level
                ORDER BY 
                    CASE severity_level 
                        WHEN 'critical' THEN 1
                        WHEN 'high' THEN 2
                        WHEN 'medium' THEN 3
                        WHEN 'low' THEN 4
                    END
            """
            
            severity_distribution = db.get_all(severity_query, params)
        
        return success_response({
            'statistics': statistics,
            'total_stats': total_stats,
            'severity_distribution': severity_distribution
        }, '获取事故统计成功')
        
    except Exception as e:
        return error_response(f'获取事故统计失败: {str(e)}')

@api_v1.route('/accident/records', methods=['GET'])
@token_required
def get_accident_records(current_user_id):
    """获取事故记录列表"""
    try:
        page = int(request.args.get('page', 1))
        size = int(request.args.get('page_size', 20))
        monitor_id = request.args.get('monitor_id')
        accident_type = request.args.get('accident_type')
        severity_level = request.args.get('severity_level')
        status = request.args.get('status')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        offset = (page - 1) * size
        
        # 构建查询条件
        conditions = []
        params = []
        
        if monitor_id:
            conditions.append('a.monitor_id = %s')
            params.append(monitor_id)
            
        if accident_type:
            conditions.append('a.accident_type = %s')
            params.append(accident_type)
            
        if severity_level:
            conditions.append('a.severity_level = %s')
            params.append(severity_level)
            
        if status:
            conditions.append('a.status = %s')
            params.append(status)
            
        if start_date:
            conditions.append('a.accident_time >= %s')
            params.append(start_date)
            
        if end_date:
            conditions.append('a.accident_time <= %s')
            params.append(end_date)
        
        where_clause = ' AND '.join(conditions) if conditions else '1=1'
        
        with get_db_connection() as db:
            # 获取记录总数
            count_query = f"SELECT COUNT(*) as total FROM accident_records a WHERE {where_clause}"
            total_result = db.get_one(count_query, params)
            total = total_result['total'] if total_result else 0
            
            # 获取记录列表
            records_query = f"""
                SELECT 
                    a.id as record_id,
                    a.monitor_id,
                    a.accident_type,
                    a.severity_level as severity,
                    a.location,
                    a.description,
                    a.confidence,
                    a.status,
                    a.evidence_image,
                    a.accident_time as create_time,
                    m.name as monitor_name
                FROM accident_records a
                LEFT JOIN monitor m ON a.monitor_id = m.id
                WHERE {where_clause}
                ORDER BY a.accident_time DESC
                LIMIT %s OFFSET %s
            """
            
            raw_records = db.get_all(records_query, params + [size, offset])
            
            # 格式化记录以匹配前端期望格式
            formatted_records = []
            for record in raw_records:
                # 处理location字段
                location = {'x': 320, 'y': 240}  # 默认位置
                if record['location']:
                    try:
                        # 尝试解析JSON格式的location
                        location_data = json.loads(record['location'])
                        if isinstance(location_data, dict):
                            location = {
                                'x': location_data.get('x', 320),
                                'y': location_data.get('y', 240)
                            }
                    except (json.JSONDecodeError, TypeError):
                        # 如果不是JSON格式，保持默认值
                        pass
                
                formatted_record = {
                    'record_id': f"ACC_{record['create_time'].strftime('%Y%m%d')}_{str(record['record_id']).zfill(3)}",
                    'monitor_id': record['monitor_id'],
                    'accident_type': record['accident_type'],
                    'severity': record['severity'],
                    'location': location,
                    'description': record['description'] or '',
                    'confidence': float(record['confidence']) if record['confidence'] else 0.0,
                    'status': record['status'],
                    'evidence_image': record['evidence_image'] or '',
                    'create_time': record['create_time'].strftime('%Y-%m-%dT%H:%M:%S') if record['create_time'] else ''
                }
                formatted_records.append(formatted_record)
        
        return success_response({
            'records': formatted_records,
            'total': total,
            'page': page,
            'page_size': size
        }, '获取事故记录成功')
        
    except Exception as e:
        return error_response(f'获取事故记录失败: {str(e)}')

@api_v1.route('/accident/alert-config', methods=['POST'])
@token_required
def configure_accident_alerts(current_user_id):
    """配置事故预警"""
    try:
        data = request.get_json()
        monitor_id = data.get('monitor_id')
        alert_config = {
            'enable_email_alerts': data.get('enable_email_alerts', True),
            'enable_sms_alerts': data.get('enable_sms_alerts', False),
            'enable_webhook_alerts': data.get('enable_webhook_alerts', False),
            'alert_recipients': data.get('alert_recipients', []),
            'severity_threshold': data.get('severity_threshold', 'medium'),
            'alert_interval': data.get('alert_interval', 300),
            'webhook_url': data.get('webhook_url', '')
        }
        
        with get_db_connection() as db:
            # 检查是否已存在配置
            existing = db.get_one(
                "SELECT id FROM algorithm_configs WHERE monitor_id=%s AND config_type='accident_alert'",
                (monitor_id,)
            )
            
            if existing:
                # 更新配置
                db.execute(
                    "UPDATE algorithm_configs SET config_data=%s, update_time=NOW() WHERE id=%s",
                    (json.dumps(alert_config), existing['id'])
                )
            else:
                # 插入新配置
                db.execute(
                    "INSERT INTO algorithm_configs (monitor_id, config_type, algorithm_name, config_data, create_by) VALUES (%s, %s, %s, %s, %s)",
                    (monitor_id, 'accident_alert', 'alert_system', json.dumps(alert_config), current_user_id)
                )
        
        return success_response(alert_config, '事故预警配置更新成功')
        
    except Exception as e:
        return error_response(f'配置事故预警失败: {str(e)}')