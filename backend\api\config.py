# @Description : 算法参数配置API接口
# @Date : 2025年6月20日

import json
from datetime import datetime
from flask import request, jsonify
from . import api_v1
from .auth import token_required
from utils.response import success_response, error_response
from utils.database import get_db_connection
from services.config_service import ConfigService

# 初始化配置服务
config_service = ConfigService()

@api_v1.route('/config/algorithm', methods=['GET'])
@token_required
def get_algorithm_config(current_user_id):
    """获取算法配置参数"""
    try:
        monitor_id = request.args.get('monitor_id')
        config_type = request.args.get('config_type')
        algorithm_name = request.args.get('algorithm_name')
        
        # 构建查询条件
        conditions = []
        params = []
        
        if monitor_id:
            conditions.append('monitor_id = %s')
            params.append(monitor_id)
        else:
            conditions.append('monitor_id IS NULL')  # 全局配置
            
        if config_type:
            conditions.append('config_type = %s')
            params.append(config_type)
            
        if algorithm_name:
            conditions.append('algorithm_name = %s')
            params.append(algorithm_name)
        
        where_clause = ' AND '.join(conditions) if conditions else '1=1'
        
        with get_db_connection() as db:
            configs_query = f"""
                SELECT 
                    ac.*,
                    m.name as monitor_name
                FROM algorithm_configs ac
                LEFT JOIN monitor m ON ac.monitor_id = m.id
                WHERE {where_clause} AND ac.is_active = 1
                ORDER BY ac.priority DESC, ac.create_time DESC
            """
            
            configs = db.get_all(configs_query, params)
            
            # 解析JSON配置数据
            for config in configs:
                try:
                    config['config_data'] = json.loads(config['config_data'])
                except:
                    config['config_data'] = {}
        
        return success_response(configs, '获取算法配置成功')
        
    except Exception as e:
        return error_response(f'获取算法配置失败: {str(e)}')

@api_v1.route('/config/algorithm', methods=['PUT'])
@token_required
def update_algorithm_config(current_user_id):
    """更新算法配置参数"""
    try:
        data = request.get_json()
        config_id = data.get('config_id')
        monitor_id = data.get('monitor_id')
        config_type = data.get('config_type')
        algorithm_name = data.get('algorithm_name')
        config_data = data.get('config_data', {})
        is_active = data.get('is_active', True)
        priority = data.get('priority', 0)
        
        with get_db_connection() as db:
            if config_id:
                # 更新现有配置
                db.execute(
                    "UPDATE algorithm_configs SET config_data=%s, is_active=%s, priority=%s, update_time=NOW() WHERE id=%s",
                    (json.dumps(config_data), is_active, priority, config_id)
                )
            else:
                # 创建新配置
                if not config_type or not algorithm_name:
                    return error_response('配置类型和算法名称不能为空')
                
                db.execute(
                    "INSERT INTO algorithm_configs (monitor_id, config_type, algorithm_name, config_data, is_active, priority, create_by) VALUES (%s, %s, %s, %s, %s, %s, %s)",
                    (monitor_id, config_type, algorithm_name, json.dumps(config_data), is_active, priority, current_user_id)
                )
                config_id = db.lastrowid
        
        # 应用配置更改
        config_service.apply_config_changes(monitor_id, config_type, algorithm_name, config_data)
        
        return success_response({'config_id': config_id}, '算法配置更新成功')
        
    except Exception as e:
        return error_response(f'更新算法配置失败: {str(e)}')

@api_v1.route('/config/batch-update', methods=['POST'])
@token_required
def batch_update_configs(current_user_id):
    """批量更新配置"""
    try:
        data = request.get_json()
        configs = data.get('configs', [])
        
        if not configs:
            return error_response('配置列表不能为空')
        
        updated_configs = []
        
        with get_db_connection() as db:
            for config in configs:
                config_id = config.get('config_id')
                monitor_id = config.get('monitor_id')
                config_type = config.get('config_type')
                algorithm_name = config.get('algorithm_name')
                config_data = config.get('config_data', {})
                is_active = config.get('is_active', True)
                priority = config.get('priority', 0)
                
                if config_id:
                    # 更新现有配置
                    db.execute(
                        "UPDATE algorithm_configs SET config_data=%s, is_active=%s, priority=%s, update_time=NOW() WHERE id=%s",
                        (json.dumps(config_data), is_active, priority, config_id)
                    )
                else:
                    # 创建新配置
                    if config_type and algorithm_name:
                        db.execute(
                            "INSERT INTO algorithm_configs (monitor_id, config_type, algorithm_name, config_data, is_active, priority, create_by) VALUES (%s, %s, %s, %s, %s, %s, %s)",
                            (monitor_id, config_type, algorithm_name, json.dumps(config_data), is_active, priority, current_user_id)
                        )
                        config_id = db.lastrowid
                
                updated_configs.append({
                    'config_id': config_id,
                    'monitor_id': monitor_id,
                    'config_type': config_type,
                    'algorithm_name': algorithm_name
                })
                
                # 应用配置更改
                config_service.apply_config_changes(monitor_id, config_type, algorithm_name, config_data)
        
        return success_response(updated_configs, '批量配置更新成功')
        
    except Exception as e:
        return error_response(f'批量配置更新失败: {str(e)}')

@api_v1.route('/config/validate', methods=['POST'])
@token_required
def validate_config(current_user_id):
    """验证配置参数"""
    try:
        data = request.get_json()
        config_type = data.get('config_type')
        algorithm_name = data.get('algorithm_name')
        config_data = data.get('config_data', {})
        
        if not config_type or not algorithm_name:
            return error_response('配置类型和算法名称不能为空')
        
        # 验证配置
        validation_result = config_service.validate_config(config_type, algorithm_name, config_data)
        
        return success_response(validation_result, '配置验证完成')
        
    except Exception as e:
        return error_response(f'配置验证失败: {str(e)}')

@api_v1.route('/config/test', methods=['POST'])
@token_required
def test_config(current_user_id):
    """测试配置参数"""
    try:
        data = request.get_json()
        monitor_id = data.get('monitor_id')
        config_type = data.get('config_type')
        algorithm_name = data.get('algorithm_name')
        config_data = data.get('config_data', {})
        test_duration = data.get('test_duration', 60)  # 测试时长(秒)
        
        if not monitor_id or not config_type or not algorithm_name:
            return error_response('监控点ID、配置类型和算法名称不能为空')
        
        # 启动配置测试
        test_result = config_service.test_config(
            monitor_id, config_type, algorithm_name, config_data, test_duration
        )
        
        return success_response(test_result, '配置测试启动成功')
        
    except Exception as e:
        return error_response(f'配置测试失败: {str(e)}')

@api_v1.route('/config/export', methods=['GET'])
@token_required
def export_config(current_user_id):
    """导出配置"""
    try:
        monitor_id = request.args.get('monitor_id')
        config_type = request.args.get('config_type')
        
        # 构建查询条件
        conditions = ['is_active = 1']
        params = []
        
        if monitor_id:
            conditions.append('monitor_id = %s')
            params.append(monitor_id)
            
        if config_type:
            conditions.append('config_type = %s')
            params.append(config_type)
        
        where_clause = ' AND '.join(conditions)
        
        with get_db_connection() as db:
            configs_query = f"""
                SELECT 
                    ac.*,
                    m.name as monitor_name
                FROM algorithm_configs ac
                LEFT JOIN monitor m ON ac.monitor_id = m.id
                WHERE {where_clause}
                ORDER BY ac.config_type, ac.algorithm_name, ac.priority DESC
            """
            
            configs = db.get_all(configs_query, params)
            
            # 解析JSON配置数据
            for config in configs:
                try:
                    config['config_data'] = json.loads(config['config_data'])
                except:
                    config['config_data'] = {}
        
        # 生成导出文件
        export_data = {
            'export_time': datetime.now().isoformat(),
            'export_by': current_user_id,
            'configs': configs
        }
        
        return success_response(export_data, '配置导出成功')
        
    except Exception as e:
        return error_response(f'配置导出失败: {str(e)}')

@api_v1.route('/config/import', methods=['POST'])
@token_required
def import_config(current_user_id):
    """导入配置"""
    try:
        data = request.get_json()
        configs = data.get('configs', [])
        overwrite = data.get('overwrite', False)
        
        if not configs:
            return error_response('配置数据不能为空')
        
        imported_count = 0
        skipped_count = 0
        
        with get_db_connection() as db:
            for config in configs:
                monitor_id = config.get('monitor_id')
                config_type = config.get('config_type')
                algorithm_name = config.get('algorithm_name')
                config_data = config.get('config_data', {})
                is_active = config.get('is_active', True)
                priority = config.get('priority', 0)
                
                if not config_type or not algorithm_name:
                    skipped_count += 1
                    continue
                
                # 检查是否已存在
                existing = db.get_one(
                    "SELECT id FROM algorithm_configs WHERE monitor_id=%s AND config_type=%s AND algorithm_name=%s",
                    (monitor_id, config_type, algorithm_name)
                )
                
                if existing and not overwrite:
                    skipped_count += 1
                    continue
                
                if existing and overwrite:
                    # 更新现有配置
                    db.execute(
                        "UPDATE algorithm_configs SET config_data=%s, is_active=%s, priority=%s, update_time=NOW() WHERE id=%s",
                        (json.dumps(config_data), is_active, priority, existing['id'])
                    )
                else:
                    # 插入新配置
                    db.execute(
                        "INSERT INTO algorithm_configs (monitor_id, config_type, algorithm_name, config_data, is_active, priority, create_by) VALUES (%s, %s, %s, %s, %s, %s, %s)",
                        (monitor_id, config_type, algorithm_name, json.dumps(config_data), is_active, priority, current_user_id)
                    )
                
                imported_count += 1
        
        return success_response({
            'imported_count': imported_count,
            'skipped_count': skipped_count
        }, '配置导入完成')
        
    except Exception as e:
        return error_response(f'配置导入失败: {str(e)}')