# @Description : 违规检测API接口
# @Date : 2025年6月20日

import json
from datetime import datetime, timedelta
from flask import request, jsonify
from . import api_v1
from .auth import token_required
from utils.response import success_response, error_response
from utils.database import get_db_connection
from services.violation_service import ViolationService

# 初始化违规检测服务
violation_service = ViolationService()

@api_v1.route('/violation/configure', methods=['POST'])
@token_required
def configure_violation_detection(current_user_id):
    """配置违规检测参数"""
    try:
        data = request.get_json()
        monitor_id = data.get('monitor_id')
        config = {
            'speed_limit': data.get('speed_limit', 120),
            'enable_speeding': data.get('enable_speeding', True),
            'enable_wrong_way': data.get('enable_wrong_way', True),
            'enable_lane_change': data.get('enable_lane_change', True),
            'enable_illegal_parking': data.get('enable_illegal_parking', True),
            'speed_threshold_ratio': data.get('speed_threshold_ratio', 1.2),
            'lane_change_threshold': data.get('lane_change_threshold', 3),
            'parking_time_threshold': data.get('parking_time_threshold', 300),
            'confidence_threshold': data.get('confidence_threshold', 0.8)
        }
        
        with get_db_connection() as db:
            # 检查是否已存在配置
            existing = db.get_one(
                "SELECT id FROM algorithm_configs WHERE monitor_id=%s AND config_type='violation'",
                (monitor_id,)
            )
            
            if existing:
                # 更新配置
                db.execute(
                    "UPDATE algorithm_configs SET config_data=%s, update_time=NOW() WHERE id=%s",
                    (json.dumps(config), existing['id'])
                )
            else:
                # 插入新配置
                db.execute(
                    "INSERT INTO algorithm_configs (monitor_id, config_type, algorithm_name, config_data, create_by) VALUES (%s, %s, %s, %s, %s)",
                    (monitor_id, 'violation', 'violation_detector', json.dumps(config), current_user_id)
                )
        
        # 更新违规检测服务配置
        violation_service.update_config(monitor_id, config)
        
        return success_response(config, '违规检测配置更新成功')
        
    except Exception as e:
        return error_response(f'配置违规检测失败: {str(e)}')

@api_v1.route('/violation/statistics', methods=['GET'])
@token_required
def get_violation_statistics(current_user_id):
    """获取违规统计数据"""
    try:
        monitor_id = request.args.get('monitor_id')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        violation_type = request.args.get('violation_type')
        
        # 构建查询条件
        conditions = []
        params = []
        
        if monitor_id:
            conditions.append('v.monitor_id = %s')
            params.append(monitor_id)
            
        if start_date:
            conditions.append('v.violation_time >= %s')
            params.append(start_date)
            
        if end_date:
            conditions.append('v.violation_time <= %s')
            params.append(end_date)
            
        if violation_type:
            conditions.append('v.violation_type = %s')
            params.append(violation_type)
        
        where_clause = ' AND '.join(conditions) if conditions else '1=1'
        
        with get_db_connection() as db:
            # 获取违规统计
            stats_query = f"""
                SELECT 
                    v.violation_type,
                    COUNT(*) as count,
                    AVG(v.speed) as avg_speed,
                    MAX(v.speed) as max_speed,
                    m.name as monitor_name
                FROM violation_records v
                LEFT JOIN monitor m ON v.monitor_id = m.id
                WHERE {where_clause}
                GROUP BY v.violation_type, v.monitor_id
                ORDER BY count DESC
            """
            
            statistics = db.get_all(stats_query, params)
            
            # 获取总体统计
            total_query = f"""
                SELECT 
                    COUNT(*) as total_violations,
                    COUNT(DISTINCT monitor_id) as affected_monitors,
                    AVG(speed) as avg_speed
                FROM violation_records v
                WHERE {where_clause}
            """
            
            total_stats = db.get_one(total_query, params)
            
            # 获取时间分布
            time_query = f"""
                SELECT 
                    DATE(violation_time) as date,
                    HOUR(violation_time) as hour,
                    COUNT(*) as count
                FROM violation_records v
                WHERE {where_clause}
                GROUP BY DATE(violation_time), HOUR(violation_time)
                ORDER BY date DESC, hour DESC
                LIMIT 24
            """
            
            time_distribution = db.get_all(time_query, params)
        
        return success_response({
            'statistics': statistics,
            'total_stats': total_stats,
            'time_distribution': time_distribution
        }, '获取违规统计成功')
        
    except Exception as e:
        return error_response(f'获取违规统计失败: {str(e)}')

@api_v1.route('/violation/records', methods=['GET'])
@token_required
def get_violation_records(current_user_id):
    """获取违规记录列表"""
    try:
        page = int(request.args.get('page', 1))
        size = int(request.args.get('size', 20))
        monitor_id = request.args.get('monitor_id')
        violation_type = request.args.get('violation_type')
        status = request.args.get('status')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        offset = (page - 1) * size
        
        # 构建查询条件
        conditions = []
        params = []
        
        if monitor_id:
            conditions.append('v.monitor_id = %s')
            params.append(monitor_id)
            
        if violation_type:
            conditions.append('v.violation_type = %s')
            params.append(violation_type)
            
        if status:
            conditions.append('v.status = %s')
            params.append(status)
            
        if start_date:
            conditions.append('v.violation_time >= %s')
            params.append(start_date)
            
        if end_date:
            conditions.append('v.violation_time <= %s')
            params.append(end_date)
        
        where_clause = ' AND '.join(conditions) if conditions else '1=1'
        
        with get_db_connection() as db:
            # 获取记录总数
            count_query = f"SELECT COUNT(*) as total FROM violation_records v WHERE {where_clause}"
            total_result = db.get_one(count_query, params)
            total = total_result['total'] if total_result else 0
            
            # 获取记录列表
            records_query = f"""
                SELECT 
                    v.*,
                    m.name as monitor_name,
                    m.location as monitor_location
                FROM violation_records v
                LEFT JOIN monitor m ON v.monitor_id = m.id
                WHERE {where_clause}
                ORDER BY v.violation_time DESC
                LIMIT %s OFFSET %s
            """
            
            records = db.get_all(records_query, params + [size, offset])
        
        return success_response({
            'records': records,
            'pagination': {
                'page': page,
                'size': size,
                'total': total,
                'pages': (total + size - 1) // size
            }
        }, '获取违规记录成功')
        
    except Exception as e:
        return error_response(f'获取违规记录失败: {str(e)}')

@api_v1.route('/violation/start-detection', methods=['POST'])
@token_required
def start_violation_detection(current_user_id):
    """启动违规检测"""
    try:
        data = request.get_json()
        monitor_id = data.get('monitor_id')
        
        if not monitor_id:
            return error_response('监控点ID不能为空')
        
        # 启动违规检测
        result = violation_service.start_detection(monitor_id)
        
        if result['success']:
            return success_response(result['data'], '违规检测启动成功')
        else:
            return error_response(result['message'])
            
    except Exception as e:
        return error_response(f'启动违规检测失败: {str(e)}')

@api_v1.route('/violation/stop-detection', methods=['POST'])
@token_required
def stop_violation_detection(current_user_id):
    """停止违规检测"""
    try:
        data = request.get_json()
        monitor_id = data.get('monitor_id')
        
        if not monitor_id:
            return error_response('监控点ID不能为空')
        
        # 停止违规检测
        result = violation_service.stop_detection(monitor_id)
        
        if result['success']:
            return success_response(result['data'], '违规检测停止成功')
        else:
            return error_response(result['message'])
            
    except Exception as e:
        return error_response(f'停止违规检测失败: {str(e)}')

@api_v1.route('/violation/record/<int:record_id>/update-status', methods=['PUT'])
@token_required
def update_violation_status(current_user_id, record_id):
    """更新违规记录状态"""
    try:
        data = request.get_json()
        status = data.get('status')
        description = data.get('description', '')
        
        if status not in ['pending', 'confirmed', 'dismissed']:
            return error_response('无效的状态值')
        
        with get_db_connection() as db:
            db.execute(
                "UPDATE violation_records SET status=%s, description=%s WHERE id=%s",
                (status, description, record_id)
            )
        
        return success_response(None, '违规记录状态更新成功')
        
    except Exception as e:
        return error_response(f'更新违规记录状态失败: {str(e)}')