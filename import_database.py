# -*- coding: utf-8 -*-
# @Description : 数据库导入工具
# @Date : 2025年6月20日

import os
import sys
import subprocess
import getpass
import pymysql

def check_mysql_service():
    """检查MySQL服务状态"""
    try:
        if os.name == 'nt':  # Windows
            result = subprocess.run(['sc', 'query', 'mysql'], capture_output=True, text=True)
            if 'RUNNING' in result.stdout:
                return True
            else:
                print("MySQL服务未启动，尝试启动...")
                subprocess.run(['net', 'start', 'mysql'], check=True)
                return True
        else:  # Linux/Mac
            result = subprocess.run(['systemctl', 'is-active', 'mysql'], capture_output=True, text=True)
            return result.returncode == 0
    except Exception as e:
        print(f"检查MySQL服务失败: {e}")
        return False

def test_mysql_connection(host, port, user, password):
    """测试MySQL连接"""
    try:
        connection = pymysql.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            charset='utf8mb4'
        )
        connection.close()
        return True
    except Exception as e:
        print(f"MySQL连接失败: {e}")
        return False

def import_database(host, port, user, password, sql_file):
    """导入数据库"""
    try:
        # 使用mysql命令行工具导入
        cmd = f'mysql -h {host} -P {port} -u {user} -p{password} < {sql_file}'
        
        if os.name == 'nt':  # Windows
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        else:  # Linux/Mac
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            return True, "导入成功"
        else:
            return False, result.stderr
    except Exception as e:
        return False, str(e)

def verify_database(host, port, user, password):
    """验证数据库导入结果"""
    try:
        connection = pymysql.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database='yolo',
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
        
        with connection.cursor() as cursor:
            # 检查表数量
            cursor.execute("SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema='yolo'")
            table_count = cursor.fetchone()['count']
            
            # 检查用户数量
            cursor.execute("SELECT COUNT(*) as count FROM user")
            user_count = cursor.fetchone()['count']
            
            # 检查监控点数量
            cursor.execute("SELECT COUNT(*) as count FROM monitor")
            monitor_count = cursor.fetchone()['count']
            
            # 检查警报数量
            cursor.execute("SELECT COUNT(*) as count FROM alarm")
            alarm_count = cursor.fetchone()['count']
            
            # 检查配置数量
            cursor.execute("SELECT COUNT(*) as count FROM system_config")
            config_count = cursor.fetchone()['count']
        
        connection.close()
        
        return {
            'tables': table_count,
            'users': user_count,
            'monitors': monitor_count,
            'alarms': alarm_count,
            'configs': config_count
        }
    except Exception as e:
        print(f"验证数据库失败: {e}")
        return None

def main():
    """主函数"""
    print("=" * 80)
    print("基于Yolov8与ByteTrack的高速公路智慧监控平台 - 数据库导入工具")
    print("=" * 80)
    
    # 检查SQL文件是否存在
    sql_file = 'yolo_complete.sql'
    if not os.path.exists(sql_file):
        print(f"❌ SQL文件不存在: {sql_file}")
        return
    
    print(f"✅ 找到SQL文件: {sql_file}")
    
    # 检查MySQL服务
    print("\n🔧 检查MySQL服务...")
    if not check_mysql_service():
        print("❌ MySQL服务未启动，请手动启动MySQL服务")
        return
    
    print("✅ MySQL服务正在运行")
    
    # 获取连接信息
    print("\n📋 请输入MySQL连接信息:")
    host = input("主机地址 (默认: 127.0.0.1): ").strip() or '127.0.0.1'
    port = int(input("端口 (默认: 3306): ").strip() or '3306')
    user = input("用户名 (默认: root): ").strip() or 'root'
    password = getpass.getpass("密码: ")
    
    if not password:
        print("❌ 密码不能为空")
        return
    
    # 测试连接
    print("\n🔗 测试MySQL连接...")
    if not test_mysql_connection(host, port, user, password):
        print("❌ MySQL连接失败，请检查连接信息")
        return
    
    print("✅ MySQL连接成功")
    
    # 确认导入
    print(f"\n⚠️  警告: 这将删除现有的yolo数据库并重新创建")
    confirm = input("是否继续? (y/N): ").strip().lower()
    if confirm != 'y':
        print("操作已取消")
        return
    
    # 导入数据库
    print("\n🏗️  正在导入数据库...")
    success, message = import_database(host, port, user, password, sql_file)
    
    if not success:
        print(f"❌ 数据库导入失败: {message}")
        return
    
    print("✅ 数据库导入成功")
    
    # 验证导入结果
    print("\n🧪 验证数据库...")
    stats = verify_database(host, port, user, password)
    
    if stats:
        print("✅ 数据库验证成功")
        print(f"📊 数据统计:")
        print(f"  📋 数据表: {stats['tables']} 个")
        print(f"  👥 用户: {stats['users']} 个")
        print(f"  📹 监控点: {stats['monitors']} 个")
        print(f"  🚨 警报记录: {stats['alarms']} 条")
        print(f"  ⚙️  系统配置: {stats['configs']} 项")
    else:
        print("❌ 数据库验证失败")
        return
    
    print("\n" + "=" * 80)
    print("🎉 数据库导入完成！")
    print("=" * 80)
    
    print("\n📋 系统信息:")
    print("  🏷️  系统名称: 基于Yolov8与ByteTrack的高速公路智慧监控平台")
    print("  📍 监控范围: 杭州至千岛湖高速公路 (K0+000 - K95+000)")
    print("  🎯 监控点数: 15个 (覆盖收费站、服务区、隧道、桥梁等)")
    print("  🚨 警报记录: 10条 (包含不同时间段的真实数据)")
    
    print("\n🔑 默认登录账号:")
    print("  👑 管理员: admin / 123456")
    print("  👨‍💼 操作员: operator / operator")
    print("  👁️  观察员: viewer / hello")
    
    print("\n🚀 下一步操作:")
    print("  1. 启动系统: python start_server.py")
    print("  2. 访问地址: http://127.0.0.1:5500")
    print("  3. 使用admin/123456登录系统")
    
    print("\n✨ 系统功能:")
    print("  📊 实时监控: 多路视频流监控和车辆检测")
    print("  🎯 智能检测: YOLO目标检测和多目标追踪")
    print("  📈 数据分析: 交通流量统计和趋势分析")
    print("  🚨 警报管理: 自动警报生成和处理")
    print("  ⚙️  系统管理: 用户管理和系统配置")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n操作已取消")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
    finally:
        input("\n按回车键退出...")
