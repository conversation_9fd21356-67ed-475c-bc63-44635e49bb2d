# @Description : 违规检测服务
# @Date : 2025年6月20日

import json
import threading
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
import cv2
import numpy as np
from utils.database import get_db_connection
from classes.violation_detector import ViolationDetector

class ViolationService:
    """违规检测服务类"""
    
    def __init__(self):
        self.violation_detectors = {}  # monitor_id -> ViolationDetector
        self.detection_threads = {}    # monitor_id -> thread
        self.detection_status = {}     # monitor_id -> status
        self.configs = {}              # monitor_id -> config
        
    def update_config(self, monitor_id: int, config: Dict[str, Any]):
        """更新违规检测配置"""
        try:
            self.configs[monitor_id] = config
            
            # 如果检测器已存在，更新其配置
            if monitor_id in self.violation_detectors:
                detector = self.violation_detectors[monitor_id]
                detector.speed_limit = config.get('speed_limit', 120)
                detector.enable_speeding = config.get('enable_speeding', True)
                detector.enable_wrong_way = config.get('enable_wrong_way', True)
                detector.enable_lane_change = config.get('enable_lane_change', True)
                detector.enable_illegal_parking = config.get('enable_illegal_parking', True)
                detector.speed_threshold_ratio = config.get('speed_threshold_ratio', 1.2)
                detector.lane_change_threshold = config.get('lane_change_threshold', 3)
                detector.parking_time_threshold = config.get('parking_time_threshold', 300)
                detector.confidence_threshold = config.get('confidence_threshold', 0.8)
                
            return True
        except Exception as e:
            print(f"更新违规检测配置失败: {str(e)}")
            return False
    
    def start_detection(self, monitor_id: int) -> Dict[str, Any]:
        """启动违规检测"""
        try:
            if monitor_id in self.detection_status and self.detection_status[monitor_id] == 'running':
                return {
                    'success': False,
                    'message': '违规检测已在运行中'
                }
            
            # 获取监控点信息
            with get_db_connection() as db:
                monitor = db.get_one(
                    "SELECT * FROM monitor WHERE id = %s",
                    (monitor_id,)
                )
                
                if not monitor:
                    return {
                        'success': False,
                        'message': '监控点不存在'
                    }
            
            # 创建违规检测器
            config = self.configs.get(monitor_id, {})
            detector = ViolationDetector(
                speed_limit=config.get('speed_limit', 120),
                enable_speeding=config.get('enable_speeding', True),
                enable_wrong_way=config.get('enable_wrong_way', True),
                enable_lane_change=config.get('enable_lane_change', True),
                enable_illegal_parking=config.get('enable_illegal_parking', True)
            )
            
            self.violation_detectors[monitor_id] = detector
            self.detection_status[monitor_id] = 'running'
            
            # 启动检测线程
            thread = threading.Thread(
                target=self._detection_worker,
                args=(monitor_id, monitor['rtsp_url'])
            )
            thread.daemon = True
            thread.start()
            
            self.detection_threads[monitor_id] = thread
            
            return {
                'success': True,
                'data': {
                    'monitor_id': monitor_id,
                    'status': 'running',
                    'start_time': datetime.now().isoformat()
                },
                'message': '违规检测启动成功'
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'启动违规检测失败: {str(e)}'
            }
    
    def stop_detection(self, monitor_id: int) -> Dict[str, Any]:
        """停止违规检测"""
        try:
            if monitor_id not in self.detection_status or self.detection_status[monitor_id] != 'running':
                return {
                    'success': False,
                    'message': '违规检测未在运行'
                }
            
            # 停止检测
            self.detection_status[monitor_id] = 'stopped'
            
            # 清理资源
            if monitor_id in self.violation_detectors:
                del self.violation_detectors[monitor_id]
            
            if monitor_id in self.detection_threads:
                del self.detection_threads[monitor_id]
            
            return {
                'success': True,
                'data': {
                    'monitor_id': monitor_id,
                    'status': 'stopped',
                    'stop_time': datetime.now().isoformat()
                },
                'message': '违规检测停止成功'
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'停止违规检测失败: {str(e)}'
            }
    
    def _detection_worker(self, monitor_id: int, rtsp_url: str):
        """违规检测工作线程"""
        try:
            cap = cv2.VideoCapture(rtsp_url)
            detector = self.violation_detectors[monitor_id]
            
            while self.detection_status.get(monitor_id) == 'running':
                ret, frame = cap.read()
                if not ret:
                    time.sleep(0.1)
                    continue
                
                # 执行违规检测
                violations = detector.detect_violations(frame)
                
                # 保存违规记录
                if violations:
                    self._save_violations(monitor_id, violations)
                
                time.sleep(0.033)  # 约30fps
                
        except Exception as e:
            print(f"违规检测工作线程错误: {str(e)}")
        finally:
            if 'cap' in locals():
                cap.release()
            self.detection_status[monitor_id] = 'stopped'
    
    def _save_violations(self, monitor_id: int, violations: List[Dict]):
        """保存违规记录到数据库"""
        try:
            with get_db_connection() as db:
                for violation in violations:
                    db.execute(
                        """INSERT INTO violation_records 
                           (monitor_id, violation_type, vehicle_id, speed, location_x, location_y, 
                            confidence, image_path, video_path, violation_time, status) 
                           VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)""",
                        (
                            monitor_id,
                            violation['type'],
                            violation.get('vehicle_id', ''),
                            violation.get('speed', 0),
                            violation.get('location_x', 0),
                            violation.get('location_y', 0),
                            violation.get('confidence', 0),
                            violation.get('image_path', ''),
                            violation.get('video_path', ''),
                            datetime.now(),
                            'pending'
                        )
                    )
        except Exception as e:
            print(f"保存违规记录失败: {str(e)}")
    
    def get_detection_status(self, monitor_id: int = None) -> Dict[str, Any]:
        """获取检测状态"""
        if monitor_id:
            return {
                'monitor_id': monitor_id,
                'status': self.detection_status.get(monitor_id, 'stopped'),
                'config': self.configs.get(monitor_id, {})
            }
        else:
            return {
                'all_status': self.detection_status,
                'all_configs': self.configs
            }