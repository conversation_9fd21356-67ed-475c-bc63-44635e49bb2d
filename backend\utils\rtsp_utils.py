# -*- coding: utf-8 -*-
# @Description : RTSP工具类
# @Date : 2025年6月20日

import cv2
import time
import threading
from typing import Dict, List, Any, Optional, Tuple
from urllib.parse import urlparse
import socket

class RTSPUtils:
    """RTSP工具类"""
    
    def __init__(self):
        self.connection_timeout = 10  # 连接超时时间（秒）
        self.read_timeout = 5  # 读取超时时间（秒）
    
    def test_connection(self, rtsp_url: str) -> bool:
        """
        测试RTSP连接
        
        Args:
            rtsp_url: RTSP地址
        
        Returns:
            bool: 连接是否成功
        """
        try:
            cap = cv2.VideoCapture(rtsp_url)
            
            # 设置超时时间
            cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, self.connection_timeout * 1000)
            cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, self.read_timeout * 1000)
            
            if cap.isOpened():
                # 尝试读取一帧
                ret, frame = cap.read()
                cap.release()
                return ret and frame is not None
            else:
                cap.release()
                return False
        except Exception as e:
            print(f"RTSP连接测试失败: {str(e)}")
            return False
    
    def get_stream_info(self, rtsp_url: str) -> Dict[str, Any]:
        """
        获取RTSP流信息
        
        Args:
            rtsp_url: RTSP地址
        
        Returns:
            Dict[str, Any]: 流信息
        """
        try:
            cap = cv2.VideoCapture(rtsp_url)
            
            if not cap.isOpened():
                return {
                    'status': 'failed',
                    'error': '无法连接到RTSP流'
                }
            
            # 获取流信息
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            # 尝试读取一帧来验证流
            ret, frame = cap.read()
            cap.release()
            
            if not ret or frame is None:
                return {
                    'status': 'failed',
                    'error': '无法读取视频帧'
                }
            
            return {
                'status': 'success',
                'width': width,
                'height': height,
                'fps': fps,
                'frame_count': frame_count,
                'resolution': f"{width}x{height}",
                'has_video': True,
                'codec': self._get_codec_info(cap)
            }
        except Exception as e:
            return {
                'status': 'failed',
                'error': str(e)
            }
    
    def _get_codec_info(self, cap: cv2.VideoCapture) -> str:
        """获取编解码器信息"""
        try:
            fourcc = int(cap.get(cv2.CAP_PROP_FOURCC))
            codec = "".join([chr((fourcc >> 8 * i) & 0xFF) for i in range(4)])
            return codec
        except:
            return "unknown"
    
    def validate_rtsp_url(self, rtsp_url: str) -> Tuple[bool, str]:
        """
        验证RTSP URL格式
        
        Args:
            rtsp_url: RTSP地址
        
        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        try:
            if not rtsp_url:
                return False, "RTSP URL不能为空"
            
            # 解析URL
            parsed = urlparse(rtsp_url)
            
            # 检查协议
            if parsed.scheme.lower() not in ['rtsp', 'rtmp', 'http', 'https']:
                return False, f"不支持的协议: {parsed.scheme}"
            
            # 检查主机名
            if not parsed.hostname:
                return False, "缺少主机名"
            
            # 检查端口
            if parsed.port:
                if not (1 <= parsed.port <= 65535):
                    return False, f"无效的端口号: {parsed.port}"
            
            return True, "RTSP URL格式有效"
        except Exception as e:
            return False, f"RTSP URL格式错误: {str(e)}"
    
    def ping_host(self, hostname: str, port: int = 554, timeout: int = 5) -> bool:
        """
        Ping主机端口
        
        Args:
            hostname: 主机名或IP
            port: 端口号
            timeout: 超时时间
        
        Returns:
            bool: 是否可达
        """
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)
            result = sock.connect_ex((hostname, port))
            sock.close()
            return result == 0
        except Exception:
            return False
    
    def get_rtsp_formats(self) -> List[Dict[str, str]]:
        """
        获取支持的RTSP格式
        
        Returns:
            List[Dict[str, str]]: RTSP格式列表
        """
        return [
            {
                'name': 'standard',
                'description': '标准RTSP格式',
                'example': 'rtsp://username:password@ip:port/stream'
            },
            {
                'name': 'hikvision',
                'description': '海康威视格式',
                'example': 'rtsp://username:password@ip:port/Streaming/Channels/101'
            },
            {
                'name': 'dahua',
                'description': '大华格式',
                'example': 'rtsp://username:password@ip:port/cam/realmonitor?channel=1&subtype=0'
            },
            {
                'name': 'uniview',
                'description': '宇视格式',
                'example': 'rtsp://username:password@ip:port/video1s1'
            },
            {
                'name': 'axis',
                'description': 'Axis格式',
                'example': 'rtsp://username:password@ip:port/axis-media/media.amp'
            }
        ]
    
    def generate_rtsp_url(self, host: str, port: int = 554, username: str = '', 
                         password: str = '', path: str = '/stream', 
                         format_type: str = 'standard') -> str:
        """
        生成RTSP URL
        
        Args:
            host: 主机地址
            port: 端口号
            username: 用户名
            password: 密码
            path: 路径
            format_type: 格式类型
        
        Returns:
            str: 生成的RTSP URL
        """
        try:
            # 构建认证部分
            auth_part = ""
            if username and password:
                auth_part = f"{username}:{password}@"
            elif username:
                auth_part = f"{username}@"
            
            # 根据格式类型调整路径
            if format_type == 'hikvision':
                path = '/Streaming/Channels/101'
            elif format_type == 'dahua':
                path = '/cam/realmonitor?channel=1&subtype=0'
            elif format_type == 'uniview':
                path = '/video1s1'
            elif format_type == 'axis':
                path = '/axis-media/media.amp'
            
            # 构建完整URL
            rtsp_url = f"rtsp://{auth_part}{host}:{port}{path}"
            
            return rtsp_url
        except Exception as e:
            return f"生成RTSP URL失败: {str(e)}"
    
    def batch_test_connections(self, rtsp_urls: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        批量测试RTSP连接
        
        Args:
            rtsp_urls: RTSP地址列表
        
        Returns:
            Dict[str, Dict[str, Any]]: 测试结果
        """
        results = {}
        threads = []
        
        def test_single_connection(url: str):
            try:
                start_time = time.time()
                is_connected = self.test_connection(url)
                response_time = time.time() - start_time
                
                if is_connected:
                    stream_info = self.get_stream_info(url)
                    results[url] = {
                        'status': 'connected',
                        'response_time': response_time,
                        'stream_info': stream_info
                    }
                else:
                    results[url] = {
                        'status': 'failed',
                        'response_time': response_time,
                        'error': '连接失败'
                    }
            except Exception as e:
                results[url] = {
                    'status': 'error',
                    'error': str(e)
                }
        
        # 创建线程进行并发测试
        for url in rtsp_urls:
            thread = threading.Thread(target=test_single_connection, args=(url,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join(timeout=self.connection_timeout + 5)
        
        return results
    
    def monitor_stream_health(self, rtsp_url: str, duration: int = 60) -> Dict[str, Any]:
        """
        监控流健康状态
        
        Args:
            rtsp_url: RTSP地址
            duration: 监控时长（秒）
        
        Returns:
            Dict[str, Any]: 健康状态报告
        """
        try:
            cap = cv2.VideoCapture(rtsp_url)
            
            if not cap.isOpened():
                return {
                    'status': 'failed',
                    'error': '无法连接到RTSP流'
                }
            
            start_time = time.time()
            frame_count = 0
            error_count = 0
            total_frames = 0
            
            while time.time() - start_time < duration:
                ret, frame = cap.read()
                total_frames += 1
                
                if ret and frame is not None:
                    frame_count += 1
                else:
                    error_count += 1
                
                time.sleep(0.1)  # 避免过于频繁的读取
            
            cap.release()
            
            # 计算统计信息
            success_rate = (frame_count / total_frames) * 100 if total_frames > 0 else 0
            avg_fps = frame_count / duration if duration > 0 else 0
            
            return {
                'status': 'completed',
                'duration': duration,
                'total_frames': total_frames,
                'successful_frames': frame_count,
                'error_frames': error_count,
                'success_rate': success_rate,
                'average_fps': avg_fps,
                'health_status': 'good' if success_rate > 90 else 'poor' if success_rate > 50 else 'bad'
            }
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def extract_frame(self, rtsp_url: str, frame_number: int = 0) -> Optional[bytes]:
        """
        从RTSP流中提取指定帧
        
        Args:
            rtsp_url: RTSP地址
            frame_number: 帧号（0表示第一帧）
        
        Returns:
            Optional[bytes]: 帧数据（JPEG格式）
        """
        try:
            cap = cv2.VideoCapture(rtsp_url)
            
            if not cap.isOpened():
                return None
            
            # 跳转到指定帧
            if frame_number > 0:
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            
            ret, frame = cap.read()
            cap.release()
            
            if ret and frame is not None:
                # 编码为JPEG
                _, buffer = cv2.imencode('.jpg', frame)
                return buffer.tobytes()
            else:
                return None
        except Exception as e:
            print(f"提取帧失败: {str(e)}")
            return None
    
    def get_stream_thumbnail(self, rtsp_url: str, size: Tuple[int, int] = (150, 150)) -> Optional[bytes]:
        """
        获取流缩略图
        
        Args:
            rtsp_url: RTSP地址
            size: 缩略图尺寸
        
        Returns:
            Optional[bytes]: 缩略图数据（JPEG格式）
        """
        try:
            frame_data = self.extract_frame(rtsp_url, 0)
            
            if frame_data:
                import numpy as np
                from PIL import Image
                import io
                
                # 解码图像
                nparr = np.frombuffer(frame_data, np.uint8)
                img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
                
                # 转换为PIL图像
                img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                pil_img = Image.fromarray(img_rgb)
                
                # 创建缩略图
                pil_img.thumbnail(size, Image.Resampling.LANCZOS)
                
                # 保存为JPEG
                buffer = io.BytesIO()
                pil_img.save(buffer, format='JPEG', quality=85)
                
                return buffer.getvalue()
            else:
                return None
        except Exception as e:
            print(f"生成缩略图失败: {str(e)}")
            return None
    
    def detect_stream_changes(self, rtsp_url: str, check_interval: int = 5, 
                            sensitivity: float = 0.1) -> Dict[str, Any]:
        """
        检测流变化
        
        Args:
            rtsp_url: RTSP地址
            check_interval: 检查间隔（秒）
            sensitivity: 变化敏感度（0-1）
        
        Returns:
            Dict[str, Any]: 变化检测结果
        """
        try:
            cap = cv2.VideoCapture(rtsp_url)
            
            if not cap.isOpened():
                return {
                    'status': 'failed',
                    'error': '无法连接到RTSP流'
                }
            
            # 读取第一帧作为参考
            ret, prev_frame = cap.read()
            if not ret or prev_frame is None:
                cap.release()
                return {
                    'status': 'failed',
                    'error': '无法读取参考帧'
                }
            
            # 转换为灰度图
            prev_gray = cv2.cvtColor(prev_frame, cv2.COLOR_BGR2GRAY)
            
            changes_detected = []
            start_time = time.time()
            
            while time.time() - start_time < 60:  # 监控1分钟
                time.sleep(check_interval)
                
                ret, curr_frame = cap.read()
                if not ret or curr_frame is None:
                    continue
                
                # 转换为灰度图
                curr_gray = cv2.cvtColor(curr_frame, cv2.COLOR_BGR2GRAY)
                
                # 计算帧差
                diff = cv2.absdiff(prev_gray, curr_gray)
                
                # 计算变化程度
                change_ratio = np.sum(diff > 30) / diff.size
                
                if change_ratio > sensitivity:
                    changes_detected.append({
                        'timestamp': time.time(),
                        'change_ratio': change_ratio
                    })
                
                prev_gray = curr_gray
            
            cap.release()
            
            return {
                'status': 'completed',
                'changes_detected': len(changes_detected),
                'change_events': changes_detected,
                'monitoring_duration': 60
            }
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
