# -*- coding: utf-8 -*-
# @Description : 系统管理相关API接口
# @Date : 2025年6月20日

import os
import psutil
import platform
from datetime import datetime
from flask import request, jsonify
from . import api_v1
from .auth import token_required
from utils.response import success_response, error_response
from utils.database import get_db_connection
from services.system_service import SystemService

# 初始化系统服务
system_service = SystemService()

@api_v1.route('/system/info', methods=['GET'])
@token_required
def get_system_info(current_user_id):
    """获取系统信息"""
    try:
        # 系统基本信息
        system_info = {
            'platform': platform.platform(),
            'system': platform.system(),
            'release': platform.release(),
            'version': platform.version(),
            'machine': platform.machine(),
            'processor': platform.processor(),
            'python_version': platform.python_version(),
            'hostname': platform.node()
        }
        
        # CPU信息
        cpu_info = {
            'cpu_count': psutil.cpu_count(),
            'cpu_percent': psutil.cpu_percent(interval=1),
            'cpu_freq': psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None
        }
        
        # 内存信息
        memory = psutil.virtual_memory()
        memory_info = {
            'total': memory.total,
            'available': memory.available,
            'used': memory.used,
            'percent': memory.percent
        }
        
        # 磁盘信息
        disk = psutil.disk_usage('/')
        disk_info = {
            'total': disk.total,
            'used': disk.used,
            'free': disk.free,
            'percent': (disk.used / disk.total) * 100
        }
        
        # 网络信息
        network = psutil.net_io_counters()
        network_info = {
            'bytes_sent': network.bytes_sent,
            'bytes_recv': network.bytes_recv,
            'packets_sent': network.packets_sent,
            'packets_recv': network.packets_recv
        }
        
        return success_response({
            'system': system_info,
            'cpu': cpu_info,
            'memory': memory_info,
            'disk': disk_info,
            'network': network_info,
            'timestamp': datetime.now().isoformat()
        }, '获取系统信息成功')
        
    except Exception as e:
        return error_response(f'获取系统信息失败: {str(e)}')

@api_v1.route('/system/performance', methods=['GET'])
@token_required
def get_system_performance(current_user_id):
    """获取系统性能指标"""
    try:
        performance = system_service.get_performance_metrics()
        
        return success_response(performance, '获取系统性能指标成功')
        
    except Exception as e:
        return error_response(f'获取系统性能指标失败: {str(e)}')

@api_v1.route('/system/logs', methods=['GET'])
@token_required
def get_system_logs(current_user_id):
    """获取系统日志"""
    try:
        log_type = request.args.get('type', 'app')  # app, error, access
        lines = int(request.args.get('lines', 100))
        
        logs = system_service.get_logs(log_type, lines)
        
        return success_response({
            'logs': logs,
            'type': log_type,
            'lines': len(logs)
        }, '获取系统日志成功')
        
    except Exception as e:
        return error_response(f'获取系统日志失败: {str(e)}')

@api_v1.route('/system/config', methods=['GET'])
@token_required
def get_system_config(current_user_id):
    """获取系统配置"""
    try:
        config = system_service.get_system_config()
        
        return success_response(config, '获取系统配置成功')
        
    except Exception as e:
        return error_response(f'获取系统配置失败: {str(e)}')

@api_v1.route('/system/config', methods=['PUT'])
@token_required
def update_system_config(current_user_id):
    """更新系统配置"""
    try:
        config_data = request.get_json()
        
        success = system_service.update_system_config(config_data)
        
        if success:
            return success_response(None, '更新系统配置成功')
        else:
            return error_response('更新系统配置失败')
        
    except Exception as e:
        return error_response(f'更新系统配置失败: {str(e)}')

@api_v1.route('/system/database/status', methods=['GET'])
@token_required
def get_database_status(current_user_id):
    """获取数据库状态"""
    try:
        with get_db_connection() as db:
            # 数据库版本
            version = db.get_one("SELECT VERSION() as version")['version']
            
            # 数据库大小
            db_size = db.get_one(
                "SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb "
                "FROM information_schema.tables WHERE table_schema = DATABASE()"
            )['size_mb']
            
            # 表信息
            tables = db.get_list(
                """SELECT table_name, table_rows, 
                          ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
                   FROM information_schema.tables 
                   WHERE table_schema = DATABASE()
                   ORDER BY size_mb DESC"""
            )
            
            # 连接数
            connections = db.get_one("SHOW STATUS LIKE 'Threads_connected'")
            
        return success_response({
            'version': version,
            'size_mb': float(db_size) if db_size else 0,
            'tables': tables,
            'connections': int(connections['Value']) if connections else 0,
            'status': 'connected'
        }, '获取数据库状态成功')
        
    except Exception as e:
        return error_response(f'获取数据库状态失败: {str(e)}')

@api_v1.route('/system/backup/create', methods=['POST'])
@token_required
def create_backup(current_user_id):
    """创建系统备份"""
    try:
        backup_type = request.json.get('type', 'database')  # database, full
        
        backup_info = system_service.create_backup(backup_type)
        
        return success_response(backup_info, '创建备份成功')
        
    except Exception as e:
        return error_response(f'创建备份失败: {str(e)}')

@api_v1.route('/system/backup/list', methods=['GET'])
@token_required
def get_backup_list(current_user_id):
    """获取备份列表"""
    try:
        backups = system_service.get_backup_list()
        
        return success_response({
            'backups': backups,
            'count': len(backups)
        }, '获取备份列表成功')
        
    except Exception as e:
        return error_response(f'获取备份列表失败: {str(e)}')

@api_v1.route('/system/backup/<backup_id>/restore', methods=['POST'])
@token_required
def restore_backup(current_user_id, backup_id):
    """恢复备份"""
    try:
        success = system_service.restore_backup(backup_id)
        
        if success:
            return success_response(None, '恢复备份成功')
        else:
            return error_response('恢复备份失败')
        
    except Exception as e:
        return error_response(f'恢复备份失败: {str(e)}')

@api_v1.route('/system/cleanup', methods=['POST'])
@token_required
def system_cleanup(current_user_id):
    """系统清理"""
    try:
        cleanup_options = request.get_json()
        
        cleanup_result = system_service.cleanup_system(cleanup_options)
        
        return success_response(cleanup_result, '系统清理完成')
        
    except Exception as e:
        return error_response(f'系统清理失败: {str(e)}')

@api_v1.route('/system/health-check', methods=['GET'])
def health_check():
    """健康检查"""
    try:
        health_status = system_service.health_check()
        
        return success_response(health_status, '健康检查完成')
        
    except Exception as e:
        return error_response(f'健康检查失败: {str(e)}')

@api_v1.route('/system/restart', methods=['POST'])
@token_required
def restart_system(current_user_id):
    """重启系统服务"""
    try:
        # 获取当前用户权限
        with get_db_connection() as db:
            user = db.get_one("SELECT grade FROM user WHERE id=%s", (current_user_id,))
            
            if not user or user['grade'] != '超级管理员':
                return error_response('权限不足，只有超级管理员可以重启系统')
        
        # 执行重启
        system_service.restart_services()
        
        return success_response(None, '系统重启指令已发送')
        
    except Exception as e:
        return error_response(f'系统重启失败: {str(e)}')

@api_v1.route('/system/version', methods=['GET'])
def get_version():
    """获取系统版本信息"""
    try:
        version_info = {
            'version': '1.0.0',
            'build_date': '2025-06-20',
            'description': '基于Yolov8与ByteTrack的高速公路智慧监控平台',
            'author': 'YOLO Team',
            'python_version': platform.python_version(),
            'dependencies': {
                'flask': '2.3.0',
                'opencv': '4.8.0',
                'ultralytics': '8.0.0',
                'pyside6': '6.4.2'
            }
        }
        
        return success_response(version_info, '获取版本信息成功')
        
    except Exception as e:
        return error_response(f'获取版本信息失败: {str(e)}')

@api_v1.route('/system/update/check', methods=['GET'])
@token_required
def check_updates(current_user_id):
    """检查系统更新"""
    try:
        update_info = system_service.check_for_updates()
        
        return success_response(update_info, '检查更新完成')
        
    except Exception as e:
        return error_response(f'检查更新失败: {str(e)}')

@api_v1.route('/system/settings', methods=['GET'])
@token_required
def get_system_settings(current_user_id):
    """获取系统设置"""
    try:
        settings = system_service.get_system_settings()
        
        return success_response(settings, '获取系统设置成功')
        
    except Exception as e:
        return error_response(f'获取系统设置失败: {str(e)}')

@api_v1.route('/system/settings', methods=['PUT'])
@token_required
def update_system_settings(current_user_id):
    """更新系统设置"""
    try:
        settings_data = request.get_json()
        
        success = system_service.update_system_settings(settings_data)
        
        if success:
            return success_response(None, '更新系统设置成功')
        else:
            return error_response('更新系统设置失败')
        
    except Exception as e:
        return error_response(f'更新系统设置失败: {str(e)}')
