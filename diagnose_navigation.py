#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断前端导航问题
"""

import requests
import json
import time

def test_detection_center_apis():
    """测试检测中心相关的API"""
    base_url = "http://127.0.0.1:5500"
    
    print("🔐 获取token...")
    
    # 登录
    login_data = {"username": "admin", "password": "123456"}
    try:
        response = requests.post(f"{base_url}/api/v1/auth/login", json=login_data, timeout=5)
        if response.status_code != 200:
            print(f"❌ 登录失败: {response.status_code}")
            return
        
        login_result = response.json()
        if not login_result.get('success'):
            print(f"❌ 登录失败: {login_result.get('message')}")
            return
        
        token = login_result['data']['token']
        headers = {"Authorization": f"Bearer {token}"}
        print("✅ 登录成功")
        
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return
    
    # 测试检测中心页面可能需要的所有API
    detection_apis = [
        ("/api/v1/monitor/list?page=1&page_size=10", "监控点列表"),
        ("/api/v1/detection/tasks?page=1&size=10", "检测任务列表"),
        ("/api/v1/detection/config", "检测配置"),
        ("/api/v1/detection/models", "检测模型"),
        ("/api/v1/detection/upload", "上传配置"),
        ("/api/v1/analysis/statistics/overview", "概览统计"),
        ("/api/v1/system/health", "系统状态"),
    ]
    
    print(f"\n🧪 测试检测中心相关API...")
    
    all_success = True
    slow_apis = []
    error_apis = []
    
    for endpoint, name in detection_apis:
        try:
            start_time = time.time()
            response = requests.get(f"{base_url}{endpoint}", headers=headers, timeout=10)
            end_time = time.time()
            
            duration = end_time - start_time
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"   ✅ {name}: {duration:.3f}秒")
                    if duration > 2.0:
                        slow_apis.append((name, duration))
                else:
                    print(f"   ❌ {name}: API返回失败 - {result.get('message')}")
                    error_apis.append((name, result.get('message')))
                    all_success = False
            else:
                print(f"   ❌ {name}: HTTP {response.status_code}")
                error_apis.append((name, f"HTTP {response.status_code}"))
                all_success = False
                
        except requests.exceptions.Timeout:
            print(f"   ⏱️ {name}: 请求超时")
            error_apis.append((name, "请求超时"))
            all_success = False
        except Exception as e:
            print(f"   ❌ {name}: 异常 - {e}")
            error_apis.append((name, str(e)))
            all_success = False
    
    # 分析结果
    print(f"\n📊 检测中心API测试结果:")
    print(f"   总API数: {len(detection_apis)}")
    print(f"   成功数: {len(detection_apis) - len(error_apis)}")
    print(f"   失败数: {len(error_apis)}")
    print(f"   慢响应数: {len(slow_apis)}")
    
    if error_apis:
        print(f"\n❌ 失败的API:")
        for name, error in error_apis:
            print(f"   {name}: {error}")
    
    if slow_apis:
        print(f"\n⚠️ 慢响应API (>2秒):")
        for name, duration in slow_apis:
            print(f"   {name}: {duration:.3f}秒")
    
    return all_success, error_apis, slow_apis

def test_specific_monitor_api():
    """测试具体的监控点API格式"""
    base_url = "http://127.0.0.1:5500"
    
    # 登录
    login_data = {"username": "admin", "password": "123456"}
    response = requests.post(f"{base_url}/api/v1/auth/login", json=login_data)
    token = response.json()['data']['token']
    headers = {"Authorization": f"Bearer {token}"}
    
    print(f"\n🔍 检查监控点API详细格式...")
    
    # 测试监控点列表API
    response = requests.get(f"{base_url}/api/v1/monitor/list?page=1&page_size=10", headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        print(f"监控点API响应格式:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        # 检查数据结构
        if result.get('success') and result.get('data'):
            data = result['data']
            if 'monitors' in data:
                monitors = data['monitors']
                print(f"\n✅ 监控点数据正常，共 {len(monitors)} 个监控点")
                if monitors:
                    print(f"监控点字段: {list(monitors[0].keys())}")
            else:
                print(f"\n❌ 监控点数据格式异常，data字段: {list(data.keys())}")
        else:
            print(f"\n❌ 监控点API返回格式异常")
    else:
        print(f"❌ 监控点API请求失败: {response.status_code}")

def main():
    """主函数"""
    print("=" * 80)
    print("🚀 前端导航问题诊断 - 检测中心")
    print("=" * 80)
    
    # 1. 测试检测中心相关API
    all_success, error_apis, slow_apis = test_detection_center_apis()
    
    # 2. 测试具体的监控点API格式
    test_specific_monitor_api()
    
    print("\n" + "=" * 80)
    print("💡 导航无法跳转的可能原因:")
    print("1. 🔴 JavaScript错误 - 检查浏览器控制台")
    print("2. 🔴 API响应格式错误 - 前端无法解析数据")
    print("3. 🔴 API响应太慢 - 前端等待超时")
    print("4. 🔴 前端路由守卫 - 权限或状态检查失败")
    print("5. 🔴 前端状态管理 - 组件状态异常")
    
    print("\n🔧 解决步骤:")
    print("1. 按F12打开浏览器开发者工具")
    print("2. 查看Console标签的红色错误信息")
    print("3. 查看Network标签的API请求状态")
    print("4. 如果有API错误，修复对应的API")
    print("5. 如果API正常，检查前端代码")
    
    if error_apis:
        print(f"\n⚠️ 发现 {len(error_apis)} 个API问题，建议优先修复")
    elif slow_apis:
        print(f"\n⚠️ 发现 {len(slow_apis)} 个慢API，可能导致前端超时")
    elif all_success:
        print(f"\n✅ 所有API都正常，问题可能在前端代码")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
