# u7b80u5316u7684 FPS u8ba1u7b97u5668
import time
import numpy as np

class FPS:
    """u7528u4e8eu8ba1u7b97u548cu663eu793au5e27u7387u7684u7c7b"""
    
    def __init__(self, num_frames=30):
        self.num_frames = num_frames
        self.frames = []
        self.last_time = time.time()
    
    def update(self):
        """u66f4u65b0u5e27u8ba1u6570u5668"""
        current_time = time.time()
        self.frames.append(current_time - self.last_time)
        self.last_time = current_time
        
        # u4fddu6301u5e27u5217u8868u5728u6307u5b9au957fu5ea6
        if len(self.frames) > self.num_frames:
            self.frames.pop(0)
    
    def get_fps(self):
        """u8ba1u7b97u5e73u5747 FPS"""
        if not self.frames:
            return 0
        return len(self.frames) / sum(self.frames)
