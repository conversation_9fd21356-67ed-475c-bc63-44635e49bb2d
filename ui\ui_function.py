# -*- coding: utf-8 -*-
# <AUTHOR> pan
# 避免循环导入
from ui.main_window import Ui_MainWindow
from ui.custom_grips import CustomGrip
from PySide6.QtCore import QPropertyAnimation, QEasingCurve, QEvent, QTimer
from PySide6.QtCore import *
from PySide6.QtGui import *
from PySide6.QtWidgets import *
import time
from ui.dialog.violation_detection_dialog import ViolationDetectionWindow
from ui.dialog.pedestrian_detection_dialog import PedestrianDetectionWindow
from ui.dialog.tech_tracking_win import TechTrackingDialog
# 导入优化版多目标追踪对话框
from ui.dialog.optimized_multi_tracking_dialog import OptimizedMultiTrackingDialog

GLOBAL_STATE = False    # max min flag
GLOBAL_TITLE_BAR = True


class UIFuncitons(Ui_MainWindow):
    def __init__(self):
        super(UIFuncitons, self).__init__()
        #展开左菜单
        self.toggleMenu(True)
        #展开右菜单
        self.settingBox(True)
        #最大化窗口
        self.maximize_restore()
        # window control
        self.uiDefinitions()
        # Control the stretching of the four sides of the window
        self.resize_grips()
        # Show module to add shadow
        # 不再对左侧菜单栏应用阴影
        # self.shadow_style(self.LeftMenuBg, QColor(255, 255, 255))
        self.shadow_style(self.prm_page, QColor(255, 255, 255))

        # 连接按钮事件
        self.connectButtons()

    def connectButtons(self):
        """连接所有按钮的点击事件"""
        # 连接行人检测按钮
        self.src_pedestrian_button.clicked.connect(self.showPedestrianDetectionDialog)
        # 连接违规检测按钮
        self.src_violation_button.clicked.connect(self.showViolationDetectionDialog)
        # 注意: 多目标追踪按钮已在main.py中连接，不在这里连接

    #展开左菜单
    def toggleMenu(self, enable):
        if enable:
            standard = 68
            maxExtend = 180
            width = self.LeftMenuBg.width()

            if width == 68:
                widthExtended = maxExtend
            else:
                widthExtended = standard

            #Qt动画
            self.animation = QPropertyAnimation(self.LeftMenuBg, b"minimumWidth")
            self.animation.setDuration(500) # ms
            self.animation.setStartValue(width)
            self.animation.setEndValue(widthExtended)
            self.animation.setEasingCurve(QEasingCurve.InOutQuint)
            self.animation.start()

    #展开右菜单
    def settingBox(self, enable):
        if enable:
            #获取宽度
            widthRightBox = self.prm_page.width()           # right set column width
            widthLeftBox = self.LeftMenuBg.width()  # left column length
            maxExtend = 220
            standard = 0

            #设置最大宽度
            if widthRightBox == 0:
                widthExtended = maxExtend
            else:
                widthExtended = standard

            #左菜单的动画
            self.left_box = QPropertyAnimation(self.LeftMenuBg, b"minimumWidth")
            self.left_box.setDuration(500)
            self.left_box.setStartValue(widthLeftBox)
            self.left_box.setEndValue(68)
            self.left_box.setEasingCurve(QEasingCurve.InOutQuart)

            #设置界面的动画
            self.right_box = QPropertyAnimation(self.prm_page, b"minimumWidth")
            self.right_box.setDuration(500)
            self.right_box.setStartValue(widthRightBox)
            self.right_box.setEndValue(widthExtended)
            self.right_box.setEasingCurve(QEasingCurve.InOutQuart)

            #并行动画
            self.group = QParallelAnimationGroup()
            self.group.addAnimation(self.left_box)
            self.group.addAnimation(self.right_box)
            self.group.start()

    #最大化窗口
    def maximize_restore(self):
        global GLOBAL_STATE
        status = GLOBAL_STATE
        if status == False:
            GLOBAL_STATE = True
            self.showMaximized()    #最大化
            self.max_sf.setToolTip("Restore")
            self.frame_size_grip.hide()
            self.left_grip.hide()
            self.right_grip.hide()
            self.top_grip.hide()
            self.bottom_grip.hide()
        else:
            GLOBAL_STATE = False
            self.showNormal()       #最小化
            self.resize(self.width()+1, self.height()+1)
            self.max_sf.setToolTip("Maximize")
            self.frame_size_grip.show()
            self.left_grip.show()
            self.right_grip.show()
            self.top_grip.show()
            self.bottom_grip.show()

    # window control
    def uiDefinitions(self):
        # Double-click the title bar to maximize
        def dobleClickMaximizeRestore(event):
            if event.type() == QEvent.MouseButtonDblClick:
                QTimer.singleShot(250, lambda: UIFuncitons.maximize_restore(self))
        self.top.mouseDoubleClickEvent = dobleClickMaximizeRestore

        # MOVE WINDOW / MAXIMIZE / RESTORE
        def moveWindow(event):
            if GLOBAL_STATE:                        # IF MAXIMIZED CHANGE TO NORMAL
                UIFuncitons.maximize_restore(self)
            if event.buttons() == Qt.LeftButton:    # MOVE
                self.move(self.pos() + event.globalPos() - self.dragPos)
                self.dragPos = event.globalPos()
        self.top.mouseMoveEvent = moveWindow
        # CUSTOM GRIPS
        self.left_grip = CustomGrip(self, Qt.LeftEdge, True)
        self.right_grip = CustomGrip(self, Qt.RightEdge, True)
        self.top_grip = CustomGrip(self, Qt.TopEdge, True)
        self.bottom_grip = CustomGrip(self, Qt.BottomEdge, True)

        # MINIMIZE
        self.min_sf.clicked.connect(lambda: self.showMinimized())
        # MAXIMIZE/RESTORE
        self.max_sf.clicked.connect(lambda: UIFuncitons.maximize_restore(self))
        # CLOSE APPLICATION
        self.close_button.clicked.connect(self.close)

    # Control the stretching of the four sides of the window
    def resize_grips(self):
        self.left_grip.setGeometry(0, 10, 10, self.height())
        self.right_grip.setGeometry(self.width() - 10, 10, 10, self.height())
        self.top_grip.setGeometry(0, 0, self.width(), 10)
        self.bottom_grip.setGeometry(0, self.height() - 10, self.width(), 10)

    # Show module to add shadow - 修改为不对导航栏应用阴影
    def shadow_style(self, widget, Color):
        # 如果widget是LeftMenuBg或者包含在LeftMenuBg中的控件，则不应用阴影
        if hasattr(self, 'LeftMenuBg') and (
           widget == self.LeftMenuBg or
           (hasattr(widget, 'parent') and widget.parent() == self.LeftMenuBg) or
           widget.objectName().startswith("src_") or  # 导航栏按钮通常以src_开头
           "button" in widget.objectName().lower()  # 检查名称中是否包含button
        ):
            # 不应用阴影效果，保留现有样式
            print(f"跳过对导航栏元素应用阴影: {widget.objectName()}")
            return

        # 对其他控件应用阴影效果
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setOffset(8, 8)  # offset
        shadow.setBlurRadius(38)    # shadow radius
        shadow.setColor(Color)    # shadow color
        widget.setGraphicsEffect(shadow)

    def showPedestrianDetectionDialog(self):
        """显示行人检测对话框"""
        self.pedestrian_dialog = PedestrianDetectionWindow()
        self.pedestrian_dialog.configConfirmed.connect(self.onPedestrianConfigConfirmed)
        self.pedestrian_dialog.statsUpdated.connect(self.onPedestrianStatsUpdated)
        self.pedestrian_dialog.show()

    def onPedestrianConfigConfirmed(self, config):
        """处理行人检测配置确认"""
        print("行人检测配置已确认:", config)
        # TODO: 在这里实现配置处理逻辑

    def onPedestrianStatsUpdated(self, stats):
        """处理行人检测统计更新"""
        print("行人检测统计已更新:", stats)
        # TODO: 在这里实现统计更新逻辑

    def showViolationDetectionDialog(self):
        """显示违规检测窗口"""
        dialog = ViolationDetectionWindow(self.Main_QW)
        dialog.exec()

    # 移除toggleTrackingMode方法，因为该功能已在main.py中实现

    # 删除多目标追踪点击处理方法，因为我们现在只使用单目标追踪

    def showMultiTrackingDialog(self):
        """显示优化版多目标追踪窗口"""
        dialog = OptimizedMultiTrackingDialog(self.Main_QW)
        dialog.exec()