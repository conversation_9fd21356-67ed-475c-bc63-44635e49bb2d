# -*- coding: utf-8 -*-
# <AUTHOR> Enhanced by <PERSON>
# @Description : u591au76eeu6807u8ddfu8e2au5bf9u8bddu6846u754cu9762 - u9ad8u79d1u6280u98ceu683c

from PySide6.QtCore import (QCoreApplication, QMetaObject, QObject, QPoint, QRect,
    QSize, Qt, QPropertyAnimation, QEasingCurve, Signal, Property, QTimer)
from PySide6.QtGui import (QBrush, QColor, QConicalGradient, QCursor, QFont,
    QFontDatabase, QGradient, QIcon, QLinearGradient, QPainter, QPalette,
    QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QFrame, QLabel, QLineEdit, QPushButton,
    QSizePolicy, QVBoxLayout, QH<PERSON><PERSON><PERSON><PERSON><PERSON>, Q<PERSON>idget, QGraphicsDrop<PERSON><PERSON><PERSON><PERSON>ffect,
    QScrollArea, QGridLayout, QSlider, QComboBox, QCheckBox, QProgressBar, QGraphicsBlurEffect)

class MultiTrackingForm(object):
    def setupUi(self, Form):
        if not Form.objectName():
            Form.setObjectName(u"MultiTrackingForm")
        
        # u8bbeu7f6eu7a97u53e3u5927u5c0fu548cu6837u5f0f - u6bd4u5355u76eeu6807u8ddfu8e2au66f4u5927
        Form.resize(800, 600)
        Form.setMinimumSize(QSize(800, 600))
        Form.setMaximumSize(QSize(1200, 800))
        Form.setStyleSheet(u"""
        #MultiTrackingForm {
            background-color: rgb(30, 40, 50);
            border: 2px solid rgba(0, 200, 255, 0.7);
        }
        QWidget {
            color: white;
            font-family: 'Segoe UI', Arial;
        }
        """)
        
        # 使用常规窗口样式而非无边框，确保可见
        # Form.setWindowFlags(Qt.FramelessWindowHint)
        # Form.setAttribute(Qt.WA_TranslucentBackground)
        
        # u4e3bu5e03u5c40
        self.verticalLayout = QVBoxLayout(Form)
        self.verticalLayout.setSpacing(10)
        self.verticalLayout.setContentsMargins(15, 15, 15, 15)
        
        # u6807u9898u680f
        self.titleBar = QFrame(Form)
        self.titleBar.setMinimumSize(QSize(0, 50))
        self.titleBar.setMaximumSize(QSize(16777215, 50))
        self.titleBar.setStyleSheet(u"QFrame {\n"
                                   "    border-radius: 10px;\n"
                                   "    background-color: rgba(0, 40, 80, 200);\n"
                                   "}")
        
        # u6dfbu52a0u9634u5f71u6548u679c
        shadow = QGraphicsDropShadowEffect(self.titleBar)
        shadow.setBlurRadius(15)
        shadow.setOffset(0, 2)
        shadow.setColor(QColor(0, 200, 255, 150))
        self.titleBar.setGraphicsEffect(shadow)
        
        # u6807u9898u680fu5e03u5c40
        self.horizontalLayout_title = QHBoxLayout(self.titleBar)
        self.horizontalLayout_title.setSpacing(6)
        self.horizontalLayout_title.setContentsMargins(15, 0, 15, 0)
        
        # u6807u9898u6807u7b7e
        self.titleLabel = QLabel(self.titleBar)
        self.titleLabel.setStyleSheet(u"QLabel {\n"
                                    "    color: rgb(0, 220, 255);\n"
                                    "    font: 700 16pt \"Segoe UI\";\n"
                                    "    background-color: transparent;\n"
                                    "}")
        self.horizontalLayout_title.addWidget(self.titleLabel)
        
        # u6807u9898u680fu7a7au767du533au57df
        self.horizontalSpacer = QWidget()
        self.horizontalSpacer.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.horizontalLayout_title.addWidget(self.horizontalSpacer)
        
        # u6700u5c0fu5316u6309u94ae
        self.minButton = QPushButton(self.titleBar)
        self.minButton.setMinimumSize(QSize(36, 36))
        self.minButton.setMaximumSize(QSize(36, 36))
        self.minButton.setStyleSheet(u"QPushButton {\n"
                                     "    background-color: rgba(0, 180, 255, 50);\n"
                                     "    color: rgb(0, 220, 255);\n"
                                     "    border-radius: 18px;\n"
                                     "    font: 700 14pt \"Segoe UI\";\n"
                                     "}\n"
                                     "QPushButton:hover {\n"
                                     "    background-color: rgba(0, 180, 255, 100);\n"
                                     "}\n"
                                     "QPushButton:pressed {\n"
                                     "    background-color: rgba(0, 150, 200, 150);\n"
                                     "}")
        self.horizontalLayout_title.addWidget(self.minButton)
        
        # u5173u95edu6309u94ae
        self.closeButton = QPushButton(self.titleBar)
        self.closeButton.setMinimumSize(QSize(36, 36))
        self.closeButton.setMaximumSize(QSize(36, 36))
        self.closeButton.setStyleSheet(u"QPushButton {\n"
                                     "    background-color: rgba(180, 0, 0, 50);\n"
                                     "    color: white;\n"
                                     "    border-radius: 18px;\n"
                                     "    font: 700 14pt \"Segoe UI\";\n"
                                     "}\n"
                                     "QPushButton:hover {\n"
                                     "    background-color: rgba(255, 50, 50, 150);\n"
                                     "}\n"
                                     "QPushButton:pressed {\n"
                                     "    background-color: rgba(200, 0, 0, 200);\n"
                                     "}")
        self.horizontalLayout_title.addWidget(self.closeButton)
        self.verticalLayout.addWidget(self.titleBar)
        
        # u5185u5bb9u533au57df
        self.contentWidget = QWidget(Form)
        self.contentLayout = QHBoxLayout(self.contentWidget)
        self.contentLayout.setSpacing(10)
        self.contentLayout.setContentsMargins(0, 0, 0, 0)
        
        # u5de6u4fa7u9762u677f - u76d1u63a7u753bu9762u548cu63a7u5236u533a
        self.leftPanel = QFrame(self.contentWidget)
        self.leftPanel.setMinimumWidth(500)
        self.leftPanel.setStyleSheet(u"QFrame {\n"
                                   "    background-color: rgba(0, 30, 60, 150);\n"
                                   "    border-radius: 10px;\n"
                                   "    border: 1px solid rgba(0, 180, 255, 0.5);\n"
                                   "}")
        
        # u5de6u4fa7u9762u677fu5e03u5c40
        self.leftLayout = QVBoxLayout(self.leftPanel)
        self.leftLayout.setSpacing(10)
        self.leftLayout.setContentsMargins(10, 10, 10, 10)
        
        # 视频显示区域
        self.videoFrame = QFrame()
        self.videoFrame.setMinimumHeight(320)
        self.videoFrame.setStyleSheet(u"QFrame {\n"
                                    u"    background-color: rgba(0, 15, 35, 150);\n"
                                    u"    border-radius: 5px;\n"
                                    u"    border: 1px solid rgba(0, 150, 230, 0.6);\n"
                                    u"}"
                                    )
        
        # 视频显示布局
        self.videoLayout = QVBoxLayout(self.videoFrame)
        self.videoLayout.setContentsMargins(2, 2, 2, 2)
        
        # 视频标签
        self.videoLabel = QLabel()
        self.videoLabel.setAlignment(Qt.AlignCenter)
        self.videoLabel.setStyleSheet(u"background-color: transparent;")
        self.videoLabel.setText("多目标追踪视图")
        self.videoLayout.addWidget(self.videoLabel)
        
        # 添加视频区到左侧布局
        self.leftLayout.addWidget(self.videoFrame)
        
        # 控制栏 - 使用复杂的网格布局
        self.controlFrame = QFrame()
        self.controlFrame.setMinimumHeight(120)
        self.controlFrame.setStyleSheet(u"QFrame {\n"
                                      u"    background-color: rgba(0, 30, 70, 120);\n"
                                      u"    border-radius: 5px;\n"
                                      u"    border: 1px solid rgba(0, 150, 230, 0.4);\n"
                                      u"}"
                                      )
        
        # 控制区布局
        self.controlGrid = QGridLayout(self.controlFrame)
        self.controlGrid.setContentsMargins(10, 10, 10, 10)
        self.controlGrid.setSpacing(10)
        
        # 置信度阈值滑块
        self.confLabel = QLabel("置信度阈值:")
        self.confLabel.setStyleSheet("color: rgb(180, 220, 255);")
        self.confSlider = QSlider(Qt.Horizontal)
        self.confSlider.setMinimum(1)
        self.confSlider.setMaximum(10)
        self.confSlider.setValue(4)  # 默认值为0.4
        self.confSlider.setStyleSheet(u"""
            QSlider::groove:horizontal {
                height: 8px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 rgba(0, 50, 100, 150), 
                    stop:1 rgba(0, 150, 230, 150));
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: qradialgradient(cx:0.5, cy:0.5, radius: 0.8, fx:0.5, fy:0.5, 
                    stop:0 rgba(0, 200, 255, 200), 
                    stop:1 rgba(0, 150, 200, 200));
                width: 18px;
                height: 18px;
                margin: -5px 0;
                border-radius: 9px;
                border: 1px solid rgba(0, 100, 150, 200);
            }
        """)
        self.confValueLabel = QLabel("0.4")
        self.confValueLabel.setStyleSheet("color: rgb(0, 220, 255); font-weight: bold;")
        
        # 关联滑块与标签值的更新
        self.controlGrid.addWidget(self.confLabel, 0, 0)
        self.controlGrid.addWidget(self.confSlider, 0, 1)
        self.controlGrid.addWidget(self.confValueLabel, 0, 2)
        
        # IOU阈值滑块
        self.iouLabel = QLabel("IOU阈值:")
        self.iouLabel.setStyleSheet("color: rgb(180, 220, 255);")
        self.iouSlider = QSlider(Qt.Horizontal)
        self.iouSlider.setMinimum(1)
        self.iouSlider.setMaximum(10)
        self.iouSlider.setValue(5)  # 默认值为0.5
        self.iouSlider.setStyleSheet(u"""
            QSlider::groove:horizontal {
                height: 8px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 rgba(0, 50, 100, 150), 
                    stop:1 rgba(0, 150, 230, 150));
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: qradialgradient(cx:0.5, cy:0.5, radius: 0.8, fx:0.5, fy:0.5, 
                    stop:0 rgba(0, 200, 255, 200), 
                    stop:1 rgba(0, 150, 200, 200));
                width: 18px;
                height: 18px;
                margin: -5px 0;
                border-radius: 9px;
                border: 1px solid rgba(0, 100, 150, 200);
            }
        """)
        self.iouValueLabel = QLabel("0.5")
        self.iouValueLabel.setStyleSheet("color: rgb(0, 220, 255); font-weight: bold;")
        
        # 关联滑块与标签值的更新
        self.controlGrid.addWidget(self.iouLabel, 1, 0)
        self.controlGrid.addWidget(self.iouSlider, 1, 1)
        self.controlGrid.addWidget(self.iouValueLabel, 1, 2)
        
        # 追踪选项
        self.trackingOptionsLabel = QLabel("追踪选项:")
        self.trackingOptionsLabel.setStyleSheet("color: rgb(180, 220, 255);")
        self.showLabelsCheck = QCheckBox("显示标签")
        self.showLabelsCheck.setChecked(True)
        self.showLabelsCheck.setStyleSheet("color: rgb(220, 220, 255);")
        self.showTrailsCheck = QCheckBox("显示轨迹")
        self.showTrailsCheck.setChecked(True)
        self.showTrailsCheck.setStyleSheet("color: rgb(220, 220, 255);")
        
        # 添加追踪选项到控制网格
        self.controlGrid.addWidget(self.trackingOptionsLabel, 2, 0)
        self.optionsLayout = QHBoxLayout()
        self.optionsLayout.addWidget(self.showLabelsCheck)
        self.optionsLayout.addWidget(self.showTrailsCheck)
        self.controlGrid.addLayout(self.optionsLayout, 2, 1, 1, 2)
        
        # 添加控制栏到左侧布局
        self.leftLayout.addWidget(self.controlFrame)
        
        # 追踪控制按钮
        self.buttonFrame = QFrame()
        self.buttonFrame.setMinimumHeight(60)
        self.buttonFrame.setStyleSheet("background-color: transparent;")
        
        # 按钮水平布局
        self.buttonLayout = QHBoxLayout(self.buttonFrame)
        self.buttonLayout.setContentsMargins(5, 5, 5, 5)
        self.buttonLayout.setSpacing(15)
        
        # 开始追踪按钮
        self.startButton = QPushButton("启动多目标追踪")
        self.startButton.setMinimumSize(QSize(180, 40))
        self.startButton.setStyleSheet(u"""
            QPushButton {
                background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 rgba(0, 80, 150, 180), 
                    stop:1 rgba(0, 150, 220, 200));
                color: white;
                border-radius: 10px;
                font-weight: bold;
                font-size: 14px;
                border: 1px solid rgba(0, 200, 255, 0.8);
            }
            QPushButton:hover {
                background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 rgba(0, 100, 180, 220), 
                    stop:1 rgba(0, 180, 255, 240));
            }
            QPushButton:pressed {
                background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 rgba(0, 60, 120, 200), 
                    stop:1 rgba(0, 120, 180, 220));
            }
        """)
        
        # 停止追踪按钮
        self.stopButton = QPushButton("停止追踪")
        self.stopButton.setMinimumSize(QSize(180, 40))
        self.stopButton.setStyleSheet(u"""
            QPushButton {
                background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 rgba(150, 30, 30, 180), 
                    stop:1 rgba(200, 50, 50, 200));
                color: white;
                border-radius: 10px;
                font-weight: bold;
                font-size: 14px;
                border: 1px solid rgba(255, 100, 100, 0.8);
            }
            QPushButton:hover {
                background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 rgba(180, 40, 40, 220), 
                    stop:1 rgba(230, 60, 60, 240));
            }
            QPushButton:pressed {
                background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 rgba(120, 20, 20, 200), 
                    stop:1 rgba(180, 40, 40, 220));
            }
        """)
        
        # 添加按钮到按钮布局
        self.buttonLayout.addStretch()
        self.buttonLayout.addWidget(self.startButton)
        self.buttonLayout.addWidget(self.stopButton)
        self.buttonLayout.addStretch()
        
        # 添加按钮区到左侧布局
        self.leftLayout.addWidget(self.buttonFrame)
        
        # 状态栏
        self.statusFrame = QFrame()
        self.statusFrame.setMinimumHeight(30)
        self.statusFrame.setMaximumHeight(30)
        self.statusFrame.setStyleSheet(u"QFrame {\n"
                                     u"    background-color: rgba(0, 30, 60, 150);\n"
                                     u"    border-radius: 5px;\n"
                                     u"    border: 1px solid rgba(0, 150, 230, 0.4);\n"
                                     u"}"
                                     )
        
        # 状态栏布局
        self.statusLayout = QHBoxLayout(self.statusFrame)
        self.statusLayout.setContentsMargins(10, 0, 10, 0)
        
        # 状态标签
        self.statusLabel = QLabel("就绪")
        self.statusLabel.setStyleSheet("color: rgb(0, 230, 150); font-weight: bold;")
        self.statusLayout.addWidget(self.statusLabel)
        
        # 添加状态栏到左侧布局
        self.leftLayout.addWidget(self.statusFrame)
        
        # 将左侧面板添加到内容布局
        self.contentLayout.addWidget(self.leftPanel, 3)  # 左侧占比为3
        
        # 右侧面板 - 数据可视化和目标列表
        self.rightPanel = QFrame(self.contentWidget)
        self.rightPanel.setMinimumWidth(300)
        self.rightPanel.setStyleSheet(u"QFrame {\n"
                                    u"    background-color: rgba(0, 25, 50, 150);\n"
                                    u"    border-radius: 10px;\n"
                                    u"    border: 1px solid rgba(0, 180, 255, 0.5);\n"
                                    u"}"
                                    )
        
        # 右侧面板布局
        self.rightLayout = QVBoxLayout(self.rightPanel)
        self.rightLayout.setSpacing(10)
        self.rightLayout.setContentsMargins(10, 10, 10, 10)
        
        # 目标统计区域
        self.statsFrame = QFrame()
        self.statsFrame.setMinimumHeight(120)
        self.statsFrame.setStyleSheet(u"QFrame {\n"
                                    u"    background-color: rgba(0, 20, 45, 170);\n"
                                    u"    border-radius: 5px;\n"
                                    u"    border: 1px solid rgba(0, 130, 200, 0.6);\n"
                                    u"}"
                                    )
        
        # 统计区布局
        self.statsLayout = QVBoxLayout(self.statsFrame)
        self.statsLayout.setContentsMargins(10, 10, 10, 10)
        self.statsLayout.setSpacing(8)
        
        # 统计标题
        self.statsTitle = QLabel("目标统计")
        self.statsTitle.setStyleSheet("color: rgb(0, 220, 255); font-weight: bold; font-size: 14px;")
        self.statsTitle.setAlignment(Qt.AlignCenter)
        self.statsLayout.addWidget(self.statsTitle)
        
        # 目标总数
        self.totalLayout = QHBoxLayout()
        self.totalLabel = QLabel("检测到的目标:"
                               )
        self.totalLabel.setStyleSheet("color: rgb(180, 220, 255);")
        self.totalCountLabel = QLabel("0")
        self.totalCountLabel.setStyleSheet("color: rgb(0, 255, 200); font-weight: bold; font-size: 16px;")
        self.totalLayout.addWidget(self.totalLabel)
        self.totalLayout.addWidget(self.totalCountLabel)
        self.statsLayout.addLayout(self.totalLayout)
        
        # 目标分类数量
        self.classLayout = QHBoxLayout()
        self.classLabel = QLabel("车辆类别:"
                               )
        self.classLabel.setStyleSheet("color: rgb(180, 220, 255);")
        self.classCountLabel = QLabel("载客车: 0  货车: 0  摩托: 0")
        self.classCountLabel.setStyleSheet("color: rgb(220, 220, 255);")
        self.classLayout.addWidget(self.classLabel)
        self.classLayout.addWidget(self.classCountLabel)
        self.statsLayout.addLayout(self.classLayout)
        
        # 平均速度
        self.speedLayout = QHBoxLayout()
        self.speedLabel = QLabel("平均速度:"
                               )
        self.speedLabel.setStyleSheet("color: rgb(180, 220, 255);")
        self.speedValueLabel = QLabel("0 km/h")
        self.speedValueLabel.setStyleSheet("color: rgb(220, 220, 255);")
        self.speedLayout.addWidget(self.speedLabel)
        self.speedLayout.addWidget(self.speedValueLabel)
        self.statsLayout.addLayout(self.speedLayout)
        
        # 添加统计区域到右侧布局
        self.rightLayout.addWidget(self.statsFrame)
        
        # 目标信息区域 - 使用滚动区域
        self.targetsFrame = QFrame()
        self.targetsFrame.setStyleSheet(u"QFrame {\n"
                                    u"    background-color: rgba(0, 20, 45, 170);\n"
                                    u"    border-radius: 5px;\n"
                                    u"    border: 1px solid rgba(0, 130, 200, 0.6);\n"
                                    u"}"
                                    )
        
        # 目标区布局
        self.targetsLayout = QVBoxLayout(self.targetsFrame)
        self.targetsLayout.setContentsMargins(10, 10, 10, 10)
        self.targetsLayout.setSpacing(10)
        
        # 目标列表标题
        self.targetsTitle = QLabel("目标列表")
        self.targetsTitle.setStyleSheet("color: rgb(0, 220, 255); font-weight: bold; font-size: 14px;")
        self.targetsTitle.setAlignment(Qt.AlignCenter)
        self.targetsLayout.addWidget(self.targetsTitle)
        
        # 目标显示区域（取消滚动条）
        self.targetsContainer = QWidget()
        self.targetsContainer.setStyleSheet("background-color: transparent;")
        
        # 使用网格布局实现先水平后垂直的排列
        self.targetsContainerLayout = QGridLayout(self.targetsContainer)
        self.targetsContainerLayout.setContentsMargins(0, 0, 5, 0)
        self.targetsContainerLayout.setSpacing(10)
        
        # 直接添加容器到布局，不使用滚动区域
        self.targetsLayout.addWidget(self.targetsContainer)
        
        # 添加目标信息区域到右侧布局
        self.rightLayout.addWidget(self.targetsFrame)
        
        # 右侧按钮区 - 数据导出和清除
        self.rightButtonFrame = QFrame()
        self.rightButtonFrame.setMinimumHeight(40)
        self.rightButtonFrame.setMaximumHeight(40)
        self.rightButtonFrame.setStyleSheet("background-color: transparent;")
        
        # 右侧按钮布局
        self.rightButtonLayout = QHBoxLayout(self.rightButtonFrame)
        self.rightButtonLayout.setContentsMargins(0, 0, 0, 0)
        self.rightButtonLayout.setSpacing(10)
        
        # 清除数据按钮
        self.clearButton = QPushButton("清除统计")
        self.clearButton.setStyleSheet(u"""
            QPushButton {
                background-color: rgba(80, 0, 80, 150);
                color: white;
                border-radius: 5px;
                padding: 5px 10px;
                border: 1px solid rgba(150, 0, 150, 0.6);
            }
            QPushButton:hover {
                background-color: rgba(120, 0, 120, 180);
            }
            QPushButton:pressed {
                background-color: rgba(60, 0, 60, 150);
            }
        """)
        
        # 数据导出按钮
        self.exportButton = QPushButton("导出数据")
        self.exportButton.setStyleSheet(u"""
            QPushButton {
                background-color: rgba(0, 80, 80, 150);
                color: white;
                border-radius: 5px;
                padding: 5px 10px;
                border: 1px solid rgba(0, 150, 150, 0.6);
            }
            QPushButton:hover {
                background-color: rgba(0, 120, 120, 180);
            }
            QPushButton:pressed {
                background-color: rgba(0, 60, 60, 150);
            }
        """)
        
        # 添加按钮到布局
        self.rightButtonLayout.addWidget(self.clearButton)
        self.rightButtonLayout.addWidget(self.exportButton)
        
        # 添加按钮区到右侧布局
        self.rightLayout.addWidget(self.rightButtonFrame)
        
        # 将右侧面板添加到内容布局
        self.contentLayout.addWidget(self.rightPanel, 2)  # 右侧占比为2
        
        # 初始化轨迹目标存储容器
        self.track_targets = {}  # {id: {"class": str, "box": tuple, "confidence": float, ...}}
        self.target_cards = {}   # {id: card_widget}
        
        # 连接信号与槽
        self.connectSignals()
        
    def connectSignals(self):
        """连接所有信号和槽函数"""
        # 连接滑块值变化事件
        self.confSlider.valueChanged.connect(self.updateConfidenceLabel)
        self.iouSlider.valueChanged.connect(self.updateIouLabel)
        
        # 连接按钮事件
        self.startButton.clicked.connect(self.startTracking)
        self.stopButton.clicked.connect(self.stopTracking)
        self.closeButton.clicked.connect(self.closeDialog)
        self.minimizeButton.clicked.connect(self.showMinimized)
        
        # 连接右侧按钮事件
        self.clearButton.clicked.connect(self.clearStatistics)
        self.exportButton.clicked.connect(self.exportData)
        
    def updateConfidenceLabel(self, value):
        """更新置信度标签值
        
        Args:
            value: int 滑块值 (1-10)
        """
        confidence = value / 10.0  # 转换为0.1-1.0的浮点数
        self.confValueLabel.setText(f"{confidence:.1f}")
        
    def updateIouLabel(self, value):
        """更新IOU标签值
        
        Args:
            value: int 滑块值 (1-10)
        """
        iou = value / 10.0  # 转换为0.1-1.0的浮点数
        self.iouValueLabel.setText(f"{iou:.1f}")
        
    def startTracking(self):
        """开始多目标追踪"""
        # 更新状态
        self.statusLabel.setText("正在追踪...")
        self.statusLabel.setStyleSheet("color: rgb(0, 200, 255); font-weight: bold;")
        
        # 禁用开始按钮，启用停止按钮
        self.startButton.setEnabled(False)
        self.stopButton.setEnabled(True)
        
        # TODO: 启动多目标追踪线程
        # self.tracking_thread = MultiTrackingThread(...)
        # self.tracking_thread.update_signal.connect(self.updateTrackingResults)
        # self.tracking_thread.start()
        
        # 测试数据 - 在实际实现中移除
        self.mockTrackingData()
        
    def stopTracking(self):
        """停止多目标追踪"""
        # 更新状态
        self.statusLabel.setText("已停止")
        self.statusLabel.setStyleSheet("color: rgb(255, 100, 100); font-weight: bold;")
        
        # 启用开始按钮，禁用停止按钮
        self.startButton.setEnabled(True)
        self.stopButton.setEnabled(False)
        
        # TODO: 停止追踪线程
        # if self.tracking_thread and self.tracking_thread.isRunning():
        #     self.tracking_thread.stop()
        #     self.tracking_thread.wait()
        
    def closeDialog(self):
        """关闭对话框"""
        # 停止所有正在运行的线程
        self.stopTracking()
        
        # 关闭对话框
        self.close()
        
    def clearStatistics(self):
        """清除统计数据"""
        # 重置统计数据
        self.totalCountLabel.setText("0")
        self.classCountLabel.setText("载客车: 0  货车: 0  摩托: 0")
        self.speedValueLabel.setText("0 km/h")
        
        # 清除目标信息卡片
        for card in self.target_cards.values():
            card.setParent(None)  # 从容器中移除
            card.deleteLater()     # 删除卡片
        
        # 重置存储容器
        self.track_targets = {}
        self.target_cards = {}
        
    def exportData(self):
        """导出追踪数据"""
        # TODO: 实现数据导出功能
        pass
        
    def updateTrackingResults(self, results):
        """更新追踪结果
        
        Args:
            results: dict 检测结果数据
                {
                    "targets": [
                        {
                            "id": int,
                            "class": str,
                            "box": (x1, y1, x2, y2),
                            "confidence": float,
                            "track_points": [(x1,y1), ...],
                            "speed": float,
                            "is_lost": bool
                        },
                        ...
                    ],
                    "stats": {
                        "total": int,
                        "class_counts": {
                            "car": int, 
                            "truck": int, 
                            "bus": int, ...
                        },
                        "avg_speed": float
                    },
                    "frame": image (numpy array)
                }
        """
        if not results:
            return
        
        # 更新统计数据
        if "stats" in results:
            stats = results["stats"]
            # 更新目标总数
            self.totalCountLabel.setText(str(stats["total"]))
            
            # 更新类别统计
            class_counts = stats["class_counts"]
            class_text = f"载客车: {class_counts.get('car', 0)}  "
            class_text += f"货车: {class_counts.get('truck', 0)}  "
            class_text += f"摩托: {class_counts.get('motorcycle', 0)}"
            self.classCountLabel.setText(class_text)
            
            # 更新平均速度
            self.speedValueLabel.setText(f"{stats.get('avg_speed', 0):.1f} km/h")
        
        # 更新目标信息卡片
        if "targets" in results:
            self.updateTargetCards(results["targets"])
            
        # 更新视频帧
        if "frame" in results:
            self.updateFrame(results["frame"])
            
    def updateTargetCards(self, targets):
        """更新目标信息卡片
        
        Args:
            targets: list 检测到的目标列表
        """
        # 记录当前有效ID
        current_ids = {target["id"] for target in targets}
        
        # 处理消失的目标（仅标记为消失，不立即删除）
        for target_id in self.target_cards.keys():
            if target_id not in current_ids:
                if target_id in self.target_cards:
                    # 将目标标记为消失
                    self.target_cards[target_id].set_lost(True)
        
        # 处理新建和更新的目标
        for target in targets:
            target_id = target["id"]
            target_class = target["class"]
            confidence = target["confidence"]
            is_lost = target.get("is_lost", False)
            
            # 生成目标的颜色 (基于ID创建固定颜色)
            import random
            random.seed(target_id)  # 确保目标ID的颜色始终一致
            color = (
                random.randint(100, 255),
                random.randint(100, 255),
                random.randint(100, 255)
            )
            
            if target_id not in self.target_cards:
                # 创建新目标卡片
                from PyQt5.QtGui import QColor
                card = self.createTargetCard(target_id, target_class, confidence, 
                                           QColor(color[0], color[1], color[2]))
                # 计算网格位置（先水平后垂直）
                card_count = len(self.target_cards)
                cols_per_row = 3  # 每行3个卡片
                row = card_count // cols_per_row
                col = card_count % cols_per_row
                # 添加到网格布局
                self.targetsContainerLayout.addWidget(card, row, col)
                self.target_cards[target_id] = card
            else:
                # 更新现有卡片
                card = self.target_cards[target_id]
                card.update_confidence(confidence)
                # 更新轨迹长度
                if "track_points" in target:
                    track_length = len(target["track_points"])
                    card.update_track_length(track_length)
                # 更新速度
                if "speed" in target:
                    card.update_speed(target["speed"])
                # 更新状态
                card.set_lost(is_lost)
            
            # 更新缩略图
            if "thumbnail" in target:
                from PyQt5.QtGui import QPixmap, QImage
                import numpy as np
                
                thumbnail = target["thumbnail"]
                if isinstance(thumbnail, np.ndarray):
                    # 将numpy数组转换为QPixmap
                    h, w, c = thumbnail.shape
                    qimg = QImage(thumbnail.data, w, h, w * c, QImage.Format_RGB888)
                    pixmap = QPixmap.fromImage(qimg)
                    card.update_thumb(pixmap)
                elif isinstance(thumbnail, QPixmap):
                    card.update_thumb(thumbnail)
    
    def createTargetCard(self, target_id, target_class, confidence, color):
        """创建目标信息卡片
        
        Args:
            target_id: int 目标ID
            target_class: str 目标类别
            confidence: float 置信度
            color: QColor 卡片颜色
            
        Returns:
            QFrame 目标卡片
        """
        from PyQt5.QtWidgets import QFrame, QVBoxLayout, QHBoxLayout, QLabel
        from PyQt5.QtCore import Qt, QSize
        from PyQt5.QtGui import QColor
        
        # 创建目标卡片容器
        card = QFrame()
        card.setFrameShape(QFrame.StyledPanel)
        card.setMinimumHeight(80)
        card.setMaximumHeight(80)
        card.setStyleSheet(f"""
            QFrame {{
                background-color: rgba(0, 35, 70, 180);
                border-radius: 5px;
                border-left: 3px solid rgba({color.red()}, {color.green()}, {color.blue()}, 200);
            }}
        """)
        
        # 卡片布局
        mainLayout = QHBoxLayout(card)
        mainLayout.setContentsMargins(8, 8, 8, 8)
        mainLayout.setSpacing(10)
        
        # 缩略图区域
        thumbFrame = QFrame()
        thumbFrame.setMinimumSize(60, 60)
        thumbFrame.setMaximumSize(60, 60)
        thumbFrame.setStyleSheet("""
            QFrame {
                background-color: rgba(0, 0, 0, 100);
                border-radius: 3px;
                border: 1px solid rgba(100, 200, 255, 0.5);
            }
        """)
        
        # 缩略图布局
        thumbLayout = QVBoxLayout(thumbFrame)
        thumbLayout.setContentsMargins(1, 1, 1, 1)
        
        # 缩略图标签
        thumbLabel = QLabel()
        thumbLabel.setAlignment(Qt.AlignCenter)
        thumbLabel.setText("无图像")
        thumbLabel.setStyleSheet("color: rgba(150, 200, 255, 150);")
        thumbLayout.addWidget(thumbLabel)
        
        # 添加缩略图到卡片布局
        mainLayout.addWidget(thumbFrame)
        
        # 信息区域
        infoLayout = QVBoxLayout()
        infoLayout.setSpacing(2)
        
        # ID和类别行
        titleLayout = QHBoxLayout()
        titleLayout.setSpacing(5)
        
        # ID标签
        idLabel = QLabel(f"ID: {target_id}")
        idLabel.setStyleSheet(f"""
            color: rgb({color.red()}, {color.green()}, {color.blue()});
            font-weight: bold;
            font-size: 13px;
        """)
        titleLayout.addWidget(idLabel)
        
        # 类别标签
        classLabel = QLabel(f"类别: {target_class}")
        classLabel.setStyleSheet("""
            color: rgb(200, 220, 255);
            font-size: 12px;
        """)
        titleLayout.addWidget(classLabel)
        titleLayout.addStretch()
        
        # 添加标题行到信息布局
        infoLayout.addLayout(titleLayout)
        
        # 置信度行
        confidenceLayout = QHBoxLayout()
        confidenceLayout.setSpacing(5)
        
        # 置信度标签
        confidenceLabel = QLabel(f"置信度: {confidence:.2f}")
        confidenceLabel.setStyleSheet("""
            color: rgb(180, 220, 255);
            font-size: 12px;
        """)
        confidenceLayout.addWidget(confidenceLabel)
        
        # 速度标签
        speedLabel = QLabel("速度: -- km/h")
        speedLabel.setStyleSheet("""
            color: rgb(180, 220, 255);
            font-size: 12px;
        """)
        confidenceLayout.addWidget(speedLabel)
        confidenceLayout.addStretch()
        
        # 添加置信度行到信息布局
        infoLayout.addLayout(confidenceLayout)
        
        # 轨迹信息行
        trackLayout = QHBoxLayout()
        trackLayout.setSpacing(5)
        
        # 轨迹长度标签
        trackLengthLabel = QLabel("轨迹点: 0")
        trackLengthLabel.setStyleSheet("""
            color: rgb(180, 220, 255);
            font-size: 12px;
        """)
        trackLayout.addWidget(trackLengthLabel)
        
        # 目标状态标签
        statusLabel = QLabel("状态: 跟踪中")
        statusLabel.setStyleSheet("""
            color: rgb(0, 230, 150);
            font-size: 12px;
            font-weight: bold;
        """)
        trackLayout.addWidget(statusLabel)
        trackLayout.addStretch()
        
        # 添加轨迹行到信息布局
        infoLayout.addLayout(trackLayout)
        
        # 添加信息区域到卡片布局
        mainLayout.addLayout(infoLayout)
        
        # 保存重要组件引用
        card.idLabel = idLabel
        card.classLabel = classLabel
        card.confidenceLabel = confidenceLabel
        card.speedLabel = speedLabel
        card.trackLengthLabel = trackLengthLabel
        card.statusLabel = statusLabel
        card.thumbLabel = thumbLabel
        card.color = color
        
        # 添加方法
        card.update_confidence = lambda conf: card.confidenceLabel.setText(f"置信度: {conf:.2f}")
        card.update_speed = lambda speed: card.speedLabel.setText(f"速度: {speed:.1f} km/h")
        card.update_track_length = lambda length: card.trackLengthLabel.setText(f"轨迹点: {length}")
        card.update_thumb = lambda pixmap: (card.thumbLabel.setPixmap(pixmap.scaled(58, 58, Qt.KeepAspectRatio, Qt.SmoothTransformation)), 
                                          card.thumbLabel.setText(""))
        
        # 添加状态更新方法
        def set_lost(is_lost=True):
            if is_lost:
                card.statusLabel.setText("状态: 已丢失")
                card.statusLabel.setStyleSheet("""
                    color: rgb(255, 100, 100);
                    font-size: 12px;
                    font-weight: bold;
                """)
                # 设置灰色滤镜效果
                card.setStyleSheet(f"""
                    QFrame {{
                        background-color: rgba(30, 35, 40, 180);
                        border-radius: 5px;
                        border-left: 3px solid rgba({color.red()}, {color.green()}, {color.blue()}, 100);
                        opacity: 0.7;
                    }}
                """)
            else:
                card.statusLabel.setText("状态: 跟踪中")
                card.statusLabel.setStyleSheet("""
                    color: rgb(0, 230, 150);
                    font-size: 12px;
                    font-weight: bold;
                """)
                # 恢复正常样式
                card.setStyleSheet(f"""
                    QFrame {{
                        background-color: rgba(0, 35, 70, 180);
                        border-radius: 5px;
                        border-left: 3px solid rgba({color.red()}, {color.green()}, {color.blue()}, 200);
                    }}
                """)
        
        card.set_lost = set_lost
        
        return card
        
    def updateFrame(self, frame):
        """更新视频帧显示
        
        Args:
            frame: numpy.ndarray 视频帧图像
        """
        if frame is None:
            return
            
        try:
            from PyQt5.QtGui import QImage, QPixmap
            import cv2
            import numpy as np
            
            # 确保图像是RGB格式
            if len(frame.shape) == 2:  # 灰度图
                frame = cv2.cvtColor(frame, cv2.COLOR_GRAY2RGB)
            elif frame.shape[2] == 3:  # BGR格式
                frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                
            # 获取图像尺寸
            height, width, channels = frame.shape
            bytes_per_line = channels * width
            
            # 创建QImage
            q_img = QImage(frame.data, width, height, bytes_per_line, QImage.Format_RGB888)
            
            # 根据标签大小调整图像
            pixmap = QPixmap.fromImage(q_img)
            pixmap = pixmap.scaled(self.videoLabel.width(), self.videoLabel.height(), 
                                  Qt.KeepAspectRatio, Qt.SmoothTransformation)
            
            # 显示图像
            self.videoLabel.setPixmap(pixmap)
            
        except Exception as e:
            print(f"更新视频帧出错: {str(e)}")
    
    def mockTrackingData(self):
        """生成模拟数据用于界面测试"""
        import random
        import numpy as np
        from PyQt5.QtCore import QTimer
        
        # 随机生成目标数量
        target_count = random.randint(3, 6)
        
        # 目标类别
        classes = ["小汽车", "货车", "客车", "摩托车"]
        
        # 生成测试目标数据
        targets = []
        class_counts = {"car": 0, "truck": 0, "bus": 0, "motorcycle": 0}
        
        total_speed = 0
        
        for i in range(target_count):
            # 随机目标ID
            target_id = i + 1
            
            # 随机类别
            target_class = random.choice(classes)
            class_key = "car"
            if "货车" in target_class:
                class_key = "truck"
            elif "客车" in target_class:
                class_key = "bus"
            elif "摩托" in target_class:
                class_key = "motorcycle"
            
            class_counts[class_key] += 1
            
            # 随机生成置信度
            confidence = random.uniform(0.6, 0.98)
            
            # 随机生成边界框
            box = (random.randint(10, 200), random.randint(10, 200), 
                   random.randint(100, 300), random.randint(100, 300))
            
            # 随机生成轨迹点
            track_points = [(random.randint(10, 300), random.randint(10, 300)) 
                           for _ in range(random.randint(5, 20))]
            
            # 随机生成速度
            speed = random.uniform(30, 120)
            total_speed += speed
            
            # 随机是否丢失
            is_lost = random.random() < 0.2  # 20%概率丢失
            
            # 创建测试缩略图 (纯色带ID的图像)
            thumb = np.zeros((80, 80, 3), dtype=np.uint8)
            if "小汽车" in target_class:
                color = (200, 100, 100)  # 红色调
            elif "货车" in target_class:
                color = (100, 200, 100)  # 绿色调
            elif "客车" in target_class:
                color = (100, 100, 200)  # 蓝色调
            else:
                color = (200, 200, 100)  # 黄色调
                
            thumb[:] = color
            
            # 将缩略图数据添加到目标
            target_data = {
                "id": target_id,
                "class": target_class,
                "confidence": confidence,
                "box": box,
                "track_points": track_points,
                "speed": speed,
                "thumbnail": thumb,
                "is_lost": is_lost
            }
            
            targets.append(target_data)
        
        # 计算平均速度
        avg_speed = total_speed / target_count if target_count > 0 else 0
        
        # 创建结果数据
        results = {
            "targets": targets,
            "stats": {
                "total": target_count,
                "class_counts": class_counts,
                "avg_speed": avg_speed
            },
            "frame": np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)  # 随机图像
        }
        
        # 更新界面
        self.updateTrackingResults(results)
        
        # 创建定时器，模拟数据更新 (每秒更新一次)
        self.mock_timer = QTimer()
        self.mock_timer.timeout.connect(self.mockTrackingData)
        self.mock_timer.start(1000)  # 1000ms = 1秒
