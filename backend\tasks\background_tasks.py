# -*- coding: utf-8 -*-
# @Description : 后台任务处理器
# @Date : 2025年6月20日

import time
import threading
import schedule
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
try:
    from ..utils.database import get_db_connection
    from ..services.realtime_service import RealtimeService
    from ..websocket.events import (
        broadcast_monitor_data, broadcast_alert, broadcast_system_status,
        broadcast_live_statistics, broadcast_monitor_status_change
    )
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from utils.database import get_db_connection
    from services.realtime_service import RealtimeService
    from websocket.events import (
        broadcast_monitor_data, broadcast_alert, broadcast_system_status,
        broadcast_live_statistics, broadcast_monitor_status_change
    )

class BackgroundTaskManager:
    """后台任务管理器"""
    
    def __init__(self, socketio=None):
        self.socketio = socketio
        self.realtime_service = RealtimeService()
        self.running = False
        self.threads = []
        
        # 任务配置
        self.task_intervals = {
            'system_status': 30,  # 系统状态更新间隔（秒）
            'monitor_health': 60,  # 监控点健康检查间隔（秒）
            'live_statistics': 10,  # 实时统计更新间隔（秒）
            'cleanup': 3600,  # 数据清理间隔（秒）
        }
    
    def start(self):
        """启动后台任务"""
        if self.running:
            return
        
        self.running = True
        print("启动后台任务管理器...")
        
        # 启动各种后台任务
        self._start_system_status_task()
        self._start_monitor_health_task()
        self._start_live_statistics_task()
        self._start_cleanup_task()
        self._start_scheduler()
        
        print("后台任务管理器启动完成")
    
    def stop(self):
        """停止后台任务"""
        self.running = False
        print("停止后台任务管理器...")
        
        # 等待所有线程结束
        for thread in self.threads:
            if thread.is_alive():
                thread.join(timeout=5)
        
        print("后台任务管理器已停止")
    
    def _start_system_status_task(self):
        """启动系统状态监控任务"""
        def system_status_worker():
            while self.running:
                try:
                    # 获取系统状态
                    status = self.realtime_service.get_system_status()
                    
                    # 广播系统状态
                    if self.socketio:
                        broadcast_system_status(self.socketio, status)
                    
                    # 检查系统健康状态
                    self._check_system_health(status)
                    
                except Exception as e:
                    print(f"系统状态任务错误: {str(e)}")
                
                time.sleep(self.task_intervals['system_status'])
        
        thread = threading.Thread(target=system_status_worker, daemon=True)
        thread.start()
        self.threads.append(thread)
    
    def _start_monitor_health_task(self):
        """启动监控点健康检查任务"""
        def monitor_health_worker():
            while self.running:
                try:
                    # 获取所有监控点
                    with get_db_connection() as db:
                        monitors = db.get_list("""
                            SELECT id, url, connection_status, is_alarm
                            FROM monitor
                            WHERE is_alarm = '开启'
                        """)
                    
                    # 检查每个监控点的健康状态
                    for monitor in monitors:
                        self._check_monitor_health(monitor)
                    
                except Exception as e:
                    print(f"监控点健康检查任务错误: {str(e)}")
                
                time.sleep(self.task_intervals['monitor_health'])
        
        thread = threading.Thread(target=monitor_health_worker, daemon=True)
        thread.start()
        self.threads.append(thread)
    
    def _start_live_statistics_task(self):
        """启动实时统计任务"""
        def live_statistics_worker():
            while self.running:
                try:
                    # 获取实时统计数据
                    stats = self.realtime_service.get_live_statistics()
                    
                    # 广播实时统计
                    if self.socketio:
                        broadcast_live_statistics(self.socketio, stats)
                    
                except Exception as e:
                    print(f"实时统计任务错误: {str(e)}")
                
                time.sleep(self.task_intervals['live_statistics'])
        
        thread = threading.Thread(target=live_statistics_worker, daemon=True)
        thread.start()
        self.threads.append(thread)
    
    def _start_cleanup_task(self):
        """启动数据清理任务"""
        def cleanup_worker():
            while self.running:
                try:
                    self._cleanup_old_data()
                except Exception as e:
                    print(f"数据清理任务错误: {str(e)}")
                
                time.sleep(self.task_intervals['cleanup'])
        
        thread = threading.Thread(target=cleanup_worker, daemon=True)
        thread.start()
        self.threads.append(thread)
    
    def _start_scheduler(self):
        """启动定时任务调度器"""
        def scheduler_worker():
            # 设置定时任务
            schedule.every().hour.do(self._hourly_statistics)
            schedule.every().day.at("02:00").do(self._daily_maintenance)
            schedule.every().week.do(self._weekly_report)
            
            while self.running:
                try:
                    schedule.run_pending()
                    time.sleep(60)  # 每分钟检查一次
                except Exception as e:
                    print(f"定时任务调度器错误: {str(e)}")
        
        thread = threading.Thread(target=scheduler_worker, daemon=True)
        thread.start()
        self.threads.append(thread)
    
    def _check_system_health(self, status: Dict[str, Any]):
        """检查系统健康状态"""
        try:
            # 检查系统负载
            system_load = status.get('system', {})
            cpu_percent = system_load.get('cpu_percent', 0)
            memory_percent = system_load.get('memory_percent', 0)
            
            # 如果系统负载过高，发送警报
            if cpu_percent > 90 or memory_percent > 90:
                alert_data = {
                    'type': 'system_overload',
                    'message': f'系统负载过高 - CPU: {cpu_percent}%, 内存: {memory_percent}%',
                    'severity': 'high',
                    'timestamp': datetime.now().isoformat()
                }
                
                if self.socketio:
                    broadcast_alert(self.socketio, alert_data)
        except Exception as e:
            print(f"系统健康检查错误: {str(e)}")
    
    def _check_monitor_health(self, monitor: Dict[str, Any]):
        """检查监控点健康状态"""
        try:
            monitor_id = monitor['id']
            current_status = monitor['connection_status']
            
            # 测试连接
            from ..utils.rtsp_utils import RTSPUtils
            rtsp_utils = RTSPUtils()
            is_connected = rtsp_utils.test_connection(monitor['url'])
            
            new_status = 'connected' if is_connected else 'disconnected'
            
            # 如果状态发生变化，更新数据库并广播
            if new_status != current_status:
                with get_db_connection() as db:
                    db.modify("""
                        UPDATE monitor 
                        SET connection_status = %s 
                        WHERE id = %s
                    """, (new_status, monitor_id))
                
                # 广播状态变化
                status_change = {
                    'old_status': current_status,
                    'new_status': new_status,
                    'change_time': datetime.now().isoformat()
                }
                
                if self.socketio:
                    broadcast_monitor_status_change(self.socketio, monitor_id, status_change)
                
                # 如果连接断开，发送警报
                if new_status == 'disconnected':
                    alert_data = {
                        'type': 'monitor_disconnected',
                        'monitor_id': monitor_id,
                        'message': f'监控点 {monitor_id} 连接断开',
                        'severity': 'medium',
                        'timestamp': datetime.now().isoformat()
                    }
                    
                    if self.socketio:
                        broadcast_alert(self.socketio, alert_data)
        except Exception as e:
            print(f"监控点健康检查错误: {str(e)}")
    
    def _cleanup_old_data(self):
        """清理旧数据"""
        try:
            with get_db_connection() as db:
                # 清理过期的实时数据
                db.modify("""
                    DELETE FROM realtime_data 
                    WHERE expire_time < NOW() OR timestamp < %s
                """, (datetime.now() - timedelta(hours=24),))
                
                # 清理旧的系统日志
                db.modify("""
                    DELETE FROM system_log 
                    WHERE create_time < %s
                """, (datetime.now() - timedelta(days=30),))
                
                print("数据清理完成")
        except Exception as e:
            print(f"数据清理错误: {str(e)}")
    
    def _hourly_statistics(self):
        """每小时统计任务"""
        try:
            current_time = datetime.now()
            hour = current_time.hour
            date = current_time.date()
            
            with get_db_connection() as db:
                # 获取所有活跃监控点
                monitors = db.get_list("""
                    SELECT id FROM monitor WHERE is_alarm = '开启'
                """)
                
                for monitor in monitors:
                    monitor_id = monitor['id']
                    
                    # 计算该小时的统计数据
                    hour_start = current_time.replace(minute=0, second=0, microsecond=0)
                    hour_end = hour_start + timedelta(hours=1)
                    
                    stats = db.get_one("""
                        SELECT COUNT(*) as alarm_count,
                               AVG(vehicle_count) as avg_vehicles,
                               MAX(vehicle_count) as max_vehicles,
                               MIN(vehicle_count) as min_vehicles,
                               AVG(confidence_level) as avg_confidence
                        FROM alarm
                        WHERE pid = %s AND create_time >= %s AND create_time < %s
                    """, (monitor_id, hour_start, hour_end))
                    
                    # 插入或更新统计数据
                    db.modify("""
                        INSERT INTO traffic_statistics 
                        (monitor_id, stat_date, stat_hour, vehicle_count, avg_confidence, 
                         max_confidence, min_confidence, detection_count, alert_count)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                        ON DUPLICATE KEY UPDATE
                        vehicle_count = VALUES(vehicle_count),
                        avg_confidence = VALUES(avg_confidence),
                        max_confidence = VALUES(max_confidence),
                        min_confidence = VALUES(min_confidence),
                        detection_count = VALUES(detection_count),
                        alert_count = VALUES(alert_count),
                        update_time = NOW()
                    """, (
                        monitor_id, date, hour,
                        int(stats['avg_vehicles'] or 0),
                        stats['avg_confidence'] or 0,
                        stats['max_confidence'] or 0,
                        stats['min_confidence'] or 0,
                        stats['alarm_count'] or 0,
                        stats['alarm_count'] or 0
                    ))
            
            print(f"完成 {current_time.strftime('%Y-%m-%d %H:00')} 小时统计")
        except Exception as e:
            print(f"小时统计任务错误: {str(e)}")
    
    def _daily_maintenance(self):
        """每日维护任务"""
        try:
            print("开始每日维护任务...")
            
            # 数据库优化
            with get_db_connection() as db:
                # 优化表
                tables = ['alarm', 'monitor', 'detection_result', 'system_log']
                for table in tables:
                    try:
                        db.execute(f"OPTIMIZE TABLE {table}")
                    except Exception as e:
                        print(f"优化表 {table} 失败: {str(e)}")
                
                # 更新统计信息
                db.execute("ANALYZE TABLE alarm, monitor, detection_result")
            
            # 清理临时文件
            import os
            import shutil
            temp_dirs = ['temp', 'uploads/temp']
            for temp_dir in temp_dirs:
                if os.path.exists(temp_dir):
                    try:
                        shutil.rmtree(temp_dir)
                        os.makedirs(temp_dir, exist_ok=True)
                    except Exception as e:
                        print(f"清理临时目录 {temp_dir} 失败: {str(e)}")
            
            print("每日维护任务完成")
        except Exception as e:
            print(f"每日维护任务错误: {str(e)}")
    
    def _weekly_report(self):
        """每周报告任务"""
        try:
            print("生成每周报告...")
            
            # 这里可以添加生成周报的逻辑
            # 例如：发送邮件报告、生成PDF报告等
            
            print("每周报告生成完成")
        except Exception as e:
            print(f"每周报告任务错误: {str(e)}")
    
    def add_custom_task(self, task_func, interval: int, name: str = None):
        """添加自定义任务"""
        def custom_worker():
            while self.running:
                try:
                    task_func()
                except Exception as e:
                    print(f"自定义任务 {name} 错误: {str(e)}")
                
                time.sleep(interval)
        
        thread = threading.Thread(target=custom_worker, daemon=True)
        thread.start()
        self.threads.append(thread)
        
        if name:
            print(f"添加自定义任务: {name}")
    
    def trigger_immediate_cleanup(self):
        """立即触发数据清理"""
        try:
            self._cleanup_old_data()
            return True
        except Exception as e:
            print(f"立即清理失败: {str(e)}")
            return False
    
    def get_task_status(self) -> Dict[str, Any]:
        """获取任务状态"""
        return {
            'running': self.running,
            'thread_count': len(self.threads),
            'active_threads': sum(1 for t in self.threads if t.is_alive()),
            'task_intervals': self.task_intervals,
            'last_check': datetime.now().isoformat()
        }

# 全局任务管理器实例
task_manager = None

def init_background_tasks(socketio):
    """初始化后台任务"""
    global task_manager
    if task_manager is None:
        task_manager = BackgroundTaskManager(socketio)
        task_manager.start()
    return task_manager

def get_task_manager():
    """获取任务管理器实例"""
    return task_manager

def stop_background_tasks():
    """停止后台任务"""
    global task_manager
    if task_manager:
        task_manager.stop()
        task_manager = None
