#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库表结构和字段
"""

import pymysql
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(override=True, dotenv_path='config/end-back.env')

def check_database_structure():
    """检查数据库结构"""
    print("🔍 检查数据库表结构")
    print("=" * 60)
    
    config = {
        'host': '127.0.0.1',
        'port': 3306,
        'user': 'root',
        'password': '123456',
        'database': 'yolo',
        'charset': 'utf8mb4',
        'autocommit': True,
        'cursorclass': pymysql.cursors.DictCursor
    }
    
    try:
        connection = pymysql.connect(**config)
        with connection.cursor() as cursor:
            
            # 1. 检查所有表
            print("1. 检查所有表:")
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            table_names = [list(table.values())[0] for table in tables]
            print(f"   表数量: {len(table_names)}")
            print(f"   表列表: {', '.join(table_names)}")
            
            # 2. 检查user表结构
            if 'user' in table_names:
                print("\n2. 检查user表结构:")
                cursor.execute("DESCRIBE user")
                user_columns = cursor.fetchall()
                print("   字段列表:")
                for col in user_columns:
                    print(f"     {col['Field']} - {col['Type']} - {col['Null']} - {col['Key']} - {col['Default']}")
                
                # 3. 检查user表数据
                print("\n3. 检查user表数据:")
                cursor.execute("SELECT * FROM user LIMIT 5")
                users = cursor.fetchall()
                print(f"   用户数量: {len(users)}")
                for user in users:
                    print(f"   用户: {user}")
                
                # 4. 测试登录查询
                print("\n4. 测试登录查询:")
                cursor.execute("SELECT * FROM user WHERE username=%s AND password=%s", ("admin", "123456"))
                login_result = cursor.fetchone()
                if login_result:
                    print(f"   ✅ 登录查询成功: {login_result}")
                else:
                    print("   ❌ 登录查询失败，用户不存在或密码错误")
                    
                    # 检查admin用户
                    cursor.execute("SELECT * FROM user WHERE username=%s", ("admin",))
                    admin_user = cursor.fetchone()
                    if admin_user:
                        print(f"   admin用户存在: {admin_user}")
                        print(f"   存储的密码: '{admin_user['password']}'")
                        print(f"   输入的密码: '123456'")
                        print(f"   密码匹配: {admin_user['password'] == '123456'}")
                    else:
                        print("   admin用户不存在")
            else:
                print("\n❌ user表不存在!")
            
            # 5. 检查其他重要表
            important_tables = ['monitor', 'alarm', 'detection_task', 'tracking_target']
            print(f"\n5. 检查其他重要表:")
            for table in important_tables:
                if table in table_names:
                    cursor.execute(f"SELECT COUNT(*) as count FROM {table}")
                    count = cursor.fetchone()['count']
                    print(f"   ✅ {table}: {count} 条记录")
                else:
                    print(f"   ❌ {table}: 表不存在")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_login():
    """直接测试登录逻辑"""
    print("\n" + "=" * 60)
    print("🧪 直接测试登录逻辑")
    print("=" * 60)
    
    config = {
        'host': '127.0.0.1',
        'port': 3306,
        'user': 'root',
        'password': '123456',
        'database': 'yolo',
        'charset': 'utf8mb4',
        'autocommit': True,
        'cursorclass': pymysql.cursors.DictCursor
    }
    
    try:
        print("1. 连接数据库...")
        connection = pymysql.connect(**config)
        print("   ✅ 数据库连接成功")
        
        print("2. 创建游标...")
        cursor = connection.cursor()
        print("   ✅ 游标创建成功")
        
        print("3. 执行登录查询...")
        sql = "SELECT * FROM user WHERE username=%s AND password=%s"
        params = ("admin", "123456")
        print(f"   SQL: {sql}")
        print(f"   参数: {params}")
        
        cursor.execute(sql, params)
        print("   ✅ SQL执行成功")
        
        print("4. 获取查询结果...")
        result = cursor.fetchone()
        print(f"   查询结果: {result}")
        
        if result:
            print("   ✅ 登录验证成功")
            print(f"   用户ID: {result['id']}")
            print(f"   用户名: {result['username']}")
            print(f"   邮箱: {result['email']}")
            print(f"   角色: {result['grade']}")
        else:
            print("   ❌ 登录验证失败")
        
        cursor.close()
        connection.close()
        print("5. 连接关闭")
        
        return result is not None
        
    except Exception as e:
        print(f"❌ 直接登录测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 80)
    print("🚀 数据库结构和登录测试")
    print("=" * 80)
    
    # 1. 检查数据库结构
    structure_ok = check_database_structure()
    
    # 2. 直接测试登录
    if structure_ok:
        login_ok = test_direct_login()
        
        print("\n" + "=" * 80)
        print("📋 测试结果:")
        print(f"   数据库结构: {'✅ 正常' if structure_ok else '❌ 异常'}")
        print(f"   登录测试: {'✅ 成功' if login_ok else '❌ 失败'}")
        
        if structure_ok and login_ok:
            print("\n🎉 数据库和登录逻辑都正常!")
            print("💡 问题可能在于:")
            print("   1. Flask应用的路由注册")
            print("   2. 请求处理流程")
            print("   3. 异常处理机制")
        else:
            print("\n⚠️ 发现问题，请根据上述信息修复")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
