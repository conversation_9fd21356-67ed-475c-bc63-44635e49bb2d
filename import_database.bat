@echo off
chcp 65001 >nul
echo ================================================================================
echo 基于Yolov8与ByteTrack的高速公路智慧监控平台 - 数据库导入工具
echo ================================================================================

echo.
echo 🔧 正在重置数据库...

REM 检查MySQL服务
sc query mysql | findstr "RUNNING" >nul 2>&1
if errorlevel 1 (
    echo ❌ MySQL服务未启动，正在启动...
    net start mysql
    if errorlevel 1 (
        echo ❌ MySQL服务启动失败，请手动启动MySQL服务
        pause
        exit /b 1
    )
)

echo ✅ MySQL服务正在运行

echo.
echo 📋 请输入MySQL连接信息:
set /p MYSQL_USER="MySQL用户名 (默认: root): "
if "%MYSQL_USER%"=="" set MYSQL_USER=root

set /p MYSQL_PASSWORD="MySQL密码: "
if "%MYSQL_PASSWORD%"=="" (
    echo ❌ 密码不能为空
    pause
    exit /b 1
)

echo.
echo 🗑️  正在删除旧数据库...
mysql -u %MYSQL_USER% -p%MYSQL_PASSWORD% -e "DROP DATABASE IF EXISTS yolo;"
if errorlevel 1 (
    echo ❌ 删除数据库失败，请检查用户名和密码
    pause
    exit /b 1
)

echo ✅ 旧数据库已删除

echo.
echo 🏗️  正在导入新数据库结构和数据...
mysql -u %MYSQL_USER% -p%MYSQL_PASSWORD% < yolo_complete.sql
if errorlevel 1 (
    echo ❌ 数据库导入失败
    pause
    exit /b 1
)

echo ✅ 数据库导入成功

echo.
echo 🧪 正在验证数据库...
mysql -u %MYSQL_USER% -p%MYSQL_PASSWORD% -e "USE yolo; SELECT 'Tables:' as info, COUNT(*) as count FROM information_schema.tables WHERE table_schema='yolo' UNION ALL SELECT 'Users:', COUNT(*) FROM user UNION ALL SELECT 'Monitors:', COUNT(*) FROM monitor UNION ALL SELECT 'Alarms:', COUNT(*) FROM alarm UNION ALL SELECT 'Configs:', COUNT(*) FROM system_config;"

echo.
echo ================================================================================
echo 🎉 数据库导入完成！
echo ================================================================================

echo.
echo 📊 数据库包含:
echo   👥 用户表: 3个用户 (admin, operator, viewer)
echo   📹 监控点表: 15个监控点 (杭州至千岛湖全程)
echo   🚨 警报表: 10条警报记录
echo   ⚙️  系统配置表: 10项配置
echo   📝 其他表: 日志、统计、任务等

echo.
echo 🔑 默认登录信息:
echo   管理员: admin / 123456
echo   操作员: operator / operator
echo   观察员: viewer / hello

echo.
echo 🚀 下一步操作:
echo   1. 启动系统: python start_server.py
echo   2. 访问地址: http://127.0.0.1:5500
echo   3. 使用admin/123456登录

echo.
echo 按任意键退出...
pause >nul
