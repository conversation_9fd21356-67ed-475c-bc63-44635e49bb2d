# -*- coding: utf-8 -*-
# @Description : 基于Yolov8与ByteTrack的高速公路智慧监控平台 - 增强版Flask后端
# @Date : 2025年6月20日

import os
import sys
import logging
from datetime import datetime
from flask import Flask, request, jsonify
from flask_cors import CORS
from flask_socketio import Socket<PERSON>
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入自定义模块
from api import api_v1
from utils.database import init_database, health_check as db_health_check
from utils.response import error_response, server_error_response
from services.realtime_service import RealtimeService

# 加载环境变量
load_dotenv(override=True, dotenv_path='config/end-back.env')

def create_app():
    """创建Flask应用"""
    app = Flask(__name__, static_folder='../static')
    
    # 应用配置
    app.config.update({
        'SECRET_KEY': os.getenv('SECRET_KEY', 'yolo-highway-monitoring-system-2025'),
        'JSON_AS_ASCII': False,
        'JSONIFY_PRETTYPRINT_REGULAR': True,
        'MAX_CONTENT_LENGTH': 100 * 1024 * 1024,  # 100MB
        'UPLOAD_FOLDER': 'uploads',
        'STATIC_FOLDER': '../static'
    })
    
    # 跨域支持
    CORS(app, supports_credentials=True, origins="*")
    
    # WebSocket支持
    socketio = SocketIO(
        app, 
        cors_allowed_origins="*", 
        async_mode='threading',
        logger=True,
        engineio_logger=True
    )
    
    # 配置日志
    setup_logging(app)
    
    # 注册蓝图
    app.register_blueprint(api_v1)
    
    # 注册错误处理器
    register_error_handlers(app)
    
    # 注册请求钩子
    register_request_hooks(app)
    
    # 初始化数据库
    init_database()
    
    # 创建必要的目录
    create_directories()
    
    return app, socketio

def setup_logging(app):
    """设置日志"""
    # 确保日志目录存在
    log_dir = 'logs'
    os.makedirs(log_dir, exist_ok=True)
    
    # 配置日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 文件处理器
    file_handler = logging.FileHandler(
        os.path.join(log_dir, 'app.log'),
        encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    file_handler.setLevel(logging.INFO)
    
    # 错误文件处理器
    error_handler = logging.FileHandler(
        os.path.join(log_dir, 'error.log'),
        encoding='utf-8'
    )
    error_handler.setFormatter(formatter)
    error_handler.setLevel(logging.ERROR)
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    console_handler.setLevel(logging.INFO)
    
    # 配置应用日志
    app.logger.addHandler(file_handler)
    app.logger.addHandler(error_handler)
    app.logger.addHandler(console_handler)
    app.logger.setLevel(logging.INFO)
    
    # 配置Werkzeug日志
    werkzeug_logger = logging.getLogger('werkzeug')
    werkzeug_logger.addHandler(file_handler)
    werkzeug_logger.setLevel(logging.INFO)

def register_error_handlers(app):
    """注册错误处理器"""
    
    @app.errorhandler(400)
    def bad_request(error):
        return error_response('请求参数错误', 400)
    
    @app.errorhandler(401)
    def unauthorized(error):
        return error_response('未授权访问', 401)
    
    @app.errorhandler(403)
    def forbidden(error):
        return error_response('权限不足', 403)
    
    @app.errorhandler(404)
    def not_found(error):
        return error_response('资源不存在', 404)
    
    @app.errorhandler(405)
    def method_not_allowed(error):
        return error_response('请求方法不允许', 405)
    
    @app.errorhandler(413)
    def request_entity_too_large(error):
        return error_response('请求实体过大', 413)
    
    @app.errorhandler(429)
    def too_many_requests(error):
        return error_response('请求过于频繁', 429)
    
    @app.errorhandler(500)
    def internal_server_error(error):
        app.logger.error(f'服务器内部错误: {str(error)}')
        return server_error_response('服务器内部错误')
    
    @app.errorhandler(Exception)
    def handle_exception(error):
        app.logger.error(f'未处理的异常: {str(error)}', exc_info=True)
        return server_error_response('服务器发生未知错误')

def register_request_hooks(app):
    """注册请求钩子"""
    
    @app.before_request
    def before_request():
        """请求前处理"""
        # 记录请求信息
        app.logger.info(f'{request.method} {request.url} - {request.remote_addr}')
        
        # 设置请求开始时间
        request.start_time = datetime.now()
    
    @app.after_request
    def after_request(response):
        """请求后处理"""
        # 计算请求处理时间
        if hasattr(request, 'start_time'):
            duration = (datetime.now() - request.start_time).total_seconds()
            response.headers['X-Response-Time'] = f'{duration:.3f}s'
        
        # 添加安全头
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        
        # 记录响应信息
        app.logger.info(f'{request.method} {request.url} - {response.status_code}')
        
        return response

def create_directories():
    """创建必要的目录"""
    directories = [
        'uploads/images',
        'uploads/videos',
        'uploads/models',
        'static/images/original',
        'static/images/detection',
        'static/images/result',
        'static/after_img',
        'static/before_img',
        'results',
        'backups',
        'logs',
        'exports',
        'temp'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)

# 创建应用实例
app, socketio = create_app()

# 添加根路由
@app.route('/')
def index():
    """首页"""
    return jsonify({
        'message': '欢迎使用基于Yolov8与ByteTrack的高速公路智慧监控平台！',
        'version': '1.0.0',
        'status': 'running',
        'timestamp': datetime.now().isoformat(),
        'api_docs': '/api/v1/docs',
        'health_check': '/api/v1/system/health-check'
    })

# 添加健康检查端点
@app.route('/health')
def health():
    """健康检查"""
    try:
        # 检查数据库连接
        db_status = db_health_check()
        
        # 检查系统资源
        import psutil
        system_status = {
            'cpu_percent': psutil.cpu_percent(),
            'memory_percent': psutil.virtual_memory().percent,
            'disk_percent': psutil.disk_usage('/').percent
        }
        
        overall_status = 'healthy'
        if (system_status['cpu_percent'] > 90 or 
            system_status['memory_percent'] > 90 or 
            system_status['disk_percent'] > 90):
            overall_status = 'warning'
        
        if db_status.get('status') != 'healthy':
            overall_status = 'unhealthy'
        
        return jsonify({
            'status': overall_status,
            'timestamp': datetime.now().isoformat(),
            'database': db_status,
            'system': system_status,
            'uptime': psutil.boot_time()
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/v1/docs')
def api_docs():
    """API文档"""
    return jsonify({
        'title': '基于Yolov8与ByteTrack的高速公路智慧监控平台 API文档',
        'version': '1.0.0',
        'description': '基于Yolov8与ByteTrack的高速公路智慧监控平台RESTful API',
        'base_url': request.host_url + 'api/v1',
        'endpoints': {
            'authentication': {
                'login': 'POST /auth/login',
                'logout': 'POST /auth/logout',
                'register': 'POST /auth/register',
                'profile': 'GET /auth/profile'
            },
            'monitor_management': {
                'list_monitors': 'GET /monitor/list',
                'get_monitor': 'GET /monitor/{id}',
                'create_monitor': 'POST /monitor',
                'update_monitor': 'PUT /monitor/{id}',
                'delete_monitor': 'DELETE /monitor/{id}',
                'test_connection': 'POST /monitor/{id}/test-connection'
            },
            'detection': {
                'detect_image': 'POST /detection/image',
                'detect_base64': 'POST /detection/base64',
                'detect_video': 'POST /detection/video',
                'detect_rtsp': 'POST /detection/rtsp',
                'get_tasks': 'GET /detection/tasks'
            },
            'analysis': {
                'get_alarms': 'GET /analysis/alarms',
                'get_statistics': 'GET /analysis/statistics/overview',
                'get_heatmap': 'GET /analysis/heatmap',
                'export_data': 'POST /analysis/export/alarms'
            },
            'realtime': {
                'get_status': 'GET /realtime/status',
                'get_active_monitors': 'GET /realtime/monitors/active',
                'get_live_stats': 'GET /realtime/statistics/live'
            },
            'system': {
                'get_info': 'GET /system/info',
                'get_performance': 'GET /system/performance',
                'get_logs': 'GET /system/logs',
                'health_check': 'GET /system/health-check'
            }
        },
        'websocket': {
            'endpoint': '/socket.io',
            'events': {
                'connect': '连接到WebSocket',
                'join_monitor': '加入监控点房间',
                'join_alerts': '加入警报房间',
                'monitor_data': '监控点数据推送',
                'new_alert': '新警报推送'
            }
        }
    })

# WebSocket事件处理
@socketio.on('connect')
def handle_connect():
    """客户端连接"""
    app.logger.info(f'WebSocket客户端连接: {request.sid}')

@socketio.on('disconnect')
def handle_disconnect():
    """客户端断开连接"""
    app.logger.info(f'WebSocket客户端断开: {request.sid}')

if __name__ == '__main__':
    # 获取配置
    host = os.getenv('HOST_NAME', '127.0.0.1')
    port = int(os.getenv('PORT', 5500))
    debug = os.getenv('DEBUG', 'False').lower() == 'true'
    
    app.logger.info(f'启动基于Yolov8与ByteTrack的高速公路智慧监控平台后端服务')
    app.logger.info(f'服务地址: http://{host}:{port}')
    app.logger.info(f'调试模式: {debug}')
    
    # 启动应用
    socketio.run(
        app,
        host=host,
        port=port,
        debug=debug,
        allow_unsafe_werkzeug=True
    )
