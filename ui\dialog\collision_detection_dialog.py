# -*- coding: utf-8 -*-
# @Description : 车辆碰撞检测对话框
# @Date : 2025年6月21日

import sys
import cv2
import numpy as np
import math
from datetime import datetime
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                               QPushButton, QFrame, QGridLayout, QScrollArea,
                               QWidget, QSpinBox, QDoubleSpinBox, QCheckBox,
                               QGroupBox, QTextEdit, QProgressBar, QSlider,
                               QComboBox, QListWidget, QListWidgetItem)
from PySide6.QtCore import Qt, Signal, QTimer, QThread, QPoint
from PySide6.QtGui import QPixmap, QFont, QIcon, QPalette, QColor
from PySide6.QtWidgets import QGraphicsDropShadowEffect

class CollisionDetectionAlgorithm:
    """碰撞检测算法类"""
    
    def __init__(self):
        self.collision_threshold = 80  # 碰撞距离阈值(像素) - 增加阈值提高精度
        self.speed_threshold = 15      # 速度变化阈值 - 降低阈值减少误报
        self.time_threshold = 0.3      # 时间阈值(秒) - 缩短时间窗口
        self.collision_history = []    # 碰撞历史记录
        self.object_history = {}       # 对象历史轨迹
        self.frame_count = 0           # 帧计数器
        self.collision_cooldown = {}   # 碰撞冷却时间，避免重复检测
        
    def detect_collision(self, tracked_objects, frame_time):
        """检测车辆碰撞"""
        collisions = []
        self.frame_count += 1
        
        if len(tracked_objects) < 2:
            return collisions
        
        # 更新对象历史轨迹
        self._update_object_history(tracked_objects)
        
        # 清理过期的冷却时间
        current_time = frame_time.timestamp()
        expired_keys = [key for key, time_val in self.collision_cooldown.items() 
                       if current_time - time_val > 3.0]  # 3秒冷却期
        for key in expired_keys:
            del self.collision_cooldown[key]
            
        # 遍历所有车辆对
        for i, obj1 in enumerate(tracked_objects):
            for j, obj2 in enumerate(tracked_objects[i+1:], i+1):
                collision = self._check_collision_between_objects(obj1, obj2, frame_time)
                if collision:
                    collisions.append(collision)
                    
        return collisions
    
    def _update_object_history(self, tracked_objects):
        """更新对象历史轨迹"""
        for obj in tracked_objects:
            obj_id = obj.get('track_id', 'unknown')
            center = self._get_center(obj['bbox'])
            
            if obj_id not in self.object_history:
                self.object_history[obj_id] = []
            
            self.object_history[obj_id].append({
                'center': center,
                'frame': self.frame_count,
                'bbox': obj['bbox']
            })
            
            # 只保留最近10帧的历史
            if len(self.object_history[obj_id]) > 10:
                self.object_history[obj_id] = self.object_history[obj_id][-10:]
    
    def _calculate_bbox_overlap(self, bbox1, bbox2):
        """计算两个边界框的重叠率"""
        x1_min, y1_min, x1_max, y1_max = bbox1
        x2_min, y2_min, x2_max, y2_max = bbox2
        
        # 计算重叠区域
        overlap_x_min = max(x1_min, x2_min)
        overlap_y_min = max(y1_min, y2_min)
        overlap_x_max = min(x1_max, x2_max)
        overlap_y_max = min(y1_max, y2_max)
        
        if overlap_x_min >= overlap_x_max or overlap_y_min >= overlap_y_max:
            return 0.0
        
        overlap_area = (overlap_x_max - overlap_x_min) * (overlap_y_max - overlap_y_min)
        area1 = (x1_max - x1_min) * (y1_max - y1_min)
        area2 = (x2_max - x2_min) * (y2_max - y2_min)
        
        return overlap_area / min(area1, area2)
    
    def _get_object_velocity(self, obj_id):
        """获取对象速度"""
        if obj_id not in self.object_history or len(self.object_history[obj_id]) < 2:
            return (0, 0)
        
        history = self.object_history[obj_id]
        current = history[-1]['center']
        previous = history[-2]['center']
        
        velocity_x = current[0] - previous[0]
        velocity_y = current[1] - previous[1]
        
        return (velocity_x, velocity_y)
    
    def _check_collision_between_objects(self, obj1, obj2, frame_time):
        """检查两个对象之间的碰撞"""
        obj1_id = obj1.get('track_id', 'unknown')
        obj2_id = obj2.get('track_id', 'unknown')
        collision_key = f"{min(obj1_id, obj2_id)}_{max(obj1_id, obj2_id)}"
        
        # 检查冷却时间
        current_time = frame_time.timestamp()
        if collision_key in self.collision_cooldown:
            return None
        
        # 获取边界框中心点
        center1 = self._get_center(obj1['bbox'])
        center2 = self._get_center(obj2['bbox'])
        
        # 计算边界框重叠
        overlap = self._calculate_bbox_overlap(obj1['bbox'], obj2['bbox'])
        
        # 计算距离
        distance = self._calculate_distance(center1, center2)
        
        # 改进的碰撞检测逻辑
        if overlap > 0.1 or distance < self.collision_threshold:
            # 计算相对速度和运动趋势
            velocity1 = self._get_object_velocity(obj1_id)
            velocity2 = self._get_object_velocity(obj2_id)
            
            # 计算相对速度
            relative_velocity = ((velocity1[0] - velocity2[0])**2 + 
                               (velocity1[1] - velocity2[1])**2)**0.5
            
            # 检查是否真正碰撞（相对速度和重叠度）
            if relative_velocity > self.speed_threshold or overlap > 0.2:
                severity = self._calculate_severity(distance, relative_velocity, overlap)
                
                collision = {
                    'id1': obj1_id,
                    'id2': obj2_id,
                    'position': [(center1[0] + center2[0]) / 2, (center1[1] + center2[1]) / 2],
                    'distance': distance,
                    'speed_change1': relative_velocity,
                    'speed_change2': overlap,
                    'time': frame_time,
                    'severity': severity
                }
                
                # 添加到冷却列表
                self.collision_cooldown[collision_key] = current_time
                self.collision_history.append(collision)
                return collision
                
        return None
    
    def _get_center(self, bbox):
        """获取边界框中心点"""
        return [(bbox[0] + bbox[2]) / 2, (bbox[1] + bbox[3]) / 2]
    
    def _calculate_distance(self, point1, point2):
        """计算两点间距离"""
        return math.sqrt((point1[0] - point2[0])**2 + (point1[1] - point2[1])**2)
    
    def _get_speed_change(self, obj):
        """获取速度变化（简化实现）"""
        # 这里应该基于历史位置计算速度变化
        # 简化实现，返回随机值用于演示
        return np.random.uniform(0, 30)
    
    def _calculate_severity(self, distance, relative_velocity, overlap):
        """计算碰撞严重程度"""
        # 综合考虑距离、相对速度和重叠度
        severity_score = 0
        
        # 距离因子
        if distance < 20:
            severity_score += 3
        elif distance < 40:
            severity_score += 2
        else:
            severity_score += 1
        
        # 相对速度因子
        if relative_velocity > 25:
            severity_score += 3
        elif relative_velocity > 15:
            severity_score += 2
        else:
            severity_score += 1
        
        # 重叠度因子
        if overlap > 0.5:
            severity_score += 3
        elif overlap > 0.2:
            severity_score += 2
        else:
            severity_score += 1
        
        # 根据总分判断严重程度
        if severity_score >= 7:
            return "严重"
        elif severity_score >= 5:
            return "中等"
        else:
            return "轻微"

class CollisionDetectionDialog(QDialog):
    """车辆碰撞检测对话框"""
    
    # 信号定义
    detection_started = Signal()
    detection_stopped = Signal()
    collision_detected = Signal(dict)  # 发送碰撞信息
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.detection_algorithm = CollisionDetectionAlgorithm()
        self.is_detecting = False
        self.collision_count = 0
        self.drag_pos = QPoint()
        
        self.init_ui()
        self.setup_connections()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("车辆碰撞检测系统")
        self.setFixedSize(900, 700)
        self.setModal(True)

        # 设置窗口无边框和透明背景
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.Dialog)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # 设置现代化浅色透明样式
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.5 rgba(248, 250, 252, 0.98),
                    stop:1 rgba(241, 245, 249, 0.95));
                border-radius: 20px;
                border: 1px solid rgba(203, 213, 225, 0.6);
            }
            QLabel {
                color: rgb(51, 65, 85);
                font-family: 'Microsoft YaHei UI', 'PingFang SC', sans-serif;
                font-size: 14px;
                background: transparent;
            }
            QGroupBox {
                color: rgb(71, 85, 105);
                font-weight: 600;
                font-size: 15px;
                border: 1px solid rgba(203, 213, 225, 0.8);
                border-radius: 12px;
                margin-top: 12px;
                padding-top: 15px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.6),
                    stop:1 rgba(248, 250, 252, 0.4));
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                background: rgba(255, 255, 255, 0.9);
                border-radius: 6px;
                color: rgb(59, 130, 246);
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgb(59, 130, 246),
                    stop:1 rgb(37, 99, 235));
                border: none;
                border-radius: 8px;
                color: white;
                font-size: 13px;
                font-weight: 500;
                padding: 8px 16px;
                min-width: 80px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgb(37, 99, 235),
                    stop:1 rgb(29, 78, 216));
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                background: rgb(29, 78, 216);
                transform: translateY(0px);
            }
            QPushButton:disabled {
                background: rgba(148, 163, 184, 0.5);
                color: rgba(255, 255, 255, 0.7);
            }
            QLineEdit, QSpinBox, QDoubleSpinBox {
                background: rgba(255, 255, 255, 0.8);
                border: 1px solid rgba(203, 213, 225, 0.6);
                border-radius: 6px;
                padding: 6px 10px;
                font-size: 13px;
                color: rgb(51, 65, 85);
            }
            QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus {
                border-color: rgb(59, 130, 246);
                background: rgba(255, 255, 255, 0.95);
            }
            QCheckBox {
                color: rgb(71, 85, 105);
                font-size: 13px;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border-radius: 3px;
                border: 2px solid rgba(148, 163, 184, 0.5);
                background: rgba(255, 255, 255, 0.8);
            }
            QCheckBox::indicator:checked {
                background: rgb(34, 197, 94);
                border-color: rgb(34, 197, 94);
            }
            QTextEdit {
                background: rgba(255, 255, 255, 0.8);
                border: 1px solid rgba(203, 213, 225, 0.6);
                border-radius: 8px;
                padding: 8px;
                font-size: 12px;
                color: rgb(51, 65, 85);
            }
        """)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # 标题栏
        header_layout = QHBoxLayout()

        # 标题
        title_label = QLabel("🚗 车辆碰撞检测系统")
        title_label.setStyleSheet("""
            font-size: 28px;
            font-weight: 700;
            color: rgb(59, 130, 246);
            text-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
            background: transparent;
        """)

        # 关闭按钮
        close_button = QPushButton("×")
        close_button.setFixedSize(32, 32)
        close_button.setStyleSheet("""
            QPushButton {
                background: rgba(148, 163, 184, 0.1);
                border: none;
                border-radius: 16px;
                color: rgb(100, 116, 139);
                font-size: 18px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: rgba(239, 68, 68, 0.1);
                color: rgb(239, 68, 68);
            }
            QPushButton:pressed {
                background: rgba(239, 68, 68, 0.2);
            }
        """)
        close_button.clicked.connect(self.close)

        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(close_button)

        main_layout.addLayout(header_layout)
        
        # 创建内容区域
        content_widget = QWidget()
        content_layout = QHBoxLayout(content_widget)
        content_layout.setSpacing(15)
        
        # 左侧控制面板
        self.create_control_panel(content_layout)
        
        # 右侧检测结果面板
        self.create_results_panel(content_layout)
        
        main_layout.addWidget(content_widget)
        
        # 底部按钮
        self.create_bottom_buttons(main_layout)

        # 添加阴影效果
        self.add_shadow_effect()
        
    def create_control_panel(self, parent_layout):
        """创建左侧控制面板"""
        control_frame = QFrame()
        control_frame.setFixedWidth(350)
        control_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(173, 216, 230, 0.95),
                    stop:0.5 rgba(135, 206, 235, 0.98),
                    stop:1 rgba(176, 224, 230, 0.95));
                border-radius: 10px;
                border: 1px solid rgba(100, 149, 237, 0.6);
                /* Qt不支持box-shadow属性 */
            }
        """)
        
        control_layout = QVBoxLayout(control_frame)
        control_layout.setContentsMargins(15, 15, 15, 15)
        control_layout.setSpacing(15)
        
        # 检测参数设置
        params_group = QGroupBox("检测参数设置")
        params_layout = QGridLayout(params_group)
        
        # 碰撞距离阈值
        params_layout.addWidget(QLabel("碰撞距离阈值(像素):"), 0, 0)
        self.distance_spinbox = QSpinBox()
        self.distance_spinbox.setRange(10, 200)
        self.distance_spinbox.setValue(50)
        self.distance_spinbox.setStyleSheet("""
            QSpinBox {
                background: rgba(255, 255, 255, 0.8);
                color: rgb(51, 65, 85);
                border: 1px solid rgba(100, 149, 237, 0.5);
                border-radius: 6px;
                padding: 6px 10px;
                font-size: 13px;
            }
            QSpinBox:focus {
                border-color: rgb(59, 130, 246);
                background: rgba(255, 255, 255, 0.95);
            }
            QSpinBox::up-button, QSpinBox::down-button {
                background: rgba(100, 149, 237, 0.3);
                border-radius: 3px;
            }
        """)
        params_layout.addWidget(self.distance_spinbox, 0, 1)
        
        # 速度变化阈值
        params_layout.addWidget(QLabel("速度变化阈值:"), 1, 0)
        self.speed_spinbox = QSpinBox()
        self.speed_spinbox.setRange(5, 50)
        self.speed_spinbox.setValue(20)
        self.speed_spinbox.setStyleSheet("""
            QSpinBox {
                background: rgba(255, 255, 255, 0.8);
                color: rgb(51, 65, 85);
                border: 1px solid rgba(100, 149, 237, 0.5);
                border-radius: 6px;
                padding: 6px 10px;
                font-size: 13px;
            }
            QSpinBox:focus {
                border-color: rgb(59, 130, 246);
                background: rgba(255, 255, 255, 0.95);
            }
            QSpinBox::up-button, QSpinBox::down-button {
                background: rgba(100, 149, 237, 0.3);
                border-radius: 3px;
            }
        """)
        params_layout.addWidget(self.speed_spinbox, 1, 1)
        
        # 检测敏感度
        params_layout.addWidget(QLabel("检测敏感度:"), 2, 0)
        self.sensitivity_slider = QSlider(Qt.Horizontal)
        self.sensitivity_slider.setRange(1, 10)
        self.sensitivity_slider.setValue(5)
        self.sensitivity_slider.setStyleSheet("""
            QSlider::groove:horizontal {
                border: 1px solid rgba(100, 149, 237, 0.5);
                height: 8px;
                background: rgba(255, 255, 255, 0.6);
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: rgb(59, 130, 246);
                border: 2px solid white;
                width: 16px;
                margin: -5px 0;
                border-radius: 8px;
            }
            QSlider::handle:horizontal:hover {
                background: rgb(37, 99, 235);
            }
        """)
        params_layout.addWidget(self.sensitivity_slider, 2, 1)
        
        control_layout.addWidget(params_group)
        
        # 检测状态
        status_group = QGroupBox("检测状态")
        status_layout = QVBoxLayout(status_group)
        
        self.status_label = QLabel("状态: 未开始")
        self.status_label.setStyleSheet("""
            color: rgb(51, 65, 85);
            font-size: 14px;
            font-weight: bold;
            padding: 8px;
            background: rgba(255, 255, 255, 0.7);
            border: 1px solid rgba(100, 149, 237, 0.3);
            border-radius: 6px;
        """)
        status_layout.addWidget(self.status_label)
        
        self.collision_count_label = QLabel("检测到碰撞: 0 次")
        self.collision_count_label.setStyleSheet("""
            color: rgb(51, 65, 85);
            font-size: 14px;
            font-weight: bold;
            padding: 8px;
            background: rgba(255, 255, 255, 0.7);
            border: 1px solid rgba(100, 149, 237, 0.3);
            border-radius: 6px;
        """)
        status_layout.addWidget(self.collision_count_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid rgba(100, 149, 237, 0.5);
                border-radius: 5px;
                text-align: center;
                background: rgba(255, 255, 255, 0.7);
                height: 20px;
                font-size: 12px;
                color: rgb(51, 65, 85);
            }
            QProgressBar::chunk {
                background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgb(59, 130, 246), stop:1 rgb(37, 99, 235));
                border-radius: 4px;
            }
        """)
        status_layout.addWidget(self.progress_bar)
        
        control_layout.addWidget(status_group)
        
        # 高级设置
        advanced_group = QGroupBox("高级设置")
        advanced_layout = QVBoxLayout(advanced_group)
        
        self.enable_sound_checkbox = QCheckBox("启用声音警报")
        self.enable_sound_checkbox.setChecked(True)
        self.enable_sound_checkbox.setStyleSheet("""
            QCheckBox {
                color: rgb(51, 65, 85);
                font-size: 13px;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid rgba(100, 149, 237, 0.5);
                border-radius: 4px;
                background: rgba(255, 255, 255, 0.8);
            }
            QCheckBox::indicator:checked {
                background: rgb(59, 130, 246);
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMiA2TDUgOUwxMCAzIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPjwvc3ZnPg==);
            }
            QCheckBox::indicator:hover {
                border-color: rgb(59, 130, 246);
            }
        """)
        advanced_layout.addWidget(self.enable_sound_checkbox)
        
        self.auto_save_checkbox = QCheckBox("自动保存检测结果")
        self.auto_save_checkbox.setChecked(True)
        self.auto_save_checkbox.setStyleSheet("""
            QCheckBox {
                color: rgb(51, 65, 85);
                font-size: 13px;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid rgba(100, 149, 237, 0.5);
                border-radius: 4px;
                background: rgba(255, 255, 255, 0.8);
            }
            QCheckBox::indicator:checked {
                background: rgb(59, 130, 246);
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMiA2TDUgOUwxMCAzIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPjwvc3ZnPg==);
            }
            QCheckBox::indicator:hover {
                border-color: rgb(59, 130, 246);
            }
        """)
        advanced_layout.addWidget(self.auto_save_checkbox)
        
        self.show_trajectory_checkbox = QCheckBox("显示车辆轨迹")
        self.show_trajectory_checkbox.setChecked(False)
        self.show_trajectory_checkbox.setStyleSheet("""
            QCheckBox {
                color: rgb(51, 65, 85);
                font-size: 13px;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid rgba(100, 149, 237, 0.5);
                border-radius: 4px;
                background: rgba(255, 255, 255, 0.8);
            }
            QCheckBox::indicator:checked {
                background: rgb(59, 130, 246);
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMiA2TDUgOUwxMCAzIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPjwvc3ZnPg==);
            }
            QCheckBox::indicator:hover {
                border-color: rgb(59, 130, 246);
            }
        """)
        advanced_layout.addWidget(self.show_trajectory_checkbox)
        
        control_layout.addWidget(advanced_group)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.start_button = QPushButton("开始检测")
        self.start_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgb(34, 197, 94), stop:1 rgb(22, 163, 74));
                color: white;
                border-radius: 10px;
                padding: 12px 24px;
                font-weight: 600;
                font-size: 14px;
dui                border: none;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgb(22, 163, 74), stop:1 rgb(21, 128, 61));
            }
            QPushButton:pressed {
                background: rgb(22, 163, 74);
            }
        """)
        button_layout.addWidget(self.start_button)
        
        self.stop_button = QPushButton("停止检测")
        self.stop_button.setEnabled(False)
        self.stop_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgb(239, 68, 68), stop:1 rgb(220, 38, 38));
                color: white;
                border-radius: 10px;
                padding: 12px 24px;
                font-weight: 600;
                font-size: 14px;
                border: none;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgb(220, 38, 38), stop:1 rgb(185, 28, 28));
            }
            QPushButton:pressed {
                background: rgb(185, 28, 28);
            }
            QPushButton:disabled {
                background: rgba(148, 163, 184, 0.6);
                color: rgba(255, 255, 255, 0.7);
            }
        """)
        button_layout.addWidget(self.stop_button)
        
        control_layout.addWidget(QWidget())  # 弹簧
        control_layout.addLayout(button_layout)
        
        parent_layout.addWidget(control_frame)
        
    def create_results_panel(self, parent_layout):
        """创建右侧检测结果面板"""
        results_frame = QFrame()
        results_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(173, 216, 230, 0.95),
                    stop:0.5 rgba(135, 206, 235, 0.98),
                    stop:1 rgba(176, 224, 230, 0.95));
                border-radius: 10px;
                border: 1px solid rgba(100, 149, 237, 0.6);
                /* Qt不支持box-shadow属性 */
            }
        """)
        
        results_layout = QVBoxLayout(results_frame)
        results_layout.setContentsMargins(15, 15, 15, 15)
        results_layout.setSpacing(15)
        
        # 结果标题
        results_title = QLabel("🔍 检测结果")
        results_title.setStyleSheet("""
            font-size: 18px;
            font-weight: 700;
            color: rgb(59, 130, 246);
            text-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
            background: transparent;
            margin-bottom: 10px;
        """)
        results_layout.addWidget(results_title)
        
        # 碰撞列表
        self.collision_list = QListWidget()
        self.collision_list.setStyleSheet("""
            QListWidget {
                background: rgba(255, 255, 255, 0.8);
                border: 1px solid rgba(100, 149, 237, 0.5);
                border-radius: 8px;
                color: rgb(51, 65, 85);
                font-size: 13px;
                font-family: 'Microsoft YaHei UI', 'PingFang SC', sans-serif;
            }
            QListWidget::item {
                padding: 10px;
                border-bottom: 1px solid rgba(203, 213, 225, 0.3);
                margin: 2px 5px;
                border-radius: 4px;
                background: rgba(255, 255, 255, 0.4);
            }
            QListWidget::item:hover {
                background: rgba(59, 130, 246, 0.1);
                border: 1px solid rgba(59, 130, 246, 0.3);
            }
            QListWidget::item:selected {
                background: rgba(59, 130, 246, 0.2);
                border: 1px solid rgba(59, 130, 246, 0.5);
                color: rgb(37, 99, 235);
                font-weight: 500;
            }
        """)
        results_layout.addWidget(self.collision_list)
        
        # 详细信息显示
        details_group = QGroupBox("碰撞详情")
        details_group.setStyleSheet("""
            QGroupBox {
                color: rgb(71, 85, 105);
                font-weight: 600;
                font-size: 15px;
                border: 1px solid rgba(203, 213, 225, 0.8);
                border-radius: 12px;
                margin-top: 12px;
                padding-top: 15px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.6),
                    stop:1 rgba(248, 250, 252, 0.4));
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                background: rgba(255, 255, 255, 0.9);
                border-radius: 6px;
                color: rgb(59, 130, 246);
            }
        """)
        details_layout = QVBoxLayout(details_group)
        
        self.details_text = QTextEdit()
        self.details_text.setMaximumHeight(150)
        self.details_text.setStyleSheet("""
            QTextEdit {
                background: rgba(255, 255, 255, 0.8);
                border: 1px solid rgba(100, 149, 237, 0.5);
                border-radius: 8px;
                color: rgb(51, 65, 85);
                font-size: 13px;
                font-family: 'Microsoft YaHei UI', 'PingFang SC', sans-serif;
                padding: 8px;
                selection-background-color: rgba(59, 130, 246, 0.3);
                selection-color: rgb(37, 99, 235);
            }
            QTextEdit:focus {
                border-color: rgb(59, 130, 246);
                background: rgba(255, 255, 255, 0.95);
            }
        """)
        self.details_text.setPlainText("选择一个碰撞事件查看详细信息...")
        details_layout.addWidget(self.details_text)
        
        results_layout.addWidget(details_group)
        
        parent_layout.addWidget(results_frame)
        
    def create_bottom_buttons(self, parent_layout):
        """创建底部按钮"""
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        # 清除结果按钮
        clear_button = QPushButton("清除结果")
        clear_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgb(148, 163, 184), stop:1 rgb(100, 116, 139));
                color: white;
                border-radius: 10px;
                padding: 12px 24px;
                font-weight: 600;
                font-size: 14px;
                border: none;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgb(100, 116, 139), stop:1 rgb(71, 85, 105));
            }
            QPushButton:pressed {
                background: rgb(71, 85, 105);
            }
        """)
        clear_button.clicked.connect(self.clear_results)
        button_layout.addWidget(clear_button)
        
        # 导出结果按钮
        export_button = QPushButton("导出结果")
        export_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgb(59, 130, 246), stop:1 rgb(37, 99, 235));
                color: white;
                border-radius: 10px;
                padding: 12px 24px;
                font-weight: 600;
                font-size: 14px;
                border: none;
                font-family: 'Microsoft YaHei UI', 'PingFang SC', sans-serif;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgb(37, 99, 235), stop:1 rgb(29, 78, 216));
            }
            QPushButton:pressed {
                background: rgb(29, 78, 216);
            }
        """)
        export_button.clicked.connect(self.export_results)
        button_layout.addWidget(export_button)
        
        # 关闭按钮
        close_button = QPushButton("关闭")
        close_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgb(107, 114, 128), stop:1 rgb(75, 85, 99));
                color: white;
                border-radius: 10px;
                padding: 12px 24px;
                font-weight: 600;
                font-size: 14px;
                border: none;
                font-family: 'Microsoft YaHei UI', 'PingFang SC', sans-serif;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgb(75, 85, 99), stop:1 rgb(55, 65, 81));
            }
            QPushButton:pressed {
                background: rgb(55, 65, 81);
            }
        """)
        close_button.clicked.connect(self.close)
        button_layout.addWidget(close_button)
        
        parent_layout.addLayout(button_layout)
        
    def setup_connections(self):
        """设置信号连接"""
        self.start_button.clicked.connect(self.start_detection)
        self.stop_button.clicked.connect(self.stop_detection)
        self.collision_list.itemClicked.connect(self.show_collision_details)
        
        # 参数变化时更新算法
        self.distance_spinbox.valueChanged.connect(self.update_algorithm_params)
        self.speed_spinbox.valueChanged.connect(self.update_algorithm_params)
        
    def start_detection(self):
        """开始碰撞检测"""
        self.is_detecting = True
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.status_label.setText("状态: 检测中...")
        self.status_label.setStyleSheet("color: rgb(100, 255, 100); font-size: 14px;")
        
        # 更新算法参数
        self.update_algorithm_params()
        
        # 发送开始信号
        self.detection_started.emit()
        
        # 启动进度条动画
        self.progress_bar.setRange(0, 0)  # 无限进度条
        
    def stop_detection(self):
        """停止碰撞检测"""
        self.is_detecting = False
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_label.setText("状态: 已停止")
        self.status_label.setStyleSheet("color: rgb(255, 200, 100); font-size: 14px;")
        
        # 发送停止信号
        self.detection_stopped.emit()
        
        # 停止进度条
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(100)
        
    def update_algorithm_params(self):
        """更新算法参数"""
        self.detection_algorithm.collision_threshold = self.distance_spinbox.value()
        self.detection_algorithm.speed_threshold = self.speed_spinbox.value()
        
    def process_frame_data(self, tracked_objects):
        """处理帧数据进行碰撞检测"""
        if not self.is_detecting or not tracked_objects:
            return
            
        # 执行碰撞检测
        collisions = self.detection_algorithm.detect_collision(tracked_objects, datetime.now())
        
        # 处理检测到的碰撞
        for collision in collisions:
            self.add_collision_result(collision)
            self.collision_detected.emit(collision)
            
    def add_collision_result(self, collision):
        """添加碰撞检测结果"""
        self.collision_count += 1
        self.collision_count_label.setText(f"检测到碰撞: {self.collision_count} 次")
        
        # 添加到列表
        time_str = collision['time'].strftime("%H:%M:%S")
        item_text = f"[{time_str}] 车辆 {collision['id1']} 与 {collision['id2']} 碰撞 - {collision['severity']}"
        
        item = QListWidgetItem(item_text)
        item.setData(Qt.UserRole, collision)  # 存储完整的碰撞数据
        
        # 根据严重程度设置颜色
        if collision['severity'] == "严重":
            item.setForeground(QColor(255, 100, 100))
        elif collision['severity'] == "中等":
            item.setForeground(QColor(255, 200, 100))
        else:
            item.setForeground(QColor(100, 255, 100))
            
        self.collision_list.addItem(item)
        self.collision_list.scrollToBottom()
        
    def show_collision_details(self, item):
        """显示碰撞详情"""
        collision = item.data(Qt.UserRole)
        if collision:
            details = f"""碰撞详细信息:
            
时间: {collision['time'].strftime("%Y-%m-%d %H:%M:%S")}
涉及车辆: {collision['id1']} 和 {collision['id2']}
碰撞位置: ({collision['position'][0]:.1f}, {collision['position'][1]:.1f})
距离: {collision['distance']:.1f} 像素
速度变化1: {collision['speed_change1']:.1f}
速度变化2: {collision['speed_change2']:.1f}
严重程度: {collision['severity']}
"""
            self.details_text.setPlainText(details)
            
    def clear_results(self):
        """清除检测结果"""
        self.collision_list.clear()
        self.details_text.setPlainText("选择一个碰撞事件查看详细信息...")
        self.collision_count = 0
        self.collision_count_label.setText("检测到碰撞: 0 次")
        self.detection_algorithm.collision_history.clear()
        
    def export_results(self):
        """导出检测结果"""
        if self.collision_count == 0:
            return
            
        # 这里可以实现导出功能
        print(f"导出 {self.collision_count} 个碰撞检测结果")
        
    def closeEvent(self, event):
        """关闭事件"""
        if self.is_detecting:
            self.stop_detection()
        event.accept()

    def mousePressEvent(self, event):
        """鼠标按下事件 - 实现窗口拖拽"""
        if event.button() == Qt.LeftButton:
            self.drag_pos = event.globalPosition().toPoint()

    def mouseMoveEvent(self, event):
        """鼠标移动事件 - 实现窗口拖拽"""
        if event.buttons() == Qt.LeftButton and not self.drag_pos.isNull():
            self.move(self.pos() + event.globalPosition().toPoint() - self.drag_pos)
            self.drag_pos = event.globalPosition().toPoint()
            event.accept()

    def add_shadow_effect(self):
        """添加阴影效果 - 浅蓝色阴影"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(25)  # 减小模糊半径
        shadow.setColor(QColor(100, 180, 255, 35))  # 浅蓝色阴影
        shadow.setOffset(0, 8)
        self.setGraphicsEffect(shadow)
