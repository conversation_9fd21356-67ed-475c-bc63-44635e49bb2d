# -*- coding: utf-8 -*-
# @Description : 车道热力图实现

import cv2
import numpy as np
from collections import deque

# 存储车辆经过位置的字典
lane_data = {}

# 热力图强度衰减系数
DECAY_FACTOR = 0.95
# 热力图颜色映射
COLORMAP = cv2.COLORMAP_JET
# 最大累积值
MAX_ACCUMULATION = 200
# 最大记录帧数
MAX_FRAMES = 100

# 初始化热力图
def init_heatmap(width, height):
    """
    初始化热力图数据结构
    """
    global lane_data
    lane_data = {
        'heatmap': np.zeros((height, width), dtype=np.float32),
        'accumulation_map': np.zeros((height, width), dtype=np.float32),
        'width': width,
        'height': height,
        'frame_count': 0
    }
    return lane_data

# 更新热力图数据
def update_heatmap(detections_xyxy, identities):
    """
    基于目标检测结果更新热力图
    :param detections_xyxy: 检测框坐标 [x1, y1, x2, y2]
    :param identities: 目标ID
    """
    global lane_data
    
    if 'heatmap' not in lane_data:
        return None
    
    # 应用衰减因子，让热力图随时间衰减
    lane_data['heatmap'] *= DECAY_FACTOR
    
    # 增加帧计数
    lane_data['frame_count'] += 1
    
    # 更新热力图数据
    for i, box in enumerate(detections_xyxy):
        try:
            x1, y1, x2, y2 = [int(i) for i in box]
            # 计算底部中心点（假设是车辆与道路接触点）
            bottom_center_x = int((x1 + x2) / 2)
            bottom_center_y = int(y2)
            
            # 检查点是否在图像范围内
            if (0 <= bottom_center_x < lane_data['width'] and 
                0 <= bottom_center_y < lane_data['height']):
                
                # 在热力图上增加强度
                # 使用高斯分布增强效果
                sigma = 15  # 高斯核标准差
                for dx in range(-30, 31):
                    for dy in range(-30, 31):
                        nx, ny = bottom_center_x + dx, bottom_center_y + dy
                        if (0 <= nx < lane_data['width'] and 
                            0 <= ny < lane_data['height']):
                            # 计算高斯权重
                            weight = np.exp(-(dx**2 + dy**2) / (2 * sigma**2))
                            lane_data['heatmap'][ny, nx] += weight
                            lane_data['accumulation_map'][ny, nx] += weight
                
                # 限制最大值
                lane_data['heatmap'] = np.minimum(lane_data['heatmap'], MAX_ACCUMULATION)
                
        except Exception as e:
            print(f"热力图更新错误: {e}")
            continue
    
    return lane_data['heatmap']

# 生成可视化热力图
def generate_heatmap_visualization(img_base=None, show_title=False):
    """
    生成热力图可视化图像
    :param img_base: 基础图像，如果为None则创建空白画布
    :param show_title: 是否显示标题文本
    :return: 热力图可视化图像
    """
    global lane_data
    
    if 'heatmap' not in lane_data:
        return None
    
    # 如果没有提供基础图像，创建一个黑色背景
    if img_base is None:
        img_base = np.zeros((lane_data['height'], lane_data['width'], 3), dtype=np.uint8)
        # 添加网格线
        grid_color = (30, 30, 30)
        line_width = 1
        grid_size = 50
        for y in range(0, lane_data['height'], grid_size):
            cv2.line(img_base, (0, y), (lane_data['width'], y), grid_color, line_width)
        for x in range(0, lane_data['width'], grid_size):
            cv2.line(img_base, (x, 0), (x, lane_data['height']), grid_color, line_width)
    
    # 归一化热力图到0-255范围
    norm_heatmap = cv2.normalize(lane_data['heatmap'], None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)
    
    # 应用颜色映射
    heatmap_colored = cv2.applyColorMap(norm_heatmap, COLORMAP)
    
    # 混合原始图像和热力图
    alpha = 0.7  # 透明度
    beta = 1.0 - alpha
    overlay = cv2.addWeighted(img_base, beta, heatmap_colored, alpha, 0)
    
    # 添加描述文字
    if show_title:
        font = cv2.FONT_HERSHEY_SIMPLEX
        cv2.putText(overlay, "Real-time Heatmap", (10, 30), font, 1, (255, 255, 255), 2, cv2.LINE_AA)
    
    return overlay

# 重置热力图
def reset_heatmap():
    """
    重置热力图数据
    """
    global lane_data
    if 'heatmap' in lane_data:
        lane_data['heatmap'] = np.zeros_like(lane_data['heatmap'])
        lane_data['frame_count'] = 0

# 获取累积热力图
def get_accumulation_heatmap(show_title=False):
    """
    获取长时间累积的热力图数据
    :param show_title: 是否显示标题文本
    :return: 累积热力图可视化
    """
    global lane_data
    
    if 'accumulation_map' not in lane_data:
        return None
        
    # 创建背景
    img_base = np.zeros((lane_data['height'], lane_data['width'], 3), dtype=np.uint8)
    # 添加网格线
    grid_color = (30, 30, 30)
    line_width = 1
    grid_size = 50
    for y in range(0, lane_data['height'], grid_size):
        cv2.line(img_base, (0, y), (lane_data['width'], y), grid_color, line_width)
    for x in range(0, lane_data['width'], grid_size):
        cv2.line(img_base, (x, 0), (x, lane_data['height']), grid_color, line_width)
    
    # 归一化累积热力图
    norm_heatmap = cv2.normalize(lane_data['accumulation_map'], None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)
    
    # 应用颜色映射
    heatmap_colored = cv2.applyColorMap(norm_heatmap, COLORMAP)
    
    # 混合原始图像和热力图
    alpha = 0.7  # 透明度
    beta = 1.0 - alpha
    overlay = cv2.addWeighted(img_base, beta, heatmap_colored, alpha, 0)
    
    # 添加描述文字
    if show_title:
        font = cv2.FONT_HERSHEY_SIMPLEX
        cv2.putText(overlay, "Accumulated Heatmap", (10, 30), font, 1, (255, 255, 255), 2, cv2.LINE_AA)
    
    return overlay
