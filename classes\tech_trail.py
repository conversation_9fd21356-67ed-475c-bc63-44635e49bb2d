# -*- coding: utf-8 -*-
# u8f66u8f86u8f68u8ff9u6548u679cu6a21u5757 - u79d1u6280u98ceu683c

import cv2
import numpy as np
import random
import math
import time

class TechTrail:
    """u79d1u6280u98ceu683cu7684u8f66u8f86u8f68u8ff9u6548u679cu7c7b"""
    
    def __init__(self):
        # u8f68u8ff9u70b9u7f13u5b58 - u7f13u5b58u6bcfu4e2au8f66u8f86IDu7684u8f68u8ff9u70b9
        self.track_points = {}
        # u8f68u8ff9u989cu8272u7f13u5b58 - u6bcfu4e2au8f66u8f86u4e00u79cdu989cu8272
        self.track_colors = {}
        # u8f68u8ff9u6700u540eu66f4u65b0u65f6u95f4
        self.last_update_time = {}
        # u6700u957fu4e0du66f4u65b0u65f6u95f4(u79d2) - u8f66u8f86u6d88u5931u8fd9u4e48u957fu65f6u95f4u540eu5220u9664u8f68u8ff9
        self.max_idle_time = 1.0  # 1u79d2u540eu6d88u5931
        # u8f68u8ff9u53c2u6570
        self.max_points = 12  # u51cfu5c0fu8f68u8ff9u70b9u6570
        self.line_thickness = 2  # u57fau7840u7ebfu5bbd
        self.tech_effect = True  # u662fu5426u542fu7528u79d1u6280u7279u6548
        self.dash_gap = 2  # u865au7ebfu95f4u9694
        # u8c03u8272u677f - u4f7fu7528u66f4u591au79d1u6280u611fu989cu8272
        self.color_palette = [
            (255, 60, 20),   # u84ddu8272
            (0, 165, 255),   # u6a59u8272
            (0, 215, 255),   # u9ec4u8272
            (0, 252, 124),   # u7effu8272
            (255, 0, 128),   # u7d2bu8272
            (128, 0, 255)    # u7d2bu8272
        ]
        
        # u5f53u524du65f6u95f4
        self.current_time = 0
    
    def update_track(self, object_id, bbox):
        """u66f4u65b0u76eeu6807u8f68u8ff9
        
        Args:
            object_id: u76eeu6807ID
            bbox: u8fb9u754cu6846 [x1, y1, x2, y2]
        """
        try:
            # u66f4u65b0u5f53u524du65f6u95f4
            self.current_time = time.time()
            
            # u68c0u67e5u8fb9u754cu6846u6709u6548u6027
            if bbox is None or len(bbox) != 4:
                return
                
            # u8ba1u7b97u76eeu6807u5e95u90e8u4e2du5fc3u70b9
            x1, y1, x2, y2 = bbox
            center_x = int((x1 + x2) / 2)
            bottom_y = int(y2)
            
            # u5982u679cu662fu65b0u76eeu6807uff0cu521du59cbu5316u8f68u8ff9u70b9u548cu989cu8272
            if object_id not in self.track_points:
                self.track_points[object_id] = []
                # u4e3au6bcfu4e2au8f68u8ff9u5206u914du4e00u4e2au56fau5b9au7684u989cu8272
                color_idx = object_id % len(self.color_palette)
                self.track_colors[object_id] = self.color_palette[color_idx]
            
            # u6dfbu52a0u65b0u7684u8f68u8ff9u70b9 (u4ec5u5f53u8ddd43u4e0au4e00u4e2au70b9u8db3u591fu8fdc)
            if len(self.track_points[object_id]) == 0 or self._distance(self.track_points[object_id][-1], (center_x, bottom_y)) > 3:
                self.track_points[object_id].append((center_x, bottom_y))
                
                # u9650u5236u8f68u8ff9u70b9u6570u91cf
                if len(self.track_points[object_id]) > self.max_points:
                    self.track_points[object_id].pop(0)
            
            # u66f4u65b0u6700u540eu6d3bu52a8u65f6u95f4
            self.last_update_time[object_id] = self.current_time
        except Exception as e:
            # u9519u8befu5904u7406
            print(f"u66f4u65b0u8f68u8ff9u70b9u65f6u51fau9519: {e}")
            
    def _distance(self, point1, point2):
        """u8ba1u7b97u4e24u70b9u4e4bu95f4u7684u8dddu79bb
        
        Args:
            point1: u7b2cu4e00u4e2au70b9 (x, y)
            point2: u7b2cu4e8cu4e2au70b9 (x, y)
        
        Returns:
            u4e24u70b9u4e4bu95f4u7684u8fedu5f0fu8dddu79bb
        """
        return math.sqrt((point1[0] - point2[0])**2 + (point1[1] - point2[1])**2)
    
    def draw_trails(self, frame):
        """u5728u5e27u4e0au7ed8u5236u79d1u6280u98ceu683cu7684u8f68u8ff9
        
        Args:
            frame: u8f93u5165u89c6u9891u5e27
            
        Returns:
            u6dfbu52a0u4e86u8f68u8ff9u7ebfu7684u89c6u9891u5e27
        """
        try:
            # u66f4u65b0u5f53u524du65f6u95f4
            self.current_time = time.time()
            
            # u6e05u7406u8fc7u671fu8f68u8ff9
            self._clean_expired_trails()
            
            # u5982u679cu6ca1u6709u8f68u8ff9u70b9uff0cu76f4u63a5u8fd4u56deu539fu59cbu5e27
            if not self.track_points:
                return frame
            
            # u590du5236u5e27u5e76u5728u526fu672cu4e0au7ed8u5236
            result = frame.copy()
            
            # u9010u4e2au7ed8u5236u6bcfu4e2au76eeu6807u7684u8f68u8ff9
            active_trails = 0
            for object_id, points in self.track_points.items():
                # u81f3u5c11u9700u89812u4e2au70b9u624du80fdu7ed8u5236u8f68u8ff9
                if len(points) < 2:
                    continue
                    
                # u663eu793au8f68u8ff9u7ebfu6570u91cf
                active_trails += 1
                
                # u83b7u53d6u76eeu6807u8f68u8ff9u989cu8272
                color = self.track_colors[object_id]
                
                # u7ed8u5236u79d1u6280u98ceu683cu8f68u8ff9
                if self.tech_effect:
                    self._draw_tech_trail(result, points, color, object_id)
                else:
                    # u7b80u5355u6837u5f0f - u76f4u63a5u7ed8u5236u7ebfu6761
                    pts = np.array(points, np.int32).reshape((-1, 1, 2))
                    cv2.polylines(result, [pts], False, color, self.line_thickness, cv2.LINE_AA)
            
            # u63d0u793au4fe1u606f
            if active_trails > 0:
                print(f"当前有 {active_trails} 条有效轨迹线")
            
            return result
        except Exception as e:
            # u51fau9519u5904u7406 - u8fd4u56deu539fu59cbu5e27
            print(f"u7ed8u5236u8f68u8ff9u65f6u51fau9519: {e}")
            return frame
            
    def _clean_expired_trails(self):
        """u6e05u7406u8fc7u671fu7684u8f68u8ff9"""
        # u6536u96c6u9700u8981u5220u9664u7684u5bf9u8c61ID
        expired_ids = []
        
        # u68c0u67e5u6bcfu4e2au8f68u8ff9
        for object_id in list(self.track_points.keys()):
            # u5982u679cu6ca1u6709u6700u540eu66f4u65b0u65f6u95f4u8bb0u5f55uff0cu521bu5efau4e00u4e2a
            if object_id not in self.last_update_time:
                self.last_update_time[object_id] = self.current_time
                continue
                
            # u68c0u67e5u6d3bu52a8u65f6u95f4u662fu5426u8d85u8fc7u6700u5927u7a7au95f2u65f6u95f4
            idle_time = self.current_time - self.last_update_time[object_id]
            if idle_time > self.max_idle_time:
                expired_ids.append(object_id)
                
        # u5220u9664u8fc7u671fu5bf9u8c61
        for object_id in expired_ids:
            if object_id in self.track_points:
                del self.track_points[object_id]
            if object_id in self.track_colors:
                del self.track_colors[object_id]
            if object_id in self.last_update_time:
                del self.last_update_time[object_id]
                
        # u8fd4u56deu5220u9664u6570u91cf
        return len(expired_ids)
    
    def _draw_tech_trail(self, frame, points, color, object_id):
        """u7ed8u5236u79d1u6280u98ceu683cu8f68u8ff9
        
        Args:
            frame: u5f85u7ed8u5236u7684u5e27
            points: u8f68u8ff9u70b9u5217u8868
            color: u8f68u8ff9u989cu8272
            object_id: u76eeu6807ID
        """
        try:
            # u4e0du540cu7684IDu6709u4e0du540cu7684u8f68u8ff9u6548u679cuff0cu589eu52a0u5deeu5f02u5316
            effect_type = object_id % 3
            
            # u7ed8u5236u4e24u5c42u8f68u8ff9uff1au4e00u4e2au57fau7840u8f68u8ff9u548cu4e00u4e2au7279u6548u8f68u8ff9
            # 1. u5148u7ed8u5236u57fau7840u8f68u8ff9uff08u4e3bu8f68u8ff9uff09
            pts = np.array(points, np.int32).reshape((-1, 1, 2))
            cv2.polylines(frame, [pts], False, color, self.line_thickness, cv2.LINE_AA)
            
            # 2. u518du7ed8u5236u7279u6548u8f68u8ff9uff08u6839u636eu6548u679cu7c7bu578bu4e0du540cuff09
            if effect_type == 0:
                # u6548u679c1: u95eau70c1u866bu70b9u6548u679c
                for i in range(1, len(points), 2):
                    # u5f3au8c03u70b9uff0cu9996u5c3eu70b9u989cu8272u66f4u4eae
                    bright_color = self._adjust_brightness(color, 50)
                    cv2.circle(frame, points[i], 3, bright_color, -1, cv2.LINE_AA)
                    
                # u5728u8f68u8ff9u7ec8u70b9u6dfbu52a0u6307u5411u7badu5934
                if len(points) > 1:
                    self._draw_direction_arrow(frame, points[-2], points[-1], color)
            
            elif effect_type == 1:
                # u6548u679c2: u8109u51b2u5f0fu865au7ebfu6548u679c
                for i in range(len(points)-1):
                    # u8ba1u7b97u7ebfu6bb5u957fu5ea6
                    p1 = points[i]
                    p2 = points[i+1]
                    dist = np.sqrt((p2[0]-p1[0])**2 + (p2[1]-p1[1])**2)
                    
                    # u751fu6210u865au7ebf
                    if dist > 5:  # u53eau5bf9u8db3u591fu957fu7684u7ebfu6bb5u751fu6210u865au7ebf
                        # u8ba1u7b97u5355u4f4du5411u91cf
                        dx = (p2[0] - p1[0]) / dist
                        dy = (p2[1] - p1[1]) / dist
                        
                        # u753bu865au7ebf
                        dash_length = 5
                        steps = int(dist / (dash_length + self.dash_gap))
                        for j in range(steps):
                            x1 = int(p1[0] + (dash_length + self.dash_gap) * j * dx)
                            y1 = int(p1[1] + (dash_length + self.dash_gap) * j * dy)
                            x2 = int(x1 + dash_length * dx)
                            y2 = int(y1 + dash_length * dy)
                            # u6839u636eu70b9u4f4du7f6eu8c03u6574u989cu8272
                            point_color = self._adjust_brightness(color, j*5)
                            cv2.line(frame, (x1, y1), (x2, y2), point_color, 1, cv2.LINE_AA)
                
                # u7ed8u5236u7ec8u70b9u6807u8bb0
                if points:
                    cv2.circle(frame, points[-1], 4, color, -1, cv2.LINE_AA)
                    cv2.circle(frame, points[-1], 6, color, 1, cv2.LINE_AA)
            
            else:
                # u6548u679c3: u7387u6027u52a8u6001u6548u679cu7ebf
                for i in range(len(points)-1):
                    # u4f7fu7528u6e10u53d8u7ebfu5bbd
                    ratio = (i + 1) / len(points)
                    thickness = max(1, int(self.line_thickness * ratio * 2))
                    # u6839u636eu8f68u8ff9u4f4du7f6eu6539u53d8u989cu8272u900fu660eu5ea6
                    point_color = self._adjust_brightness(color, int(80 * ratio))
                    cv2.line(frame, points[i], points[i+1], point_color, thickness, cv2.LINE_AA)
                
                # u5728u8f68u8ff9u8d77u70b9u7ed8u5236u79d1u6280u611fu56feu6807
                if len(points) > 2:
                    start_point = points[0]
                    r = 8  # u534au5f84
                    cv2.circle(frame, start_point, r, color, 1, cv2.LINE_AA)
                    cv2.circle(frame, start_point, r//2, color, 1, cv2.LINE_AA)
                    # u7ed8u5236u5341u5b57u7ebf
                    cv2.line(frame, (start_point[0]-r, start_point[1]), (start_point[0]+r, start_point[1]), color, 1, cv2.LINE_AA)
                    cv2.line(frame, (start_point[0], start_point[1]-r), (start_point[0], start_point[1]+r), color, 1, cv2.LINE_AA)
        except Exception as e:
            print(f"u7ed8u5236u79d1u6280u8f68u8ff9u65f6u51fau9519: {e}")
    
    def _draw_direction_arrow(self, frame, p1, p2, color):
        """u7ed8u5236u65b9u5411u7badu5934
        
        Args:
            frame: u5f85u7ed8u5236u7684u5e27
            p1: u8d77u59cbu70b9
            p2: u7ec8u70b9
            color: u989cu8272
        """
        try:
            # u8ba1u7b97u7badu5934u65b9u5411u5411u91cf
            dx = p2[0] - p1[0]
            dy = p2[1] - p1[1]
            # u5982u679cu8dddu79bbu592au5c0fuff0cu4e0du7ed8u5236u7badu5934
            dist = math.sqrt(dx*dx + dy*dy)
            if dist < 5:
                return
                
            # u5355u4f4du5316
            dx, dy = dx/dist, dy/dist
            
            # u7badu5934u5c3au5bf8
            arrow_length = 8
            arrow_width = 4
            
            # u8ba1u7b97u7badu5934u7684u4e09u4e2au70b9
            # u7badu5934u9876u70b9u5c31u662fp2
            # u8ba1u7b97u7badu5934u5e95u90e8u4e24u4e2au70b9
            p3 = (int(p2[0] - arrow_length * dx + arrow_width * dy), 
                  int(p2[1] - arrow_length * dy - arrow_width * dx))
            p4 = (int(p2[0] - arrow_length * dx - arrow_width * dy), 
                  int(p2[1] - arrow_length * dy + arrow_width * dx))
            
            # u7ed8u5236u5b9eu5fc3u7badu5934
            arrow_pts = np.array([p2, p3, p4], np.int32).reshape((-1, 1, 2))
            cv2.fillPoly(frame, [arrow_pts], color)
        except Exception as e:
            print(f"u7ed8u5236u65b9u5411u7badu5934u65f6u51fau9519: {e}")
    
    def _adjust_brightness(self, color, value):
        """u8c03u6574u989cu8272u4eaeu5ea6
        
        Args:
            color: u539fu8272u5f69 (B,G,R)
            value: u589eu52a0u7684u4eaeu5ea6u503c
            
        Returns:
            u8c03u6574u540eu7684u989cu8272
        """
        b, g, r = color
        # u589eu52a0u4eaeu5ea6uff0cu4f46u9650u5236u57280-255u8303u56f4u5185
        return (min(255, b + value), min(255, g + value), min(255, r + value))
    
    def clear_tracks(self):
        """u6e05u9664u6240u6709u8f68u8ff9u6570u636e"""
        self.track_points.clear()
        self.track_colors.clear()
