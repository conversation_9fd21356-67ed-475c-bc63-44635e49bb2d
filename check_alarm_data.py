#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查警报数据
"""

import pymysql
from datetime import datetime
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(override=True, dotenv_path='config/end-back.env')

def get_db_config():
    """获取数据库配置"""
    return {
        'host': '127.0.0.1',
        'port': 3306,
        'user': 'root',
        'password': '123456',
        'database': 'yolo',
        'charset': 'utf8mb4',
        'autocommit': True,
        'cursorclass': pymysql.cursors.DictCursor
    }

def check_alarm_data():
    """检查警报数据"""
    print("🔍 检查警报数据...")
    
    config = get_db_config()
    connection = pymysql.connect(**config)
    
    try:
        with connection.cursor() as cursor:
            # 检查总警报数
            cursor.execute("SELECT COUNT(*) as total FROM alarm")
            total = cursor.fetchone()['total']
            print(f"   总警报数: {total}")
            
            # 检查今日警报数
            cursor.execute("SELECT COUNT(*) as today FROM alarm WHERE DATE(create_time) = CURDATE()")
            today = cursor.fetchone()['today']
            print(f"   今日警报数: {today}")
            
            # 检查最近的警报
            cursor.execute("SELECT * FROM alarm ORDER BY create_time DESC LIMIT 5")
            recent_alarms = cursor.fetchall()
            print(f"   最近5条警报:")
            for i, alarm in enumerate(recent_alarms):
                print(f"     [{i+1}] ID:{alarm['id']} - {alarm['alarm_type']} - {alarm['create_time']}")
            
            # 按类型统计
            cursor.execute("SELECT alarm_type, COUNT(*) as count FROM alarm GROUP BY alarm_type")
            type_stats = cursor.fetchall()
            print(f"   按类型统计:")
            for stat in type_stats:
                print(f"     {stat['alarm_type']}: {stat['count']}")
            
            # 按严重程度统计
            cursor.execute("SELECT severity, COUNT(*) as count FROM alarm GROUP BY severity")
            severity_stats = cursor.fetchall()
            print(f"   按严重程度统计:")
            for stat in severity_stats:
                print(f"     {stat['severity']}: {stat['count']}")
            
            # 检查监控点关联
            cursor.execute("""
                SELECT a.monitor_id, m.location, COUNT(a.id) as count
                FROM alarm a
                LEFT JOIN monitor m ON a.monitor_id = m.id
                GROUP BY a.monitor_id, m.location
                ORDER BY count DESC
            """)
            monitor_stats = cursor.fetchall()
            print(f"   按监控点统计:")
            for stat in monitor_stats:
                print(f"     监控点{stat['monitor_id']} ({stat['location']}): {stat['count']}")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"   ❌ 检查警报数据失败: {e}")
        return False

def add_test_alarms():
    """添加测试警报数据"""
    print("🔧 添加测试警报数据...")
    
    config = get_db_config()
    connection = pymysql.connect(**config)
    
    try:
        with connection.cursor() as cursor:
            # 获取监控点
            cursor.execute("SELECT id, location, highway_section, threshold FROM monitor LIMIT 5")
            monitors = cursor.fetchall()
            
            if not monitors:
                print("   ❌ 没有监控点数据")
                return False
            
            # 添加今日警报数据
            alarm_types = ['vehicle_count', 'accident', 'congestion', 'speeding']
            severities = ['low', 'medium', 'high']
            statuses = ['pending', 'processing', 'resolved']
            
            for i in range(20):
                monitor = monitors[i % len(monitors)]
                alarm_type = alarm_types[i % len(alarm_types)]
                severity = severities[i % len(severities)]
                status = statuses[i % len(statuses)]
                
                vehicle_count = monitor['threshold'] + (i % 10) + 1
                description = f"测试警报{i+1} - {alarm_type}"
                detection_details = f'{{"confidence": 0.{85+i%10}, "type": "{alarm_type}"}}'
                
                cursor.execute("""
                    INSERT INTO alarm (monitor_id, location, highway_section, alarm_type, 
                                     description, vehicle_count, detection_details, confidence_level, 
                                     threshold, severity, status, create_time)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    monitor['id'], monitor['location'], monitor['highway_section'],
                    alarm_type, description, vehicle_count, detection_details,
                    0.85 + (i % 10) * 0.01, monitor['threshold'], severity, status,
                    datetime.now()
                ))
            
            print(f"   ✅ 成功添加20条测试警报")
            return True
            
    except Exception as e:
        print(f"   ❌ 添加测试警报失败: {e}")
        return False
    finally:
        connection.close()

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 检查和修复警报数据")
    print("=" * 60)
    
    # 1. 检查现有数据
    check_alarm_data()
    
    # 2. 如果没有数据，添加测试数据
    print("\n" + "=" * 60)
    response = input("是否添加测试警报数据? (y/n): ")
    if response.lower() == 'y':
        add_test_alarms()
        print("\n重新检查数据:")
        check_alarm_data()
    
    print("\n" + "=" * 60)
    print("💡 如果警报数据正常，重新启动后端测试API")
    print("=" * 60)

if __name__ == "__main__":
    main()
