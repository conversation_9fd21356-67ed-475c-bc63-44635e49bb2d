# 高速公路YOLOv8与ByteTrack智能监控系统 - 前端功能模块规划

## 🎯 系统概述

基于后端API接口，严格按照实际功能设计前端模块，确保每个功能都有对应的后端支持。

## 🏗️ 技术架构

### 前端技术栈
- **框架**: Vue 3.3+ (Composition API)
- **UI库**: Arco Design Vue 2.x
- **状态管理**: Pinia 2.x
- **路由**: Vue Router 4.x
- **HTTP客户端**: Axios 1.x
- **WebSocket**: Socket.IO Client 4.x
- **图表库**: ECharts 5.x
- **视频播放**: Video.js 8.x

### 后端API支持
- **基础API**: `http://localhost:5500/api/v1`
- **WebSocket**: `ws://localhost:5500/socket.io`

## 📋 功能模块设计

### 1. 🔐 用户认证模块
**路由**: `/login`
**后端API支持**:
- `POST /auth/login` - 用户登录
- `GET /auth/profile` - 获取用户信息
- `POST /auth/logout` - 用户登出

**功能特点**:
- 支持admin(管理员)和operator(收费站班长)两个角色
- JWT Token认证
- 密码MD5加密
- 自动登录状态保持

**页面组件**:
- `LoginForm.vue` - 登录表单
- `UserProfile.vue` - 用户信息显示

---

### 2. 📊 系统仪表板
**路由**: `/dashboard`
**后端API支持**:
- `GET /realtime/status` - 实时状态
- `GET /analysis/statistics/overview` - 概览统计
- `GET /system/info` - 系统信息

**功能特点**:
- 系统运行状态概览
- 实时监控点状态
- 今日统计数据
- 活跃警报数量
- 系统性能指标

**页面组件**:
- `SystemOverview.vue` - 系统概览卡片
- `RealTimeStats.vue` - 实时统计
- `ActiveAlarms.vue` - 活跃警报列表

---

### 3. 📹 监控点管理
**路由**: `/monitor`
**后端API支持**:
- `GET /monitor/list` - 监控点列表
- `GET /monitor/{id}` - 监控点详情
- `POST /monitor` - 创建监控点
- `PUT /monitor/{id}` - 更新监控点
- `DELETE /monitor/{id}` - 删除监控点
- `POST /monitor/{id}/test-connection` - 测试连接

**功能特点**:
- 监控点CRUD操作
- RTSP连接测试
- 监控点状态监控
- 地图位置显示
- 批量操作

**页面组件**:
- `MonitorList.vue` - 监控点列表
- `MonitorForm.vue` - 监控点表单
- `MonitorDetail.vue` - 监控点详情
- `ConnectionTest.vue` - 连接测试

---

### 4. 🎯 YOLO检测管理
**路由**: `/detection`
**后端API支持**:
- `POST /detection/image` - 图像检测
- `POST /detection/base64` - Base64图像检测
- `POST /detection/video` - 视频检测
- `POST /detection/rtsp` - RTSP流检测
- `GET /detection/tasks` - 检测任务列表
- `GET /detection/models` - 可用模型
- `POST /detection/model` - 切换模型

**功能特点**:
- 多种检测方式(图像/视频/RTSP)
- 实时检测结果显示
- 检测参数调节
- 模型管理和切换
- 任务状态监控

**页面组件**:
- `DetectionUpload.vue` - 文件上传检测
- `RTSPDetection.vue` - RTSP流检测
- `DetectionResults.vue` - 检测结果展示
- `ModelManagement.vue` - 模型管理
- `TaskMonitor.vue` - 任务监控

---

### 5. 🚗 多目标追踪
**路由**: `/tracking`
**后端API支持**:
- `GET /tracking/algorithms` - 可用追踪算法
- `POST /tracking/start` - 启动追踪
- `POST /tracking/stop` - 停止追踪
- `GET /tracking/status/{monitor_id}` - 追踪状态
- `GET /tracking/targets/active` - 活动目标

**功能特点**:
- ByteTrack/BotSORT算法支持
- 实时目标轨迹显示
- 追踪性能监控
- 目标详情查看
- 算法参数配置

**页面组件**:
- `TrackingControl.vue` - 追踪控制面板
- `TrajectoryVisualization.vue` - 轨迹可视化
- `ActiveTargets.vue` - 活动目标列表
- `TrackingPerformance.vue` - 性能监控

---

### 6. 🚨 事故检测
**路由**: `/accident`
**后端API支持**:
- `POST /accident/config` - 更新检测配置
- `POST /accident/start` - 启动事故检测
- `POST /accident/stop` - 停止事故检测
- `GET /accident/records` - 事故记录
- `GET /accident/statistics` - 事故统计

**功能特点**:
- 碰撞检测配置
- 实时事故预警
- 事故记录管理
- 严重程度分级
- 事故统计分析

**页面组件**:
- `AccidentConfig.vue` - 检测配置
- `AccidentAlerts.vue` - 实时警报
- `AccidentRecords.vue` - 事故记录
- `AccidentStatistics.vue` - 统计分析

---

### 7. 📈 数据分析
**路由**: `/analysis`
**后端API支持**:
- `GET /analysis/alarms` - 警报列表
- `GET /analysis/statistics/overview` - 概览统计
- `GET /analysis/heatmap` - 热力图数据
- `POST /analysis/export/alarms` - 数据导出

**功能特点**:
- 警报数据分析
- 交通流量统计
- 热力图展示
- 趋势分析图表
- 数据导出功能

**页面组件**:
- `AlarmAnalysis.vue` - 警报分析
- `TrafficStatistics.vue` - 交通统计
- `HeatmapVisualization.vue` - 热力图
- `TrendCharts.vue` - 趋势图表
- `DataExport.vue` - 数据导出

---

### 8. ⚙️ 系统管理
**路由**: `/system`
**后端API支持**:
- `GET /system/info` - 系统信息
- `GET /system/performance` - 性能指标
- `GET /system/logs` - 系统日志
- `GET /system/health-check` - 健康检查

**功能特点**:
- 系统状态监控
- 性能指标展示
- 日志查看和搜索
- 健康检查
- 系统配置管理

**页面组件**:
- `SystemInfo.vue` - 系统信息
- `PerformanceMonitor.vue` - 性能监控
- `LogViewer.vue` - 日志查看
- `HealthCheck.vue` - 健康检查

## 🧭 导航结构

### 主导航菜单
```
📊 仪表板 (/dashboard)
├── 📹 监控管理 (/monitor)
├── 🎯 检测管理 (/detection)
│   ├── 图像检测
│   ├── 视频检测
│   └── RTSP检测
├── 🚗 目标追踪 (/tracking)
├── 🚨 事故检测 (/accident)
├── 📈 数据分析 (/analysis)
│   ├── 警报分析
│   ├── 交通统计
│   └── 热力图
└── ⚙️ 系统管理 (/system)
    ├── 系统信息
    ├── 性能监控
    └── 日志查看
```

### 权限控制
- **管理员(admin)**: 所有功能权限
- **收费站班长(operator)**: 监控、检测、分析功能，无系统管理权限

## 🎨 UI设计规范

### 布局结构
- **顶部导航**: 60px高度，包含用户信息和系统状态
- **侧边栏**: 240px宽度，可折叠
- **主内容区**: 自适应，16px内边距
- **底部状态栏**: 40px高度，显示连接状态

### 色彩方案
- **主色调**: #165DFF (Arco Blue)
- **成功色**: #00B42A (Green)
- **警告色**: #FF7D00 (Orange)
- **危险色**: #F53F3F (Red)
- **信息色**: #722ED1 (Purple)

### 组件规范
- **卡片间距**: 16px
- **表格行高**: 54px
- **按钮高度**: 32px (默认)
- **输入框高度**: 32px
- **圆角**: 6px

## 🚀 开发计划

### 第一阶段：基础架构 (1-2周)
1. ✅ 项目初始化和配置
2. ✅ 认证系统实现
3. ✅ 基础布局组件
4. ✅ API封装和状态管理

### 第二阶段：核心功能 (3-6周)
1. 🔄 监控点管理模块
2. 🔄 YOLO检测模块
3. 🔄 多目标追踪模块
4. 🔄 实时视频显示

### 第三阶段：高级功能 (7-10周)
1. 🔄 事故检测模块
2. 🔄 数据分析模块
3. 🔄 系统管理模块
4. 🔄 性能优化

### 第四阶段：完善优化 (11-12周)
1. 🔄 功能测试和优化
2. 🔄 用户体验改进
3. 🔄 文档完善
4. 🔄 部署准备

## 📝 开发注意事项

1. **严格按照后端API设计**: 每个前端功能都必须有对应的后端API支持
2. **逐步实现**: 一个模块一个模块完成，避免一次性生成大量代码
3. **充分测试**: 每个模块完成后进行充分的功能测试
4. **响应式设计**: 确保在不同屏幕尺寸下的良好体验
5. **性能优化**: 注意大数据量的处理和实时更新的性能

## 🎯 核心算法体现

- **YOLOv8检测**: 实时检测结果可视化，参数调节界面
- **ByteTrack追踪**: 目标轨迹实时显示，追踪性能监控
- **事故检测**: 碰撞预警界面，事故记录管理
- **多路视频流**: 支持多路RTSP视频同时显示和处理

这个规划确保前端严格按照后端功能设计，每个模块都有明确的API支持和具体的实现方案。
