-- 违规记录表
CREATE TABLE `violation_records` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '违规记录ID',
  `monitor_id` int NOT NULL COMMENT '监控点ID',
  `violation_type` varchar(50) NOT NULL COMMENT '违规类型(speeding,wrong_way,illegal_lane_change,illegal_parking)',
  `vehicle_id` varchar(100) DEFAULT NULL COMMENT '车辆ID/车牌号',
  `track_id` int DEFAULT NULL COMMENT '追踪ID',
  `violation_time` datetime NOT NULL COMMENT '违规时间',
  `location` varchar(200) DEFAULT NULL COMMENT '违规位置',
  `speed` float DEFAULT NULL COMMENT '车辆速度(km/h)',
  `speed_limit` float DEFAULT NULL COMMENT '限速值(km/h)',
  `evidence_image` varchar(500) DEFAULT NULL COMMENT '证据图片路径',
  `evidence_video` varchar(500) DEFAULT NULL COMMENT '证据视频路径',
  `confidence` float DEFAULT NULL COMMENT '检测置信度',
  `severity` varchar(20) DEFAULT 'medium' COMMENT '严重程度(low,medium,high)',
  `status` varchar(20) DEFAULT 'pending' COMMENT '处理状态(pending,confirmed,dismissed)',
  `description` text COMMENT '违规描述',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_monitor_id` (`monitor_id`),
  KEY `idx_violation_type` (`violation_type`),
  KEY `idx_violation_time` (`violation_time`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_violation_monitor` FOREIGN KEY (`monitor_id`) REFERENCES `monitor` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='违规记录表';

-- 事故记录表
CREATE TABLE `accident_records` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '事故记录ID',
  `monitor_id` int NOT NULL COMMENT '监控点ID',
  `accident_type` varchar(50) NOT NULL COMMENT '事故类型(collision,sudden_stop,congestion,emergency)',
  `accident_time` datetime NOT NULL COMMENT '事故时间',
  `location` varchar(200) DEFAULT NULL COMMENT '事故位置',
  `involved_vehicles` text COMMENT '涉及车辆信息(JSON格式)',
  `severity_level` varchar(20) DEFAULT 'medium' COMMENT '严重程度(low,medium,high,critical)',
  `estimated_damage` varchar(100) DEFAULT NULL COMMENT '预估损失',
  `emergency_services` tinyint(1) DEFAULT 0 COMMENT '是否需要紧急服务',
  `evidence_image` varchar(500) DEFAULT NULL COMMENT '证据图片路径',
  `evidence_video` varchar(500) DEFAULT NULL COMMENT '证据视频路径',
  `confidence` float DEFAULT NULL COMMENT '检测置信度',
  `status` varchar(20) DEFAULT 'active' COMMENT '状态(active,resolved,investigating)',
  `response_time` datetime DEFAULT NULL COMMENT '响应时间',
  `resolution_time` datetime DEFAULT NULL COMMENT '解决时间',
  `description` text COMMENT '事故描述',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_monitor_id` (`monitor_id`),
  KEY `idx_accident_type` (`accident_type`),
  KEY `idx_accident_time` (`accident_time`),
  KEY `idx_severity_level` (`severity_level`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_accident_monitor` FOREIGN KEY (`monitor_id`) REFERENCES `monitor` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='事故记录表';

-- 算法配置表
CREATE TABLE `algorithm_configs` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `monitor_id` int DEFAULT NULL COMMENT '监控点ID(NULL表示全局配置)',
  `config_type` varchar(50) NOT NULL COMMENT '配置类型(tracking,violation,accident,detection)',
  `algorithm_name` varchar(100) NOT NULL COMMENT '算法名称',
  `config_data` text NOT NULL COMMENT '配置参数(JSON格式)',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否激活',
  `priority` int DEFAULT 0 COMMENT '优先级',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_monitor_id` (`monitor_id`),
  KEY `idx_config_type` (`config_type`),
  KEY `idx_algorithm_name` (`algorithm_name`),
  CONSTRAINT `fk_config_monitor` FOREIGN KEY (`monitor_id`) REFERENCES `monitor` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='算法配置表';

-- 追踪性能统计表
CREATE TABLE `tracking_performance` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '性能记录ID',
  `monitor_id` int NOT NULL COMMENT '监控点ID',
  `algorithm_name` varchar(100) NOT NULL COMMENT '追踪算法名称',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `stat_hour` int DEFAULT NULL COMMENT '统计小时',
  `total_tracks` int DEFAULT 0 COMMENT '总追踪数',
  `successful_tracks` int DEFAULT 0 COMMENT '成功追踪数',
  `lost_tracks` int DEFAULT 0 COMMENT '丢失追踪数',
  `avg_track_length` float DEFAULT NULL COMMENT '平均追踪长度',
  `fps` float DEFAULT NULL COMMENT '处理帧率',
  `cpu_usage` float DEFAULT NULL COMMENT 'CPU使用率',
  `memory_usage` float DEFAULT NULL COMMENT '内存使用率',
  `accuracy` float DEFAULT NULL COMMENT '追踪准确率',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_monitor_id` (`monitor_id`),
  KEY `idx_algorithm_name` (`algorithm_name`),
  KEY `idx_stat_date` (`stat_date`),
  CONSTRAINT `fk_performance_monitor` FOREIGN KEY (`monitor_id`) REFERENCES `monitor` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='追踪性能统计表';