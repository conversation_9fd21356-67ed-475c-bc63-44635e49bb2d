# 事故检测系统 - UI设计实现指南

## 🎨 设计概览

本文档详细说明了如何将事故检测系统的UI设计转换为实际的前端代码实现。该系统专注于智能事故检测、实时预警和快速响应管理。

### 设计特点
- **统计概览面板**：直观显示事故统计数据和关键指标
- **智能检测配置**：可视化的检测参数调节和预警设置
- **实时事故记录**：完整的事故记录表格和状态管理
- **响应式布局**：支持桌面端和移动端的完美适配
- **状态指示系统**：清晰的系统运行状态和预警级别显示

## 🎯 核心功能模块

### 1. 统计概览模块
- **事故统计**：总事故数、今日事故、处理中事故统计
- **性能指标**：平均响应时间、处理效率分析
- **趋势分析**：事故数量变化趋势和对比数据
- **状态监控**：系统运行状态和连接状态实时显示

### 2. 检测配置模块
- **监控点管理**：支持多监控点选择和切换
- **检测类型配置**：碰撞、急停、拥堵检测的独立配置
- **阈值调节**：可视化滑块控制检测敏感度
- **预警设置**：多种预警方式和严重程度配置

### 3. 事故记录模块
- **记录列表**：完整的事故记录表格显示
- **状态管理**：事故处理状态的实时更新
- **详情查看**：支持事故详情的快速查看
- **分页导航**：大量数据的分页显示和导航

## 🏗️ 组件架构设计

### 1. 主容器组件 (`AccidentDetectionSystem.vue`)

```vue
<template>
  <div class="accident-detection-system">
    <!-- Header -->
    <SystemHeader 
      :system-status="systemStatus"
      :last-update="lastUpdate"
      @refresh="handleRefresh"
    />
    
    <!-- Statistics Overview -->
    <StatisticsOverview 
      :statistics="accidentStatistics"
      :performance-metrics="performanceMetrics"
      @stat-click="handleStatClick"
    />
    
    <!-- Detection Configuration -->
    <DetectionConfiguration 
      :config="detectionConfig"
      :monitor-points="monitorPoints"
      :is-detecting="isDetecting"
      @config-change="handleConfigChange"
      @save-config="saveConfiguration"
      @reset-config="resetConfiguration"
      @start-detection="startDetection"
    />
    
    <!-- Accident Records -->
    <AccidentRecords 
      :records="accidentRecords"
      :pagination="pagination"
      :loading="recordsLoading"
      @view-details="viewAccidentDetails"
      @page-change="handlePageChange"
      @status-change="handleStatusChange"
    />
    
    <!-- Accident Details Modal -->
    <AccidentDetailsModal 
      v-if="showDetailsModal"
      :accident="selectedAccident"
      @close="closeDetailsModal"
      @update-status="updateAccidentStatus"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useWebSocket } from '@/composables/useWebSocket'
import { useAccidentDetection } from '@/composables/useAccidentDetection'
import { useNotification } from '@/composables/useNotification'

// 组件引入
import SystemHeader from './components/SystemHeader.vue'
import StatisticsOverview from './components/StatisticsOverview.vue'
import DetectionConfiguration from './components/DetectionConfiguration.vue'
import AccidentRecords from './components/AccidentRecords.vue'
import AccidentDetailsModal from './components/AccidentDetailsModal.vue'

// 响应式数据
const systemStatus = ref('正常运行')
const lastUpdate = ref(new Date())
const isDetecting = ref(false)
const recordsLoading = ref(false)
const showDetailsModal = ref(false)
const selectedAccident = ref(null)

// 事故统计数据
const accidentStatistics = reactive({
  totalAccidents: 156,
  todayAccidents: 12,
  processingAccidents: 3,
  averageResponseTime: 8.5,
  todayChange: 3,
  responseTimeUnit: '分钟'
})

// 性能指标
const performanceMetrics = reactive({
  detectionAccuracy: 0.95,
  falsePositiveRate: 0.02,
  systemUptime: 0.998,
  alertResponseRate: 0.92
})

// 检测配置
const detectionConfig = reactive({
  selectedMonitor: '监控点001',
  collisionDetection: {
    enabled: true,
    threshold: 0.8
  },
  emergencyStopDetection: {
    enabled: true,
    threshold: 5.0
  },
  trafficJamDetection: {
    enabled: true,
    densityThreshold: 0.7,
    speedThreshold: 20
  },
  alertSettings: {
    emailAlert: true,
    smsAlert: false,
    webhookAlert: true,
    severityThreshold: 'medium',
    alertInterval: 300
  }
})

// 监控点列表
const monitorPoints = ref([
  { id: '001', name: '监控点001', status: 'active' },
  { id: '002', name: '监控点002', status: 'active' },
  { id: '003', name: '监控点003', status: 'inactive' }
])

// 事故记录
const accidentRecords = ref([
  {
    id: 1,
    time: '14:35:20',
    type: '碰撞',
    severity: 'high',
    location: 'K100+500',
    status: 'processing',
    description: '两车追尾事故',
    reportedBy: '自动检测',
    assignedTo: '应急小组A'
  },
  {
    id: 2,
    time: '13:22:15',
    type: '急停',
    severity: 'medium',
    location: 'K100+300',
    status: 'resolved',
    description: '车辆紧急制动',
    reportedBy: '自动检测',
    assignedTo: '巡逻队B'
  },
  {
    id: 3,
    time: '12:45:30',
    type: '拥堵',
    severity: 'low',
    location: 'K100+800',
    status: 'resolved',
    description: '交通拥堵缓解',
    reportedBy: '自动检测',
    assignedTo: '交通管制'
  }
])

// 分页信息
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 156,
  totalPages: 16
})

// WebSocket连接
const { connect, disconnect, sendMessage } = useWebSocket()

// 事故检测功能
const { startDetection, stopDetection, updateConfig } = useAccidentDetection()

// 通知功能
const { showNotification } = useNotification()

// 生命周期
onMounted(() => {
  initializeSystem()
})

onUnmounted(() => {
  cleanup()
})

// 方法实现
const initializeSystem = async () => {
  try {
    await connect()
    await loadAccidentRecords()
    await loadSystemStatus()
  } catch (error) {
    console.error('System initialization failed:', error)
    showNotification('系统初始化失败', 'error')
  }
}

const cleanup = () => {
  disconnect()
  if (isDetecting.value) {
    stopDetection()
  }
}

const handleConfigChange = (newConfig) => {
  Object.assign(detectionConfig, newConfig)
}

const saveConfiguration = async () => {
  try {
    await updateConfig(detectionConfig)
    showNotification('配置保存成功', 'success')
  } catch (error) {
    console.error('Failed to save configuration:', error)
    showNotification('配置保存失败', 'error')
  }
}

const resetConfiguration = () => {
  // 重置为默认配置
  Object.assign(detectionConfig, getDefaultConfig())
  showNotification('配置已重置', 'info')
}

const startDetection = async () => {
  try {
    await startDetection(detectionConfig)
    isDetecting.value = true
    showNotification('检测已启动', 'success')
  } catch (error) {
    console.error('Failed to start detection:', error)
    showNotification('启动检测失败', 'error')
  }
}

const viewAccidentDetails = (accident) => {
  selectedAccident.value = accident
  showDetailsModal.value = true
}

const closeDetailsModal = () => {
  showDetailsModal.value = false
  selectedAccident.value = null
}

const updateAccidentStatus = async (accidentId, newStatus) => {
  try {
    await updateAccidentStatus(accidentId, newStatus)
    // 更新本地记录
    const record = accidentRecords.value.find(r => r.id === accidentId)
    if (record) {
      record.status = newStatus
    }
    showNotification('状态更新成功', 'success')
  } catch (error) {
    console.error('Failed to update status:', error)
    showNotification('状态更新失败', 'error')
  }
}

const handlePageChange = async (page) => {
  pagination.currentPage = page
  await loadAccidentRecords()
}

const loadAccidentRecords = async () => {
  recordsLoading.value = true
  try {
    const response = await fetch(`/api/accidents?page=${pagination.currentPage}&size=${pagination.pageSize}`)
    const data = await response.json()
    accidentRecords.value = data.records
    pagination.total = data.total
    pagination.totalPages = data.totalPages
  } catch (error) {
    console.error('Failed to load accident records:', error)
    showNotification('加载记录失败', 'error')
  } finally {
    recordsLoading.value = false
  }
}

const loadSystemStatus = async () => {
  try {
    const response = await fetch('/api/system/status')
    const data = await response.json()
    systemStatus.value = data.status
    lastUpdate.value = new Date(data.lastUpdate)
  } catch (error) {
    console.error('Failed to load system status:', error)
  }
}

const getDefaultConfig = () => {
  return {
    selectedMonitor: '监控点001',
    collisionDetection: {
      enabled: true,
      threshold: 0.8
    },
    emergencyStopDetection: {
      enabled: true,
      threshold: 5.0
    },
    trafficJamDetection: {
      enabled: true,
      densityThreshold: 0.7,
      speedThreshold: 20
    },
    alertSettings: {
      emailAlert: true,
      smsAlert: false,
      webhookAlert: true,
      severityThreshold: 'medium',
      alertInterval: 300
    }
  }
}
</script>

<style scoped>
.accident-detection-system {
  min-height: 100vh;
  background: linear-gradient(to bottom, #f7fafc, #edf2f7);
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

@media (max-width: 768px) {
  .accident-detection-system {
    padding: 10px;
    gap: 15px;
  }
}
</style>
```

### 2. 统计概览组件 (`StatisticsOverview.vue`)

```vue
<template>
  <div class="statistics-overview">
    <div class="section-header">
      <h2 class="section-title">
        <span class="title-icon">📊</span>
        事故统计概览
      </h2>
      <div class="refresh-indicator" v-if="isRefreshing">
        <span class="spinner"></span>
        更新中...
      </div>
    </div>
    
    <div class="stats-grid">
      <!-- Total Accidents -->
      <StatCard
        :value="statistics.totalAccidents"
        label="总事故数"
        sublabel="累计统计"
        icon="⚠️"
        type="danger"
        :trend="null"
        @click="$emit('stat-click', 'total')"
      />
      
      <!-- Today's Accidents -->
      <StatCard
        :value="statistics.todayAccidents"
        label="今日事故"
        :sublabel="`较昨日 +${statistics.todayChange}`"
        icon="📈"
        type="warning"
        :trend="statistics.todayChange > 0 ? 'up' : 'down'"
        @click="$emit('stat-click', 'today')"
      />
      
      <!-- Processing Accidents -->
      <StatCard
        :value="statistics.processingAccidents"
        label="处理中"
        sublabel="需要关注"
        icon="🔄"
        type="warning"
        :animated="true"
        @click="$emit('stat-click', 'processing')"
      />
      
      <!-- Average Response Time -->
      <StatCard
        :value="statistics.averageResponseTime"
        label="平均响应"
        :sublabel="statistics.responseTimeUnit"
        icon="⚡"
        type="success"
        :trend="'stable'"
        @click="$emit('stat-click', 'response')"
      />
    </div>
    
    <!-- Performance Metrics -->
    <div class="performance-section">
      <h3 class="performance-title">系统性能指标</h3>
      <div class="metrics-grid">
        <MetricItem
          label="检测准确率"
          :value="performanceMetrics.detectionAccuracy"
          format="percentage"
          color="#48bb78"
        />
        <MetricItem
          label="误报率"
          :value="performanceMetrics.falsePositiveRate"
          format="percentage"
          color="#ed8936"
        />
        <MetricItem
          label="系统正常运行时间"
          :value="performanceMetrics.systemUptime"
          format="percentage"
          color="#4299e1"
        />
        <MetricItem
          label="预警响应率"
          :value="performanceMetrics.alertResponseRate"
          format="percentage"
          color="#9f7aea"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import StatCard from './StatCard.vue'
import MetricItem from './MetricItem.vue'

const props = defineProps({
  statistics: {
    type: Object,
    required: true
  },
  performanceMetrics: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['stat-click'])

const isRefreshing = ref(false)
</script>

<style scoped>
.statistics-overview {
  background: linear-gradient(to bottom, #ffffff, #f7fafc);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 20px;
  font-weight: bold;
  color: #2d3748;
  margin: 0;
}

.title-icon {
  font-size: 24px;
}

.refresh-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #718096;
}

.spinner {
  width: 12px;
  height: 12px;
  border: 2px solid #e2e8f0;
  border-top: 2px solid #4299e1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.performance-section {
  border-top: 1px solid #e2e8f0;
  padding-top: 24px;
}

.performance-title {
  font-size: 16px;
  font-weight: bold;
  color: #4a5568;
  margin: 0 0 16px 0;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
}
</style>
```

### 3. 检测配置组件 (`DetectionConfiguration.vue`)

```vue
<template>
  <div class="detection-configuration">
    <div class="section-header">
      <h2 class="section-title">
        <span class="title-icon">🔧</span>
        检测配置
      </h2>
      <div class="config-status">
        <span class="status-indicator" :class="{ active: isDetecting }"></span>
        {{ isDetecting ? '检测运行中' : '检测已停止' }}
      </div>
    </div>
    
    <!-- Monitor Point Selection -->
    <div class="config-section">
      <label class="config-label">监控点:</label>
      <select 
        v-model="localConfig.selectedMonitor"
        class="monitor-select"
        @change="handleConfigChange"
      >
        <option 
          v-for="point in monitorPoints" 
          :key="point.id" 
          :value="point.name"
          :disabled="point.status !== 'active'"
        >
          {{ point.name }} {{ point.status !== 'active' ? '(离线)' : '' }}
        </option>
      </select>
    </div>
    
    <!-- Detection Types -->
    <div class="config-section">
      <h3 class="subsection-title">检测类型:</h3>
      
      <!-- Collision Detection -->
      <DetectionTypeConfig
        v-model:enabled="localConfig.collisionDetection.enabled"
        v-model:threshold="localConfig.collisionDetection.threshold"
        label="碰撞检测"
        :min="0.1"
        :max="1.0"
        :step="0.1"
        threshold-label="阈值"
        @change="handleConfigChange"
      />
      
      <!-- Emergency Stop Detection -->
      <DetectionTypeConfig
        v-model:enabled="localConfig.emergencyStopDetection.enabled"
        v-model:threshold="localConfig.emergencyStopDetection.threshold"
        label="急停检测"
        :min="1.0"
        :max="10.0"
        :step="0.5"
        threshold-label="阈值"
        @change="handleConfigChange"
      />
      
      <!-- Traffic Jam Detection -->
      <div class="detection-type">
        <div class="detection-header">
          <label class="checkbox-container">
            <input 
              type="checkbox" 
              v-model="localConfig.trafficJamDetection.enabled"
              @change="handleConfigChange"
            >
            <span class="checkmark"></span>
            拥堵检测
          </label>
        </div>
        <div class="detection-controls" v-if="localConfig.trafficJamDetection.enabled">
          <div class="threshold-control">
            <span class="threshold-label">密度:</span>
            <input 
              type="range" 
              v-model="localConfig.trafficJamDetection.densityThreshold"
              min="0.1" 
              max="1.0" 
              step="0.1"
              class="threshold-slider"
              @input="handleConfigChange"
            >
            <span class="threshold-value">{{ localConfig.trafficJamDetection.densityThreshold }}</span>
          </div>
          <div class="speed-control">
            <span class="speed-label">速度:</span>
            <input 
              type="number" 
              v-model="localConfig.trafficJamDetection.speedThreshold"
              min="5" 
              max="60" 
              class="speed-input"
              @change="handleConfigChange"
            >
            <span class="speed-unit">km/h</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Alert Settings -->
    <div class="config-section">
      <h3 class="subsection-title">预警设置:</h3>
      
      <div class="alert-types">
        <label class="checkbox-container">
          <input 
            type="checkbox" 
            v-model="localConfig.alertSettings.emailAlert"
            @change="handleConfigChange"
          >
          <span class="checkmark"></span>
          邮件预警
        </label>
        
        <label class="checkbox-container">
          <input 
            type="checkbox" 
            v-model="localConfig.alertSettings.smsAlert"
            @change="handleConfigChange"
          >
          <span class="checkmark"></span>
          短信预警
        </label>
        
        <label class="checkbox-container">
          <input 
            type="checkbox" 
            v-model="localConfig.alertSettings.webhookAlert"
            @change="handleConfigChange"
          >
          <span class="checkmark"></span>
          Webhook
        </label>
      </div>
      
      <div class="alert-settings">
        <div class="setting-item">
          <label class="setting-label">严重程度阈值:</label>
          <select 
            v-model="localConfig.alertSettings.severityThreshold"
            class="severity-select"
            @change="handleConfigChange"
          >
            <option value="low">低</option>
            <option value="medium">中等</option>
            <option value="high">高</option>
          </select>
        </div>
        
        <div class="setting-item">
          <label class="setting-label">预警间隔:</label>
          <input 
            type="number" 
            v-model="localConfig.alertSettings.alertInterval"
            min="60" 
            max="3600" 
            step="60"
            class="interval-input"
            @change="handleConfigChange"
          >
          <span class="interval-unit">秒</span>
        </div>
      </div>
    </div>
    
    <!-- Action Buttons -->
    <div class="action-buttons">
      <button 
        class="btn btn-primary"
        @click="$emit('save-config')"
        :disabled="!hasChanges"
      >
        💾 保存配置
      </button>
      
      <button 
        class="btn btn-secondary"
        @click="$emit('reset-config')"
      >
        🔄 重置
      </button>
      
      <button 
        class="btn btn-success"
        @click="$emit('start-detection')"
        :disabled="isDetecting"
      >
        {{ isDetecting ? '⏸️ 检测运行中' : '▶️ 启动检测' }}
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import DetectionTypeConfig from './DetectionTypeConfig.vue'

const props = defineProps({
  config: {
    type: Object,
    required: true
  },
  monitorPoints: {
    type: Array,
    required: true
  },
  isDetecting: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['config-change', 'save-config', 'reset-config', 'start-detection'])

// 本地配置副本
const localConfig = reactive({ ...props.config })
const originalConfig = ref(JSON.stringify(props.config))

// 检查是否有变更
const hasChanges = computed(() => {
  return JSON.stringify(localConfig) !== originalConfig.value
})

// 监听配置变化
watch(() => props.config, (newConfig) => {
  Object.assign(localConfig, newConfig)
  originalConfig.value = JSON.stringify(newConfig)
}, { deep: true })

const handleConfigChange = () => {
  emit('config-change', { ...localConfig })
}
</script>

<style scoped>
.detection-configuration {
  background: linear-gradient(to bottom, #ffffff, #f7fafc);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 20px;
  font-weight: bold;
  color: #2d3748;
  margin: 0;
}

.title-icon {
  font-size: 24px;
}

.config-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #718096;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #cbd5e0;
}

.status-indicator.active {
  background: #48bb78;
  animation: pulse 2s infinite;
}

.config-section {
  margin-bottom: 32px;
}

.config-label {
  font-size: 14px;
  font-weight: 500;
  color: #4a5568;
  margin-right: 12px;
}

.monitor-select {
  padding: 8px 12px;
  border: 1px solid #cbd5e0;
  border-radius: 6px;
  background: #ffffff;
  font-size: 12px;
  color: #2d3748;
  min-width: 200px;
}

.subsection-title {
  font-size: 16px;
  font-weight: bold;
  color: #4a5568;
  margin: 0 0 16px 0;
}

.detection-type {
  margin-bottom: 16px;
  padding: 16px;
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.detection-header {
  margin-bottom: 12px;
}

.checkbox-container {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #2d3748;
  cursor: pointer;
  margin-right: 20px;
}

.checkbox-container input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #48bb78;
}

.detection-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.threshold-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

.threshold-label {
  font-size: 12px;
  color: #718096;
  min-width: 40px;
}

.threshold-slider {
  width: 100px;
  height: 6px;
  border-radius: 3px;
  background: #e2e8f0;
  outline: none;
  accent-color: #4299e1;
}

.threshold-value {
  font-size: 10px;
  color: #4a5568;
  min-width: 30px;
  text-align: center;
}

.speed-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

.speed-label {
  font-size: 12px;
  color: #718096;
}

.speed-input {
  width: 60px;
  padding: 4px 8px;
  border: 1px solid #cbd5e0;
  border-radius: 4px;
  font-size: 12px;
  text-align: center;
}

.speed-unit {
  font-size: 12px;
  color: #718096;
}

.alert-types {
  display: flex;
  gap: 20px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.alert-settings {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.setting-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.setting-label {
  font-size: 12px;
  color: #718096;
  min-width: 80px;
}

.severity-select {
  padding: 4px 8px;
  border: 1px solid #cbd5e0;
  border-radius: 4px;
  font-size: 12px;
  background: #ffffff;
}

.interval-input {
  width: 80px;
  padding: 4px 8px;
  border: 1px solid #cbd5e0;
  border-radius: 4px;
  font-size: 12px;
  text-align: center;
}

.interval-unit {
  font-size: 12px;
  color: #718096;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: #4299e1;
  color: #ffffff;
  border: 1px solid #3182ce;
}

.btn-primary:hover:not(:disabled) {
  background: #3182ce;
  transform: translateY(-1px);
}

.btn-secondary {
  background: #ffffff;
  color: #4a5568;
  border: 1px solid #cbd5e0;
}

.btn-secondary:hover {
  background: #f7fafc;
  transform: translateY(-1px);
}

.btn-success {
  background: #48bb78;
  color: #ffffff;
  border: 1px solid #38a169;
}

.btn-success:hover:not(:disabled) {
  background: #38a169;
  transform: translateY(-1px);
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

@media (max-width: 768px) {
  .detection-controls {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .alert-types {
    flex-direction: column;
    gap: 12px;
  }
  
  .alert-settings {
    flex-direction: column;
    gap: 12px;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .btn {
    justify-content: center;
  }
}
</style>
```

## 📱 响应式设计适配

### 移动端布局调整
```css
@media (max-width: 768px) {
  .accident-detection-system {
    padding: 10px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .accident-records-table {
    font-size: 12px;
  }
  
  .table-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .config-section {
    padding: 16px;
  }
  
  .detection-controls {
    flex-direction: column;
    align-items: stretch;
  }
}

@media (max-width: 480px) {
  .section-title {
    font-size: 16px;
  }
  
  .stats-grid {
    gap: 12px;
  }
  
  .stat-card {
    padding: 16px;
  }
  
  .action-buttons {
    flex-direction: column;
  }
}
```

## 🔧 技术实现要点

### 1. 实时数据更新
```javascript
// composables/useAccidentDetection.js
import { ref, reactive } from 'vue'

export function useAccidentDetection() {
  const isDetecting = ref(false)
  const detectionConfig = reactive({})
  
  const startDetection = async (config) => {
    try {
      const response = await fetch('/api/accident-detection/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
      })
      
      if (response.ok) {
        isDetecting.value = true
        return await response.json()
      }
    } catch (error) {
      console.error('Failed to start detection:', error)
      throw error
    }
  }
  
  const stopDetection = async () => {
    try {
      await fetch('/api/accident-detection/stop', { method: 'POST' })
      isDetecting.value = false
    } catch (error) {
      console.error('Failed to stop detection:', error)
      throw error
    }
  }
  
  const updateConfig = async (config) => {
    try {
      const response = await fetch('/api/accident-detection/config', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
      })
      
      if (response.ok) {
        Object.assign(detectionConfig, config)
        return await response.json()
      }
    } catch (error) {
      console.error('Failed to update config:', error)
      throw error
    }
  }
  
  return {
    isDetecting,
    detectionConfig,
    startDetection,
    stopDetection,
    updateConfig
  }
}
```

### 2. 事故记录管理
```javascript
// composables/useAccidentRecords.js
import { ref, reactive } from 'vue'

export function useAccidentRecords() {
  const records = ref([])
  const pagination = reactive({
    currentPage: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0
  })
  const loading = ref(false)
  
  const loadRecords = async (page = 1, filters = {}) => {
    loading.value = true
    try {
      const params = new URLSearchParams({
        page,
        size: pagination.pageSize,
        ...filters
      })
      
      const response = await fetch(`/api/accidents?${params}`)
      const data = await response.json()
      
      records.value = data.records
      pagination.currentPage = data.currentPage
      pagination.total = data.total
      pagination.totalPages = data.totalPages
    } catch (error) {
      console.error('Failed to load records:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  const updateAccidentStatus = async (accidentId, status) => {
    try {
      const response = await fetch(`/api/accidents/${accidentId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status })
      })
      
      if (response.ok) {
        // 更新本地记录
        const record = records.value.find(r => r.id === accidentId)
        if (record) {
          record.status = status
        }
        return await response.json()
      }
    } catch (error) {
      console.error('Failed to update status:', error)
      throw error
    }
  }
  
  return {
    records,
    pagination,
    loading,
    loadRecords,
    updateAccidentStatus
  }
}
```

### 3. 通知系统
```javascript
// composables/useNotification.js
import { ref } from 'vue'

export function useNotification() {
  const notifications = ref([])
  
  const showNotification = (message, type = 'info', duration = 3000) => {
    const id = Date.now()
    const notification = {
      id,
      message,
      type,
      visible: true
    }
    
    notifications.value.push(notification)
    
    setTimeout(() => {
      hideNotification(id)
    }, duration)
  }
  
  const hideNotification = (id) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }
  
  return {
    notifications,
    showNotification,
    hideNotification
  }
}
```

## 🚀 部署和优化建议

### 1. 性能优化
- **数据分页**：大量事故记录的分页加载和虚拟滚动
- **实时更新**：WebSocket连接的心跳检测和自动重连
- **缓存策略**：统计数据的本地缓存和定时更新
- **懒加载**：非关键组件的按需加载

### 2. 用户体验优化
- **加载状态**：数据加载时的骨架屏和进度指示
- **错误处理**：友好的错误提示和重试机制
- **快捷操作**：键盘快捷键和批量操作支持
- **数据导出**：事故记录的Excel导出功能

### 3. 安全性考虑
- **权限控制**：基于角色的功能访问控制
- **数据验证**：前端和后端的双重数据验证
- **审计日志**：关键操作的日志记录和追踪
- **敏感信息保护**：个人信息的脱敏处理

---

通过遵循本实现指南，您可以构建一个功能完整、性能优秀的事故检测系统界面，为交通管理人员提供专业的事故监控和应急响应工具。