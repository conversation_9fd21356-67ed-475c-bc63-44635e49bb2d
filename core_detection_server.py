# -*- coding: utf-8 -*-
# @Description : 核心检测服务器 - 集成YOLO检测和多目标追踪
# @Date : 2025年6月21日

import os
import cv2
import torch
import numpy as np
import threading
import time
import json
from datetime import datetime
from flask import Flask, request, jsonify
from flask_cors import CORS
import pymysql
from ultralytics import YOLO

# 导入新增的高级检测功能
from advanced_detection_engine import ViolationDetector, AccidentDetector, TrafficAnalyzer
from websocket_server import start_websocket_server, get_websocket_pusher

# 导入追踪器
try:
    from yolox.tracker.byte_tracker import BYTETracker
    BYTETRACK_AVAILABLE = True
except ImportError:
    print("警告: ByteTracker未安装，将使用简单追踪")
    BYTETRACK_AVAILABLE = False

class DetectionEngine:
    """检测引擎 - 集成YOLO和多目标追踪"""

    def __init__(self):
        self.model = None
        self.trackers = {}  # 每个监控点一个追踪器
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'

        # 新增高级检测功能
        self.violation_detector = ViolationDetector()
        self.accident_detector = AccidentDetector()
        self.traffic_analyzer = TrafficAnalyzer()

        self.load_model()
        
    def load_model(self):
        """加载YOLO模型"""
        try:
            # 尝试加载不同的YOLO模型
            model_paths = [
                'yolov8n.pt',
                'yolov8s.pt', 
                'yolov8m.pt',
                'models/yolov8n.pt',
                'weights/yolov8n.pt'
            ]
            
            for model_path in model_paths:
                if os.path.exists(model_path):
                    print(f"加载YOLO模型: {model_path}")
                    self.model = YOLO(model_path)
                    self.model.to(self.device)
                    break
            
            if self.model is None:
                print("未找到本地模型，下载默认模型...")
                self.model = YOLO('yolov8n.pt')  # 自动下载
                self.model.to(self.device)
                
            print(f"✅ YOLO模型加载成功，设备: {self.device}")
            
        except Exception as e:
            print(f"❌ YOLO模型加载失败: {e}")
            self.model = None
    
    def get_tracker(self, monitor_id, tracker_type='bytetrack'):
        """获取或创建追踪器"""
        if monitor_id not in self.trackers:
            if tracker_type == 'bytetrack' and BYTETRACK_AVAILABLE:
                # ByteTracker配置
                class Args:
                    track_thresh = 0.5
                    track_buffer = 30
                    match_thresh = 0.8
                    mot20 = False
                
                self.trackers[monitor_id] = BYTETracker(Args())
            else:
                # 简单追踪器
                self.trackers[monitor_id] = SimpleTracker()
                
        return self.trackers[monitor_id]
    
    def detect_frame(self, frame, monitor_id, conf_threshold=0.3, enable_tracking=True, tracker_type='bytetrack', frame_count=0):
        """检测单帧图像 - 集成违规检测和事故检测"""
        if self.model is None:
            return None, [], [], []

        try:
            # YOLO检测
            results = self.model(frame, conf=conf_threshold, verbose=False)

            detections = []
            boxes = []

            for result in results:
                if result.boxes is not None:
                    for box in result.boxes:
                        # 获取检测信息
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        conf = box.conf[0].cpu().numpy()
                        cls = int(box.cls[0].cpu().numpy())

                        # 只检测车辆相关类别 (car, truck, bus, motorcycle)
                        vehicle_classes = [2, 3, 5, 7]  # COCO类别ID
                        if cls in vehicle_classes:
                            detection = {
                                'bbox': [float(x1), float(y1), float(x2), float(y2)],
                                'confidence': float(conf),
                                'class_id': cls,
                                'class_name': self.model.names[cls]
                            }
                            detections.append(detection)
                            boxes.append([x1, y1, x2, y2, conf])

            # 多目标追踪
            tracked_objects = []
            if enable_tracking and boxes:
                tracker = self.get_tracker(monitor_id, tracker_type)

                if BYTETRACK_AVAILABLE and tracker_type == 'bytetrack':
                    # ByteTracker追踪
                    online_targets = tracker.update(
                        np.array(boxes),
                        [frame.shape[0], frame.shape[1]],
                        [frame.shape[0], frame.shape[1]]
                    )

                    for target in online_targets:
                        tracked_objects.append({
                            'track_id': target.track_id,
                            'bbox': target.tlbr.tolist(),
                            'confidence': target.score
                        })
                else:
                    # 简单追踪
                    tracked_objects = tracker.update(detections)

            # 违规检测
            violations = []
            if tracked_objects:
                # 违规停车检测
                parking_violations = self.violation_detector.detect_illegal_parking(
                    monitor_id, tracked_objects, frame_count
                )
                violations.extend(parking_violations)

                # 逆行检测
                direction_violations = self.violation_detector.detect_wrong_direction(
                    monitor_id, tracked_objects
                )
                violations.extend(direction_violations)

            # 事故检测
            accidents = []
            if tracked_objects:
                # 碰撞检测
                collision_accidents = self.accident_detector.detect_collision(tracked_objects)
                accidents.extend(collision_accidents)

                # 急停检测
                sudden_stop_accidents = self.accident_detector.detect_sudden_stop(tracked_objects)
                accidents.extend(sudden_stop_accidents)

                # 拥堵检测
                frame_area = frame.shape[0] * frame.shape[1]
                congestion_accidents = self.accident_detector.detect_congestion(
                    tracked_objects, frame_area
                )
                accidents.extend(congestion_accidents)

            # 更新交通统计
            self.traffic_analyzer.update_statistics(
                monitor_id, tracked_objects, datetime.now()
            )

            return detections, tracked_objects, violations, accidents

        except Exception as e:
            print(f"检测失败: {e}")
            return [], [], [], []

class SimpleTracker:
    """简单的多目标追踪器"""
    
    def __init__(self):
        self.tracks = {}
        self.next_id = 1
        self.max_disappeared = 10
        
    def update(self, detections):
        """更新追踪"""
        if not detections:
            return []
        
        tracked_objects = []
        
        for detection in detections:
            bbox = detection['bbox']
            center = [(bbox[0] + bbox[2]) / 2, (bbox[1] + bbox[3]) / 2]
            
            # 简单的最近邻匹配
            best_match = None
            min_distance = float('inf')
            
            for track_id, track in self.tracks.items():
                distance = np.sqrt((center[0] - track['center'][0])**2 + 
                                 (center[1] - track['center'][1])**2)
                if distance < min_distance and distance < 100:  # 距离阈值
                    min_distance = distance
                    best_match = track_id
            
            if best_match:
                # 更新现有轨迹
                self.tracks[best_match]['center'] = center
                self.tracks[best_match]['bbox'] = bbox
                self.tracks[best_match]['disappeared'] = 0
                
                tracked_objects.append({
                    'track_id': best_match,
                    'bbox': bbox,
                    'confidence': detection['confidence']
                })
            else:
                # 创建新轨迹
                self.tracks[self.next_id] = {
                    'center': center,
                    'bbox': bbox,
                    'disappeared': 0
                }
                
                tracked_objects.append({
                    'track_id': self.next_id,
                    'bbox': bbox,
                    'confidence': detection['confidence']
                })
                
                self.next_id += 1
        
        # 清理消失的轨迹
        to_delete = []
        for track_id in self.tracks:
            self.tracks[track_id]['disappeared'] += 1
            if self.tracks[track_id]['disappeared'] > self.max_disappeared:
                to_delete.append(track_id)
        
        for track_id in to_delete:
            del self.tracks[track_id]
        
        return tracked_objects

class VideoStreamManager:
    """视频流管理器"""
    
    def __init__(self, detection_engine):
        self.detection_engine = detection_engine
        self.streams = {}  # 活跃的视频流
        self.stream_threads = {}  # 流处理线程
        
    def start_stream(self, monitor_id, stream_url, config):
        """启动视频流处理"""
        if monitor_id in self.streams:
            self.stop_stream(monitor_id)
        
        self.streams[monitor_id] = {
            'url': stream_url,
            'config': config,
            'status': 'starting',
            'last_frame': None,
            'last_detection': None,
            'vehicle_count': 0,
            'alarm_triggered': False
        }
        
        # 启动处理线程
        thread = threading.Thread(
            target=self._process_stream,
            args=(monitor_id, stream_url, config),
            daemon=True
        )
        thread.start()
        self.stream_threads[monitor_id] = thread
        
        print(f"✅ 启动视频流: 监控点{monitor_id}")
    
    def stop_stream(self, monitor_id):
        """停止视频流处理"""
        if monitor_id in self.streams:
            self.streams[monitor_id]['status'] = 'stopping'
            time.sleep(1)  # 等待线程结束
            
            if monitor_id in self.streams:
                del self.streams[monitor_id]
            if monitor_id in self.stream_threads:
                del self.stream_threads[monitor_id]
                
            print(f"⏹️ 停止视频流: 监控点{monitor_id}")
    
    def _process_stream(self, monitor_id, stream_url, config):
        """处理视频流"""
        cap = None
        
        try:
            # 打开视频流
            cap = cv2.VideoCapture(stream_url)
            if not cap.isOpened():
                # 如果RTSP失败，尝试使用测试视频
                test_videos = ['test.mp4', 'demo.mp4', 'sample.mp4']
                for test_video in test_videos:
                    if os.path.exists(test_video):
                        cap = cv2.VideoCapture(test_video)
                        print(f"使用测试视频: {test_video}")
                        break
                
                if not cap.isOpened():
                    # 使用摄像头
                    cap = cv2.VideoCapture(0)
                    print("使用默认摄像头")
            
            if not cap.isOpened():
                self.streams[monitor_id]['status'] = 'error'
                return
            
            self.streams[monitor_id]['status'] = 'running'
            frame_count = 0
            
            while self.streams[monitor_id]['status'] == 'running':
                ret, frame = cap.read()
                if not ret:
                    # 视频结束，重新开始
                    cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                    continue
                
                frame_count += 1
                
                # 每5帧检测一次（提高性能）
                if frame_count % 5 == 0:
                    detections, tracked_objects, violations, accidents = self.detection_engine.detect_frame(
                        frame,
                        monitor_id,
                        conf_threshold=config.get('conf_threshold', 0.3),
                        enable_tracking=config.get('enable_tracking', True),
                        tracker_type=config.get('tracker_type', 'bytetrack'),
                        frame_count=frame_count
                    )

                    # 更新流状态
                    vehicle_count = len(detections)
                    timestamp = datetime.now().isoformat()

                    detection_data = {
                        'detections': detections,
                        'tracked_objects': tracked_objects,
                        'violations': violations,
                        'accidents': accidents,
                        'vehicle_count': vehicle_count,
                        'timestamp': timestamp
                    }

                    self.streams[monitor_id]['last_frame'] = frame
                    self.streams[monitor_id]['last_detection'] = detection_data
                    self.streams[monitor_id]['vehicle_count'] = vehicle_count

                    # WebSocket实时推送检测数据
                    pusher = get_websocket_pusher()
                    if pusher:
                        import asyncio
                        try:
                            loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(loop)
                            loop.run_until_complete(
                                pusher.push_detection_update(monitor_id, detection_data)
                            )
                            loop.close()
                        except Exception as e:
                            print(f"WebSocket推送失败: {e}")

                    # 处理违规事件
                    for violation in violations:
                        self._handle_violation(monitor_id, violation)

                    # 处理事故事件
                    for accident in accidents:
                        self._handle_accident(monitor_id, accident)

                    # 检查是否触发车流量警报
                    threshold = config.get('threshold', 15)
                    if vehicle_count > threshold:
                        if not self.streams[monitor_id]['alarm_triggered']:
                            self._trigger_alarm(monitor_id, vehicle_count, threshold, detections)
                            self.streams[monitor_id]['alarm_triggered'] = True
                    else:
                        self.streams[monitor_id]['alarm_triggered'] = False
                
                time.sleep(0.1)  # 控制帧率
                
        except Exception as e:
            print(f"视频流处理错误: {e}")
            self.streams[monitor_id]['status'] = 'error'
        finally:
            if cap:
                cap.release()
    
    def _trigger_alarm(self, monitor_id, vehicle_count, threshold, detections):
        """触发警报"""
        try:
            # 保存警报到数据库
            alarm_data = {
                'monitor_id': monitor_id,
                'vehicle_count': vehicle_count,
                'threshold': threshold,
                'detection_details': json.dumps({
                    'total_vehicles': vehicle_count,
                    'detections': detections,
                    'timestamp': datetime.now().isoformat()
                }),
                'create_time': datetime.now()
            }
            
            print(f"🚨 触发警报: 监控点{monitor_id}, 车辆数{vehicle_count}, 阈值{threshold}")
            
            # 这里可以添加数据库保存逻辑
            # save_alarm_to_database(alarm_data)
            
        except Exception as e:
            print(f"保存警报失败: {e}")

    def _handle_violation(self, monitor_id, violation):
        """处理违规事件"""
        try:
            print(f"🚨 违规事件: 监控点{monitor_id}, 类型: {violation['type']}, 描述: {violation['description']}")

            # WebSocket推送违规事件
            pusher = get_websocket_pusher()
            if pusher:
                import asyncio
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(
                        pusher.push_violation({
                            'monitor_id': monitor_id,
                            'violation': violation,
                            'timestamp': datetime.now().isoformat()
                        })
                    )
                    loop.close()
                except Exception as e:
                    print(f"违规事件推送失败: {e}")

            # 这里可以添加数据库保存逻辑
            # save_violation_to_database(monitor_id, violation)

        except Exception as e:
            print(f"处理违规事件失败: {e}")

    def _handle_accident(self, monitor_id, accident):
        """处理事故事件"""
        try:
            print(f"🚨 事故事件: 监控点{monitor_id}, 类型: {accident['type']}, 描述: {accident['description']}")

            # WebSocket推送事故事件
            pusher = get_websocket_pusher()
            if pusher:
                import asyncio
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(
                        pusher.push_accident({
                            'monitor_id': monitor_id,
                            'accident': accident,
                            'timestamp': datetime.now().isoformat()
                        })
                    )
                    loop.close()
                except Exception as e:
                    print(f"事故事件推送失败: {e}")

            # 这里可以添加数据库保存逻辑
            # save_accident_to_database(monitor_id, accident)

        except Exception as e:
            print(f"处理事故事件失败: {e}")

    def get_stream_status(self, monitor_id):
        """获取流状态"""
        if monitor_id in self.streams:
            return self.streams[monitor_id]
        return None
    
    def get_all_streams_status(self):
        """获取所有流状态"""
        return {mid: {
            'status': stream['status'],
            'vehicle_count': stream['vehicle_count'],
            'alarm_triggered': stream['alarm_triggered'],
            'last_detection_time': stream['last_detection']['timestamp'] if stream['last_detection'] else None
        } for mid, stream in self.streams.items()}

# 全局实例
detection_engine = DetectionEngine()
stream_manager = VideoStreamManager(detection_engine)

def create_detection_app():
    """创建检测应用"""
    app = Flask(__name__)
    CORS(app, supports_credentials=True, origins="*")
    
    @app.route('/api/v1/detection/start-stream', methods=['POST'])
    def start_stream():
        """启动视频流检测"""
        try:
            data = request.get_json()
            monitor_id = data.get('monitor_id')
            stream_url = data.get('stream_url')
            config = data.get('config', {})
            
            if not monitor_id or not stream_url:
                return jsonify({
                    'code': 400,
                    'message': '缺少必要参数',
                    'success': False
                }), 400
            
            stream_manager.start_stream(monitor_id, stream_url, config)
            
            return jsonify({
                'code': 200,
                'message': '视频流启动成功',
                'data': {
                    'monitor_id': monitor_id,
                    'status': 'starting'
                },
                'success': True
            })
            
        except Exception as e:
            return jsonify({
                'code': 500,
                'message': f'启动失败: {str(e)}',
                'success': False
            }), 500
    
    @app.route('/api/v1/detection/stop-stream', methods=['POST'])
    def stop_stream():
        """停止视频流检测"""
        try:
            data = request.get_json()
            monitor_id = data.get('monitor_id')
            
            if not monitor_id:
                return jsonify({
                    'code': 400,
                    'message': '缺少监控点ID',
                    'success': False
                }), 400
            
            stream_manager.stop_stream(monitor_id)
            
            return jsonify({
                'code': 200,
                'message': '视频流停止成功',
                'success': True
            })
            
        except Exception as e:
            return jsonify({
                'code': 500,
                'message': f'停止失败: {str(e)}',
                'success': False
            }), 500
    
    @app.route('/api/v1/detection/stream-status/<int:monitor_id>', methods=['GET'])
    def get_stream_status(monitor_id):
        """获取视频流状态"""
        try:
            status = stream_manager.get_stream_status(monitor_id)
            
            if status is None:
                return jsonify({
                    'code': 404,
                    'message': '未找到该监控点的视频流',
                    'success': False
                }), 404
            
            return jsonify({
                'code': 200,
                'message': '获取状态成功',
                'data': {
                    'monitor_id': monitor_id,
                    'status': status['status'],
                    'vehicle_count': status['vehicle_count'],
                    'alarm_triggered': status['alarm_triggered'],
                    'last_detection': status['last_detection']
                },
                'success': True
            })
            
        except Exception as e:
            return jsonify({
                'code': 500,
                'message': f'获取状态失败: {str(e)}',
                'success': False
            }), 500
    
    @app.route('/api/v1/detection/all-streams-status', methods=['GET'])
    def get_all_streams_status():
        """获取所有视频流状态"""
        try:
            status = stream_manager.get_all_streams_status()
            
            return jsonify({
                'code': 200,
                'message': '获取所有流状态成功',
                'data': status,
                'success': True
            })
            
        except Exception as e:
            return jsonify({
                'code': 500,
                'message': f'获取状态失败: {str(e)}',
                'success': False
            }), 500

    @app.route('/api/v1/analysis/traffic-stats/<int:monitor_id>', methods=['GET'])
    def get_traffic_stats(monitor_id):
        """获取交通统计"""
        try:
            period = request.args.get('period', 'hourly')  # hourly, daily

            if period == 'hourly':
                stats = detection_engine.traffic_analyzer.get_hourly_statistics(monitor_id)
            elif period == 'daily':
                from datetime import date, timedelta
                start_date = date.today() - timedelta(days=7)
                end_date = date.today()
                stats = detection_engine.traffic_analyzer.get_daily_statistics(
                    monitor_id, start_date, end_date
                )
            else:
                return jsonify({
                    'code': 400,
                    'message': '无效的统计周期',
                    'success': False
                }), 400

            return jsonify({
                'code': 200,
                'message': '获取交通统计成功',
                'data': {
                    'monitor_id': monitor_id,
                    'period': period,
                    'statistics': stats
                },
                'success': True
            })

        except Exception as e:
            return jsonify({
                'code': 500,
                'message': f'获取统计失败: {str(e)}',
                'success': False
            }), 500

    @app.route('/api/v1/analysis/speed-analysis/<int:monitor_id>', methods=['GET'])
    def get_speed_analysis(monitor_id):
        """获取速度分析"""
        try:
            hours = int(request.args.get('hours', 24))

            analysis = detection_engine.traffic_analyzer.get_speed_analysis(monitor_id, hours)

            if analysis is None:
                return jsonify({
                    'code': 404,
                    'message': '暂无速度数据',
                    'success': False
                }), 404

            return jsonify({
                'code': 200,
                'message': '获取速度分析成功',
                'data': {
                    'monitor_id': monitor_id,
                    'hours': hours,
                    'analysis': analysis
                },
                'success': True
            })

        except Exception as e:
            return jsonify({
                'code': 500,
                'message': f'获取速度分析失败: {str(e)}',
                'success': False
            }), 500

    @app.route('/api/v1/detection/configure-zones/<int:monitor_id>', methods=['POST'])
    def configure_zones(monitor_id):
        """配置监控区域"""
        try:
            data = request.get_json()

            # 配置禁停区域
            if 'parking_zones' in data:
                detection_engine.violation_detector.set_parking_zones(
                    monitor_id, data['parking_zones']
                )

            # 配置车道线
            if 'lane_lines' in data:
                detection_engine.violation_detector.set_lane_lines(
                    monitor_id, data['lane_lines']
                )

            # 配置行驶方向
            if 'direction_vectors' in data:
                detection_engine.violation_detector.set_direction_vectors(
                    monitor_id, data['direction_vectors']
                )

            return jsonify({
                'code': 200,
                'message': '区域配置成功',
                'data': {
                    'monitor_id': monitor_id,
                    'configured': list(data.keys())
                },
                'success': True
            })

        except Exception as e:
            return jsonify({
                'code': 500,
                'message': f'配置失败: {str(e)}',
                'success': False
            }), 500

    return app

if __name__ == "__main__":
    print("🚀 启动核心检测服务器")
    print("="*60)
    print(f"🎯 YOLO模型: {'已加载' if detection_engine.model else '未加载'}")
    print(f"🔧 设备: {detection_engine.device}")
    print(f"📹 ByteTracker: {'可用' if BYTETRACK_AVAILABLE else '不可用'}")
    print(f"🚨 违规检测: 已集成")
    print(f"🚗 事故检测: 已集成")
    print(f"📊 交通分析: 已集成")

    # 启动WebSocket服务器
    print("\n🌐 启动WebSocket服务器...")
    ws_thread = start_websocket_server()
    print("✅ WebSocket服务器已启动 (端口: 5502)")

    print("\n📋 服务地址:")
    print("   🎯 检测API: http://127.0.0.1:5501")
    print("   🌐 WebSocket: ws://127.0.0.1:5502")

    print("\n🔧 新增功能:")
    print("   🚨 违规检测: 违停、逆行检测")
    print("   🚗 事故检测: 碰撞、急停、拥堵检测")
    print("   📊 交通分析: 流量统计、速度分析")
    print("   🌐 实时推送: WebSocket实时数据推送")

    app = create_detection_app()
    app.run(host='127.0.0.1', port=5501, debug=False)
