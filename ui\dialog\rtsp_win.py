# -*- coding: utf-8 -*-
# <AUTHOR> pan
import sys
import cv2
from PySide6.QtWidgets import QApplication, QWidget, QMessageBox, QDialog
from PySide6.QtCore import Signal
from ui.dialog.rtsp_dialog import Ui_Form
from ui.toast.toast import DialogOver


class RTSPDialog(QDialog, Ui_Form):
    id_confirmed = Signal(str)  # 添加信号用于传递RTSP地址
    
    def __init__(self):
        super(RTSPDialog, self).__init__()
        self.setupUi(self)
        self.setWindowTitle("RTSP/HTTP流地址")
        
        # 连接信号
        self.testButton.clicked.connect(self.test_connection)
        self.rtspButton.clicked.connect(self.confirm_rtsp)
        self.streamPathCombo.currentIndexChanged.connect(self.update_rtsp_path)
    
    def update_rtsp_path(self):
        # 当选择新的路径时更新完整URL
        if not self.rtspEdit.text().strip():
            return
            
        ip_port = self.rtspEdit.text().strip()
        # 如果已经是完整URL格式则不修改
        if ip_port.startswith(('rtsp://', 'http://', 'https://')):
            # 提取基本的ip:port部分
            if '://' in ip_port:
                protocol, rest = ip_port.split('://', 1)
                if '/' in rest:
                    ip_port = rest.split('/', 1)[0]
                else:
                    ip_port = rest
        
        # 获取选中的路径
        stream_path = self.streamPathCombo.currentText()
        # 组合完整的RTSP URL
        full_url = f"rtsp://{ip_port}{stream_path}"
        # 更新显示(注意我们不直接修改编辑框内容)
        print(f"已选择路径: {full_url}")
    
    def test_connection(self):
        """测试RTSP连接"""
        if not self.rtspEdit.text().strip():
            QMessageBox.warning(self, "警告", "请先输入服务器IP和端口")
            return
        
        # 构建完整URL
        ip_port = self.rtspEdit.text().strip()
        
        # 如果已经是完整URL则直接使用
        if ip_port.startswith(('rtsp://', 'http://', 'https://')):
            test_url = ip_port
        else:
            stream_path = self.streamPathCombo.currentText()
            username = self.usernameEdit.text().strip()
            password = self.passwordEdit.text().strip()
            
            # 构建包含用户名和密码的URL
            if username and password:
                test_url = f"rtsp://{username}:{password}@{ip_port}{stream_path}"
            else:
                test_url = f"rtsp://{ip_port}{stream_path}"
        
        print(f"已选择路径: {test_url}")
        
        try:
            # 尝试打开流
            print(f"测试连接: {test_url}")
            cap = cv2.VideoCapture(test_url)
            ret, frame = cap.read()
            cap.release()
            
            if ret:
                DialogOver(parent=self, text=f"连接成功! 获取到视频帧", title="测试成功", flags="success")
                # 如果测试成功，则更新编辑框
                self.rtspEdit.setText(test_url)
                return True
            else:
                DialogOver(parent=self, text=f"无法获取视频帧. 请检查URL.", title="连接失败", flags="warning")
                return False
        except Exception as e:
            DialogOver(parent=self, text=f"连接错误: {str(e)}", title="测试失败", flags="danger")
            return False
    
    def confirm_rtsp(self):
        """确认并发送RTSP地址"""
        if not self.rtspEdit.text().strip():
            QMessageBox.warning(self, "警告", "请先输入服务器IP和端口")
            return
        
        # 构建完整URL
        ip_port = self.rtspEdit.text().strip()
        if ip_port.startswith(('rtsp://', 'http://', 'https://')):
            rtsp_url = ip_port
        else:
            # 构建包含用户名和密码的URL
            stream_path = self.streamPathCombo.currentText()
            username = self.usernameEdit.text().strip()
            password = self.passwordEdit.text().strip()
            
            # 构建包含用户名和密码的URL
            if username and password:
                rtsp_url = f"rtsp://{username}:{password}@{ip_port}{stream_path}"
            else:
                rtsp_url = f"rtsp://{ip_port}{stream_path}"
        
        print(f"最新RTSP URL: {rtsp_url}")
        # 发送信号
        self.id_confirmed.emit(rtsp_url)
        self.close()
    
    def closeWindow(self):
        self.close()

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = RTSPDialog()
    window.show()
    sys.exit(app.exec())
