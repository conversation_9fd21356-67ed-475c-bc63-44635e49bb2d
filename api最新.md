# 基于Yolov8与ByteTrack的高速公路智慧监控平台 - 前端对接API文档
## 1. 认证模块 (Authentication)
### 1.1 用户登录
```
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "123456"
}
```
响应示例：

```
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": 
    "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ
    9...",
    "user": {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>",
      "avatar": "/static/avatars/
      default.png"
    }
  }
}
```
### 1.2 用户注册
```
POST /api/v1/auth/register
Content-Type: application/json

{
  "username": "newuser",
  "password": "password123",
  "email": "<EMAIL>"
}
```
### 1.3 获取用户信息
```
GET /api/v1/auth/profile
Authorization: Bearer <token>
```
### 1.4 修改密码
```
PUT /api/v1/auth/change-password
Authorization: Bearer <token>
Content-Type: application/json

{
  "old_password": "oldpass",
  "new_password": "newpass"
}
```
## 2. 监控点管理 (Monitor Management)
### 2.1 获取监控点列表
```
GET /api/v1/monitor/list?page=1&size=20&
location=高速路段A
Authorization: Bearer <token>
```
响应示例：

```
{
  "code": 200,
  "data": {
    "monitors": [
      {
        "id": 1,
        "name": "监控点001",
        "location": "高速路段A",
        "road_section": "K100+500",
        "rtsp_url": "rtsp://192.168.1.
        100:554/stream",
        "status": "online",
        "is_alarm": "开启",
        "threshold": 50,
        "person": 10,
        "video": 5,
        "url": "http://example.com/
        stream"
      }
    ],
    "total": 100,
    "page": 1,
    "size": 20
  }
}
```
### 2.2 创建监控点
```
POST /api/v1/monitor
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "新监控点",
  "location": "高速路段B",
  "road_section": "K200+300",
  "rtsp_url": "rtsp://192.168.1.101:554/
  stream",
  "threshold": 60,
  "person": 15,
  "video": 8,
  "url": "http://example.com/stream2"
}
```
### 2.3 更新监控点
```
PUT /api/v1/monitor/{monitor_id}
Authorization: Bearer <token>
Content-Type: application/json
```
### 2.4 删除监控点
```
DELETE /api/v1/monitor/{monitor_id}
Authorization: Bearer <token>
```
### 2.5 测试监控点连接
```
POST /api/v1/monitor/{monitor_id}/
test-connection
Authorization: Bearer <token>
```
### 2.6 启动/停止监控
```
POST /api/v1/monitor/{monitor_id}/start
POST /api/v1/monitor/{monitor_id}/stop
Authorization: Bearer <token>
```
## 3. YOLO检测模块 (Detection)
### 3.1 图像检测
```
POST /api/v1/detection/image
Authorization: Bearer <token>
Content-Type: multipart/form-data

file: <image_file>
confidence: 0.5
iou_threshold: 0.4
show_labels: true
```
响应示例：

```
{
  "code": 200,
  "data": {
    "original_image": "/uploads/
    original_20231201_123456.jpg",
    "result_image": "/results/
    result_20231201_123456.jpg",
    "detections": {
      "car": 15,
      "truck": 3,
      "person": 2
    },
    "total_objects": 20,
    "processing_time": 0.85,
    "confidence": 0.5,
    "iou_threshold": 0.4
  }
}
```
### 3.2 Base64图像检测
```
POST /api/v1/detection/base64
Authorization: Bearer <token>
Content-Type: application/json

{
  "image_data": "data:image/jpeg;
  base64,/9j/4AAQSkZJRgABAQAAAQ...",
  "confidence": 0.6,
  "iou_threshold": 0.5,
  "show_labels": true
}
```
### 3.3 视频检测
```
POST /api/v1/detection/video
Authorization: Bearer <token>
Content-Type: multipart/form-data

file: <video_file>
confidence: 0.5
iou_threshold: 0.4
show_labels: true
save_result: true
```
### 3.4 RTSP流检测
```
POST /api/v1/detection/rtsp
Authorization: Bearer <token>
Content-Type: application/json

{
  "rtsp_url": "rtsp://*************:554/
  stream",
  "confidence": 0.5,
  "iou_threshold": 0.4,
  "show_labels": true
}
```
### 3.5 停止RTSP检测
```
POST /api/v1/detection/rtsp/{task_id}/
stop
Authorization: Bearer <token>
```
### 3.6 获取检测任务列表
```
GET /api/v1/detection/tasks
Authorization: Bearer <token>
```
### 3.7 获取可用模型
```
GET /api/v1/detection/models
Authorization: Bearer <token>
```
### 3.8 切换检测模型
```
POST /api/v1/detection/model
Authorization: Bearer <token>
Content-Type: application/json

{
  "model_name": "yolov8n.pt"
}
```
## 4. 数据分析模块 (Analysis)
### 4.1 获取警报列表
```
GET /api/v1/analysis/alarms?page=1&
size=20&location=高速路段A&
start_date=2023-12-01&
end_date=2023-12-31
Authorization: Bearer <token>
```
### 4.2 获取概览统计
```
GET /api/v1/analysis/statistics/
overview?days=7
Authorization: Bearer <token>
```
响应示例：

```
{
  "code": 200,
  "data": {
    "total_monitors": 50,
    "active_monitors": 45,
    "total_alarms": 1250,
    "today_alarms": 35,
    "avg_vehicles": 125.5,
    "max_vehicles": 280
  }
}
```
### 4.3 获取交通流量统计
```
GET /api/v1/analysis/statistics/
traffic-flow?days=30&road_section=K100
+500
Authorization: Bearer <token>
```
### 4.4 获取小时分布统计
```
GET /api/v1/analysis/statistics/
hourly-distribution?days=7
Authorization: Bearer <token>
```
### 4.5 获取地点排行
```
GET /api/v1/analysis/statistics/
location-ranking
Authorization: Bearer <token>
```
### 4.6 获取热力图数据
```
GET /api/v1/analysis/heatmap
Authorization: Bearer <token>
```
### 4.7 导出警报数据
```
POST /api/v1/analysis/export/alarms
Authorization: Bearer <token>
Content-Type: application/json

{
  "format": "excel",
  "start_date": "2023-12-01",
  "end_date": "2023-12-31",
  "location": "高速路段A"
}
```
## 5. 事故检测模块 (Accident Detection)
### 5.1 配置事故检测参数
```
POST /api/v1/accident/configure
Authorization: Bearer <token>
Content-Type: application/json

{
  "monitor_id": 1,
  "enable_collision": true,
  "enable_sudden_stop": true,
  "enable_congestion": true,
  "collision_threshold": 0.8,
  "sudden_stop_threshold": 5.0,
  "congestion_density_threshold": 0.7,
  "congestion_speed_threshold": 20,
  "alert_cooldown": 300,
  "confidence_threshold": 0.85
}
```
### 5.2 获取事故统计数据
```
GET /api/v1/accident/statistics?
monitor_id=1&start_date=2023-12-01&
end_date=2023-12-31
Authorization: Bearer <token>
```
响应示例：

```
{
  "code": 200,
  "data": {
    "statistics": [
      {
        "accident_type": "collision",
        "severity_level": "high",
        "count": 15,
        "avg_response_time": 8.5,
        "monitor_name": "监控点001"
      }
    ],
    "total_stats": {
      "total_accidents": 45,
      "affected_monitors": 12,
      "resolved_count": 40,
      "avg_response_time": 12.3
    },
    "severity_distribution": [
      {"severity_level": "critical", 
      "count": 5},
      {"severity_level": "high", 
      "count": 15},
      {"severity_level": "medium", 
      "count": 20},
      {"severity_level": "low", 
      "count": 5}
    ]
  }
}
```
### 5.3 获取事故记录列表
```
GET /api/v1/accident/records?page=1&
size=20&accident_type=collision&
severity_level=high
Authorization: Bearer <token>
```
### 5.4 配置事故预警
```
POST /api/v1/accident/alert-config
Authorization: Bearer <token>
Content-Type: application/json

{
  "monitor_id": 1,
  "enable_email_alerts": true,
  "enable_sms_alerts": false,
  "enable_webhook_alerts": true,
  "alert_recipients": ["admin@example.
  com"],
  "severity_threshold": "medium",
  "alert_interval": 300,
  "webhook_url": "https://webhook.
  example.com/alerts"
}
```
## 6. 实时监控模块 (Real-time)
### 6.1 获取实时数据
```
GET /api/v1/realtime/data/{monitor_id}
Authorization: Bearer <token>
```
### 6.2 WebSocket连接
```
// WebSocket连接示例
const socket = io('ws://
localhost:5000', {
  auth: {
    token: 'your_jwt_token'
  }
});

// 监听实时数据
socket.on('detection_result', (data) => 
{
  console.log('检测结果:', data);
});

// 监听警报
socket.on('alarm_triggered', (data) => {
  console.log('警报触发:', data);
});
```
## 7. 系统管理模块 (System)
### 7.1 获取系统状态
```
GET /api/v1/system/status
Authorization: Bearer <token>
```
### 7.2 获取系统配置
```
GET /api/v1/system/config
Authorization: Bearer <token>
```
### 7.3 更新系统配置
```
PUT /api/v1/system/config
Authorization: Bearer <token>
Content-Type: application/json
```
## 8. 违章检测模块 (Violation)
### 8.1 获取违章记录
```
GET /api/v1/violation/records?page=1&
size=20&violation_type=speeding
Authorization: Bearer <token>
```
### 8.2 配置违章检测
```
POST /api/v1/violation/configure
Authorization: Bearer <token>
Content-Type: application/json

{
  "monitor_id": 1,
  "enable_speeding": true,
  "enable_lane_change": true,
  "speed_limit": 120,
  "detection_sensitivity": 0.8
}
```
## 9. 目标追踪模块 (Tracking)
### 9.1 启动目标追踪
```
POST /api/v1/tracking/start
Authorization: Bearer <token>
Content-Type: application/json

{
  "monitor_id": 1,
  "tracking_algorithm": "bytetrack",
  "confidence_threshold": 0.6
}
```
### 9.2 获取追踪结果
```
GET /api/v1/tracking/results/{task_id}
Authorization: Bearer <token>
```