# 第二步：认证系统实现

## 🎯 目标
实现完整的用户认证系统，包括登录页面、状态管理、API封装和路由守卫。

## 📋 实现内容

### 1. API请求封装 (src/utils/request.ts)

```typescript
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { Message } from '@arco-design/web-vue'
import { useAuthStore } from '@/stores/auth'
import router from '@/router'

// 创建axios实例
const request: AxiosInstance = axios.create({
  baseURL: '/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    const authStore = useAuthStore()
    
    // 添加认证token
    if (authStore.token) {
      config.headers = {
        ...config.headers,
        Authorization: `Bearer ${authStore.token}`
      }
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse) => {
    const { data } = response
    
    // 统一处理响应格式
    if (data.success === false) {
      Message.error(data.message || '请求失败')
      return Promise.reject(new Error(data.message || '请求失败'))
    }
    
    return data
  },
  (error) => {
    const { response } = error
    
    if (response) {
      switch (response.status) {
        case 401:
          Message.error('登录已过期，请重新登录')
          const authStore = useAuthStore()
          authStore.logout()
          router.push('/login')
          break
        case 403:
          Message.error('权限不足')
          break
        case 404:
          Message.error('请求的资源不存在')
          break
        case 500:
          Message.error('服务器内部错误')
          break
        default:
          Message.error(response.data?.message || '请求失败')
      }
    } else {
      Message.error('网络连接失败')
    }
    
    return Promise.reject(error)
  }
)

export default request
```

### 2. 认证API (src/api/auth.ts)

```typescript
import request from '@/utils/request'
import type { ApiResponse, LoginResponse, User } from '@/types'

export interface LoginParams {
  username: string
  password: string
}

export interface RegisterParams {
  username: string
  password: string
  email: string
  role?: string
}

export interface ChangePasswordParams {
  old_password: string
  new_password: string
}

// 用户登录
export const login = (params: LoginParams): Promise<ApiResponse<LoginResponse>> => {
  return request.post('/auth/login', params)
}

// 用户注册
export const register = (params: RegisterParams): Promise<ApiResponse<User>> => {
  return request.post('/auth/register', params)
}

// 获取用户信息
export const getUserProfile = (): Promise<ApiResponse<User>> => {
  return request.get('/auth/profile')
}

// 修改密码
export const changePassword = (params: ChangePasswordParams): Promise<ApiResponse> => {
  return request.put('/auth/change-password', params)
}

// 用户登出
export const logout = (): Promise<ApiResponse> => {
  return request.post('/auth/logout')
}
```

### 3. 认证状态管理 (src/stores/auth.ts)

```typescript
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { Message } from '@arco-design/web-vue'
import * as authApi from '@/api/auth'
import type { User, LoginResponse } from '@/types'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string>(localStorage.getItem('token') || '')
  const user = ref<User | null>(null)
  const loading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value)
  const userRole = computed(() => user.value?.role || '')
  const userName = computed(() => user.value?.username || '')

  // 登录
  const login = async (username: string, password: string): Promise<boolean> => {
    try {
      loading.value = true
      const response = await authApi.login({ username, password })
      
      if (response.success && response.data) {
        const { token: authToken, user: userData } = response.data
        
        // 保存token和用户信息
        token.value = authToken
        user.value = userData
        localStorage.setItem('token', authToken)
        localStorage.setItem('user', JSON.stringify(userData))
        
        Message.success('登录成功')
        return true
      }
      
      return false
    } catch (error) {
      console.error('登录失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  // 注册
  const register = async (params: authApi.RegisterParams): Promise<boolean> => {
    try {
      loading.value = true
      const response = await authApi.register(params)
      
      if (response.success) {
        Message.success('注册成功，请登录')
        return true
      }
      
      return false
    } catch (error) {
      console.error('注册失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  // 获取用户信息
  const fetchUserProfile = async (): Promise<void> => {
    try {
      const response = await authApi.getUserProfile()
      
      if (response.success && response.data) {
        user.value = response.data
        localStorage.setItem('user', JSON.stringify(response.data))
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 如果获取用户信息失败，可能token已过期
      logout()
    }
  }

  // 修改密码
  const changePassword = async (oldPassword: string, newPassword: string): Promise<boolean> => {
    try {
      loading.value = true
      const response = await authApi.changePassword({
        old_password: oldPassword,
        new_password: newPassword
      })
      
      if (response.success) {
        Message.success('密码修改成功')
        return true
      }
      
      return false
    } catch (error) {
      console.error('修改密码失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = async (): Promise<void> => {
    try {
      // 调用后端登出接口
      await authApi.logout()
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清除本地存储
      token.value = ''
      user.value = null
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      
      Message.success('已退出登录')
    }
  }

  // 初始化用户信息
  const initAuth = (): void => {
    const savedUser = localStorage.getItem('user')
    if (savedUser && token.value) {
      try {
        user.value = JSON.parse(savedUser)
        // 验证token有效性
        fetchUserProfile()
      } catch (error) {
        console.error('解析用户信息失败:', error)
        logout()
      }
    }
  }

  return {
    // 状态
    token,
    user,
    loading,
    
    // 计算属性
    isAuthenticated,
    userRole,
    userName,
    
    // 方法
    login,
    register,
    fetchUserProfile,
    changePassword,
    logout,
    initAuth
  }
})
```

### 4. 登录页面 (src/views/Login/index.vue)

```vue
<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <h1>基于Yolov8与ByteTrack的高速公路智慧监控平台</h1>
        <p>基于YOLOv8与ByteTrack的智能监控平台</p>
      </div>
      
      <a-form
        ref="formRef"
        :model="form"
        :rules="rules"
        layout="vertical"
        @submit="handleSubmit"
      >
        <a-form-item field="username" label="用户名">
          <a-input
            v-model="form.username"
            placeholder="请输入用户名"
            size="large"
            allow-clear
          >
            <template #prefix>
              <icon-user />
            </template>
          </a-input>
        </a-form-item>
        
        <a-form-item field="password" label="密码">
          <a-input-password
            v-model="form.password"
            placeholder="请输入密码"
            size="large"
            allow-clear
          >
            <template #prefix>
              <icon-lock />
            </template>
          </a-input-password>
        </a-form-item>
        
        <a-form-item>
          <a-checkbox v-model="form.remember">记住密码</a-checkbox>
        </a-form-item>
        
        <a-form-item>
          <a-button
            type="primary"
            html-type="submit"
            size="large"
            long
            :loading="authStore.loading"
          >
            登录
          </a-button>
        </a-form-item>
      </a-form>
      
      <div class="login-footer">
        <a-link @click="showRegister = true">还没有账号？立即注册</a-link>
      </div>
    </div>
    
    <!-- 注册弹窗 -->
    <a-modal
      v-model:visible="showRegister"
      title="用户注册"
      @ok="handleRegister"
      @cancel="resetRegisterForm"
    >
      <a-form
        ref="registerFormRef"
        :model="registerForm"
        :rules="registerRules"
        layout="vertical"
      >
        <a-form-item field="username" label="用户名">
          <a-input
            v-model="registerForm.username"
            placeholder="请输入用户名"
            allow-clear
          />
        </a-form-item>
        
        <a-form-item field="email" label="邮箱">
          <a-input
            v-model="registerForm.email"
            placeholder="请输入邮箱"
            allow-clear
          />
        </a-form-item>
        
        <a-form-item field="password" label="密码">
          <a-input-password
            v-model="registerForm.password"
            placeholder="请输入密码"
            allow-clear
          />
        </a-form-item>
        
        <a-form-item field="confirmPassword" label="确认密码">
          <a-input-password
            v-model="registerForm.confirmPassword"
            placeholder="请再次输入密码"
            allow-clear
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Message } from '@arco-design/web-vue'
import { IconUser, IconLock } from '@arco-design/web-vue-icons'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

// 表单引用
const formRef = ref()
const registerFormRef = ref()

// 登录表单
const form = reactive({
  username: 'admin',
  password: '123456',
  remember: false
})

// 注册表单
const registerForm = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: ''
})

// 显示注册弹窗
const showRegister = ref(false)

// 登录表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入用户名' },
    { minLength: 3, message: '用户名至少3个字符' }
  ],
  password: [
    { required: true, message: '请输入密码' },
    { minLength: 6, message: '密码至少6个字符' }
  ]
}

// 注册表单验证规则
const registerRules = {
  username: [
    { required: true, message: '请输入用户名' },
    { minLength: 3, message: '用户名至少3个字符' }
  ],
  email: [
    { required: true, message: '请输入邮箱' },
    { type: 'email', message: '请输入有效的邮箱地址' }
  ],
  password: [
    { required: true, message: '请输入密码' },
    { minLength: 6, message: '密码至少6个字符' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码' },
    {
      validator: (value: string, callback: Function) => {
        if (value !== registerForm.password) {
          callback('两次输入的密码不一致')
        } else {
          callback()
        }
      }
    }
  ]
}

// 处理登录
const handleSubmit = async ({ errors }: { errors: any }) => {
  if (errors) return
  
  const success = await authStore.login(form.username, form.password)
  if (success) {
    router.push('/dashboard')
  }
}

// 处理注册
const handleRegister = async () => {
  const errors = await registerFormRef.value?.validate()
  if (errors) return
  
  const success = await authStore.register({
    username: registerForm.username,
    email: registerForm.email,
    password: registerForm.password
  })
  
  if (success) {
    showRegister.value = false
    resetRegisterForm()
  }
}

// 重置注册表单
const resetRegisterForm = () => {
  registerForm.username = ''
  registerForm.email = ''
  registerForm.password = ''
  registerForm.confirmPassword = ''
  registerFormRef.value?.resetFields()
}

// 组件挂载时检查登录状态
onMounted(() => {
  if (authStore.isAuthenticated) {
    router.push('/dashboard')
  }
})
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-box {
  width: 100%;
  max-width: 480px;
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-header h1 {
  font-size: 24px;
  font-weight: 600;
  color: #1d2129;
  margin-bottom: 8px;
}

.login-header p {
  font-size: 14px;
  color: #86909c;
}

.login-footer {
  text-align: center;
  margin-top: 24px;
}

:deep(.arco-form-item) {
  margin-bottom: 20px;
}

:deep(.arco-input-wrapper) {
  border-radius: 8px;
}

:deep(.arco-btn) {
  border-radius: 8px;
  height: 44px;
}
</style>
```

## ✅ 验证步骤

1. **测试登录功能**
   - 使用正确的用户名密码登录
   - 测试错误的用户名密码
   - 检查token是否正确保存

2. **测试路由守卫**
   - 未登录时访问受保护页面应跳转到登录页
   - 已登录时访问登录页应跳转到仪表板

3. **测试状态持久化**
   - 刷新页面后登录状态应保持
   - 清除localStorage后应跳转到登录页

## 🎯 下一步

认证系统完成后，下一步将实现：
1. **布局组件** - 主要的应用布局结构
2. **仪表板页面** - 系统概览和统计信息
3. **监控点管理** - 第一个业务功能模块

**准备好进行下一步了吗？**
