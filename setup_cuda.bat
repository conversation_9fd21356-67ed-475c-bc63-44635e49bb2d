@echo off
echo Setting up CUDA environment for YOLOv8 project

REM Activate the conda environment
echo Activating yolo_env environment...
call conda activate yolo_env

REM Install CUDA-enabled PyTorch
echo Installing CUDA-enabled PyTorch packages...
pip install "C:\Users\<USER>\Downloads\torch-1.13.1+cu117-cp39-cp39-win_amd64.whl" "C:\Users\<USER>\Downloads\torchvision-0.14.1+cu117-cp39-cp39-win_amd64.whl"

REM Install other requirements
echo Installing other dependencies...
pip install -r requirements.txt

REM Test CUDA availability
echo Testing CUDA availability...
python -c "import torch; print('CUDA available:', torch.cuda.is_available()); print('CUDA device count:', torch.cuda.device_count()); print('CUDA device name:', torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'N/A')"

echo Setup complete!
pause
