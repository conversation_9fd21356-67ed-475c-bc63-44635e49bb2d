# @Description : 算法参数配置服务
# @Date : 2025年6月20日

import json
import threading
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
from utils.database import get_db_connection

class ConfigService:
    """算法参数配置服务类"""
    
    def __init__(self):
        self.config_cache = {}  # 配置缓存
        self.test_results = {}  # 测试结果缓存
        
        # 配置验证规则
        self.validation_rules = {
            'violation': {
                'speed_limit': {'type': 'int', 'min': 20, 'max': 200},
                'speed_threshold_ratio': {'type': 'float', 'min': 1.0, 'max': 3.0},
                'confidence_threshold': {'type': 'float', 'min': 0.1, 'max': 1.0},
                'lane_change_threshold': {'type': 'int', 'min': 1, 'max': 10},
                'parking_time_threshold': {'type': 'int', 'min': 60, 'max': 3600}
            },
            'accident': {
                'collision_threshold': {'type': 'float', 'min': 0.1, 'max': 1.0},
                'sudden_stop_threshold': {'type': 'float', 'min': 1.0, 'max': 20.0},
                'congestion_density_threshold': {'type': 'float', 'min': 0.1, 'max': 1.0},
                'congestion_speed_threshold': {'type': 'int', 'min': 5, 'max': 60},
                'alert_cooldown': {'type': 'int', 'min': 60, 'max': 1800}
            },
            'tracking': {
                'track_thresh': {'type': 'float', 'min': 0.1, 'max': 0.9},
                'track_buffer': {'type': 'int', 'min': 10, 'max': 100},
                'match_thresh': {'type': 'float', 'min': 0.1, 'max': 0.9},
                'max_age': {'type': 'int', 'min': 1, 'max': 10},
                'min_hits': {'type': 'int', 'min': 1, 'max': 10}
            }
        }
    
    def apply_config_changes(self, monitor_id: int, config_type: str, algorithm_name: str, config_data: Dict[str, Any]):
        """应用配置更改"""
        try:
            # 更新缓存
            cache_key = f"{monitor_id}_{config_type}_{algorithm_name}"
            self.config_cache[cache_key] = {
                'config_data': config_data,
                'update_time': datetime.now(),
                'monitor_id': monitor_id,
                'config_type': config_type,
                'algorithm_name': algorithm_name
            }
            
            # 这里可以添加实际的配置应用逻辑
            # 例如通知相关服务更新配置
            
            return True
        except Exception as e:
            print(f"应用配置更改失败: {str(e)}")
            return False
    
    def validate_config(self, config_type: str, algorithm_name: str, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """验证配置参数"""
        try:
            validation_result = {
                'valid': True,
                'errors': [],
                'warnings': []
            }
            
            # 获取验证规则
            rules = self.validation_rules.get(config_type, {})
            
            for param_name, param_value in config_data.items():
                if param_name in rules:
                    rule = rules[param_name]
                    
                    # 类型检查
                    if rule['type'] == 'int':
                        if not isinstance(param_value, int):
                            try:
                                param_value = int(param_value)
                            except ValueError:
                                validation_result['errors'].append(
                                    f"参数 {param_name} 必须是整数类型"
                                )
                                validation_result['valid'] = False
                                continue
                    
                    elif rule['type'] == 'float':
                        if not isinstance(param_value, (int, float)):
                            try:
                                param_value = float(param_value)
                            except ValueError:
                                validation_result['errors'].append(
                                    f"参数 {param_name} 必须是数字类型"
                                )
                                validation_result['valid'] = False
                                continue
                    
                    # 范围检查
                    if 'min' in rule and param_value < rule['min']:
                        validation_result['errors'].append(
                            f"参数 {param_name} 的值 {param_value} 小于最小值 {rule['min']}"
                        )
                        validation_result['valid'] = False
                    
                    if 'max' in rule and param_value > rule['max']:
                        validation_result['errors'].append(
                            f"参数 {param_name} 的值 {param_value} 大于最大值 {rule['max']}"
                        )
                        validation_result['valid'] = False
                else:
                    validation_result['warnings'].append(
                        f"未知参数: {param_name}"
                    )
            
            return validation_result
            
        except Exception as e:
            return {
                'valid': False,
                'errors': [f"验证过程出错: {str(e)}"],
                'warnings': []
            }
    
    def test_config(self, monitor_id: int, config_type: str, algorithm_name: str, 
                   config_data: Dict[str, Any], test_duration: int) -> Dict[str, Any]:
        """测试配置参数"""
        try:
            test_id = f"{monitor_id}_{config_type}_{algorithm_name}_{int(time.time())}"
            
            # 启动测试线程
            test_thread = threading.Thread(
                target=self._test_worker,
                args=(test_id, monitor_id, config_type, algorithm_name, config_data, test_duration)
            )
            test_thread.daemon = True
            test_thread.start()
            
            return {
                'test_id': test_id,
                'status': 'running',
                'start_time': datetime.now().isoformat(),
                'estimated_duration': test_duration
            }
            
        except Exception as e:
            return {
                'test_id': None,
                'status': 'failed',
                'error': str(e)
            }
    
    def _test_worker(self, test_id: str, monitor_id: int, config_type: str, 
                    algorithm_name: str, config_data: Dict[str, Any], test_duration: int):
        """配置测试工作线程"""
        try:
            start_time = time.time()
            
            # 初始化测试结果
            self.test_results[test_id] = {
                'status': 'running',
                'start_time': datetime.now(),
                'progress': 0,
                'metrics': {}
            }
            
            # 模拟测试过程
            while time.time() - start_time < test_duration:
                elapsed = time.time() - start_time
                progress = min(100, int((elapsed / test_duration) * 100))
                
                self.test_results[test_id]['progress'] = progress
                
                # 模拟收集性能指标
                self.test_results[test_id]['metrics'] = {
                    'accuracy': 0.85 + (progress / 1000),  # 模拟准确率
                    'fps': 25 + (progress / 10),           # 模拟帧率
                    'cpu_usage': 40 + (progress / 5),      # 模拟CPU使用率
                    'memory_usage': 150 + (progress / 2),  # 模拟内存使用量
                    'error_count': max(0, 10 - progress // 10)  # 模拟错误数量
                }
                
                time.sleep(1)
            
            # 测试完成
            self.test_results[test_id]['status'] = 'completed'
            self.test_results[test_id]['end_time'] = datetime.now()
            self.test_results[test_id]['progress'] = 100
            
            # 生成测试报告
            self.test_results[test_id]['report'] = self._generate_test_report(
                config_type, algorithm_name, config_data, self.test_results[test_id]['metrics']
            )
            
        except Exception as e:
            self.test_results[test_id]['status'] = 'failed'
            self.test_results[test_id]['error'] = str(e)
    
    def _generate_test_report(self, config_type: str, algorithm_name: str, 
                             config_data: Dict[str, Any], metrics: Dict[str, Any]) -> Dict[str, Any]:
        """生成测试报告"""
        return {
            'config_type': config_type,
            'algorithm_name': algorithm_name,
            'config_data': config_data,
            'performance_metrics': metrics,
            'recommendations': self._generate_recommendations(config_type, metrics),
            'overall_score': self._calculate_overall_score(metrics)
        }
    
    def _generate_recommendations(self, config_type: str, metrics: Dict[str, Any]) -> List[str]:
        """生成配置建议"""
        recommendations = []
        
        if metrics.get('accuracy', 0) < 0.8:
            recommendations.append("建议提高置信度阈值以提升准确率")
        
        if metrics.get('fps', 0) < 20:
            recommendations.append("建议降低处理复杂度以提升帧率")
        
        if metrics.get('cpu_usage', 0) > 80:
            recommendations.append("建议优化算法参数以降低CPU使用率")
        
        if metrics.get('error_count', 0) > 5:
            recommendations.append("建议检查配置参数，存在较多错误")
        
        return recommendations
    
    def _calculate_overall_score(self, metrics: Dict[str, Any]) -> float:
        """计算综合评分"""
        accuracy_score = metrics.get('accuracy', 0) * 40
        fps_score = min(metrics.get('fps', 0) / 30, 1) * 30
        cpu_score = max(0, (100 - metrics.get('cpu_usage', 100)) / 100) * 20
        error_score = max(0, (10 - metrics.get('error_count', 10)) / 10) * 10
        
        return accuracy_score + fps_score + cpu_score + error_score
    
    def get_test_result(self, test_id: str) -> Dict[str, Any]:
        """获取测试结果"""
        return self.test_results.get(test_id, {'status': 'not_found'})