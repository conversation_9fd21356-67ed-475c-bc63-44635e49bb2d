# -*- coding: utf-8 -*-
# @Description : 认证相关API接口
# @Date : 2025年6月20日

import jwt
import hashlib
import os
import pymysql
from datetime import datetime, timedelta
from functools import wraps
from flask import request, jsonify, current_app, session
from . import api_v1
from utils.response import success_response, error_response
from utils.database import get_db_connection

def token_required(f):
    """JWT令牌验证装饰器"""
    @wraps(f)
    def decorated(*args, **kwargs):
        token = request.headers.get('Authorization')
        if not token:
            return error_response('缺少访问令牌', 401)
        
        try:
            if token.startswith('Bearer '):
                token = token[7:]
            data = jwt.decode(token, current_app.config['SECRET_KEY'], algorithms=['HS256'])
            current_user_id = data['user_id']
        except jwt.ExpiredSignatureError:
            return error_response('令牌已过期', 401)
        except jwt.InvalidTokenError:
            return error_response('无效的令牌', 401)
        
        return f(current_user_id, *args, **kwargs)
    return decorated

@api_v1.route('/auth/login', methods=['POST'])
def login():
    """用户登录"""
    print("=" * 50)
    print("🔍 登录函数开始执行 - 这是修改后的函数")
    print("=" * 50)

    try:
        print("1. 获取请求数据...")
        data = request.get_json()
        print(f"   请求数据: {data}")

        username = data.get('username')
        password = data.get('password')
        print(f"   用户名: {username}, 密码: {password}")

        if not username or not password:
            print("   ❌ 用户名或密码为空")
            return error_response('用户名和密码不能为空')

        print("2. 准备数据库连接...")
        import pymysql

        # 使用硬编码配置（确保连接成功）
        config = {
            'host': '127.0.0.1',
            'port': 3306,
            'user': 'root',
            'password': '123456',
            'database': 'yolo',
            'charset': 'utf8mb4',
            'autocommit': True,
            'cursorclass': pymysql.cursors.DictCursor
        }
        print(f"   数据库配置: {config}")

        print("3. 执行数据库查询...")
        try:
            connection = pymysql.connect(**config)
            print("   ✅ 数据库连接成功")

            with connection.cursor() as cursor:
                print("   ✅ 游标创建成功")
                cursor.execute(
                    "SELECT * FROM user WHERE username=%s AND password=%s",
                    (username, password)
                )
                print("   ✅ SQL执行成功")
                user = cursor.fetchone()
                print(f"   查询结果: {user}")

            connection.close()
            print("   ✅ 数据库连接关闭")

        except Exception as db_error:
            print(f"   ❌ 数据库操作失败: {str(db_error)}")
            return error_response(f'数据库连接失败: {str(db_error)}')

        if not user:
            print("   ❌ 用户验证失败")
            return error_response('用户名或密码错误')

        print("4. 生成JWT令牌...")
        try:
            token = jwt.encode({
                'user_id': user['id'],
                'username': user['username'],
                'exp': datetime.utcnow() + timedelta(hours=24)
            }, current_app.config['SECRET_KEY'], algorithm='HS256')
            print(f"   ✅ Token生成成功: {token[:30]}...")
        except Exception as jwt_error:
            print(f"   ❌ Token生成失败: {str(jwt_error)}")
            return error_response(f'Token生成失败: {str(jwt_error)}')

        print("5. 设置session...")
        try:
            session['user_id'] = user['id']
            session['username'] = user['username']
            print("   ✅ Session设置成功")
        except Exception as session_error:
            print(f"   ❌ Session设置失败: {str(session_error)}")

        print("6. 返回成功响应...")
        return success_response({
            'token': token,
            'user': {
                'id': user['id'],
                'username': user['username'],
                'email': user['email'],
                'grade': user['grade'],
                'avatar': user['avatar']
            }
        }, '登录成功')

    except Exception as e:
        print(f"❌ 登录函数异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return error_response(f'登录失败: {str(e)}')

@api_v1.route('/auth/logout', methods=['POST'])
@token_required
def logout(current_user_id):
    """用户登出"""
    try:
        session.clear()
        return success_response(None, '登出成功')
    except Exception as e:
        return error_response(f'登出失败: {str(e)}')

@api_v1.route('/auth/register', methods=['POST'])
def register():
    """用户注册"""
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        email = data.get('email')
        
        if not all([username, password, email]):
            return error_response('用户名、密码和邮箱不能为空')
        
        # 检查用户是否已存在 - 临时禁用
        # with get_db_connection() as db:
        # 临时禁用注册功能
        return error_response('注册功能暂时关闭')
            # existing_user = db.get_one(
            #     "SELECT id FROM user WHERE username=%s OR email=%s",
            #     (username, email)
            # )
            #
            # if existing_user:
            #     return error_response('用户名或邮箱已存在')
            #
            # # 密码加密
            # password_hash = hashlib.md5(password.encode()).hexdigest()
            #
            # # 创建用户
            # user_id = db.create(
            #     """INSERT INTO user (username, password, email, avatar, grade, create_time, create_by)
            #        VALUES (%s, %s, %s, %s, %s, %s, %s)""",
            #     (username, password_hash, email, 'default_avatar.jpg', '普通用户',
            #      datetime.now(), 'system')
            # )
        
        return success_response({'user_id': user_id}, '注册成功')
        
    except Exception as e:
        return error_response(f'注册失败: {str(e)}')

@api_v1.route('/auth/profile', methods=['GET'])
@token_required
def get_profile(current_user_id):
    """获取用户信息"""
    try:
        # 使用直接数据库连接
        import pymysql
        config = {
            'host': '127.0.0.1',
            'port': 3306,
            'user': 'root',
            'password': '123456',
            'database': 'yolo',
            'charset': 'utf8mb4',
            'autocommit': True,
            'cursorclass': pymysql.cursors.DictCursor
        }
        connection = pymysql.connect(**config)
        with connection.cursor() as cursor:
            cursor.execute(
                "SELECT id, username, email, avatar, grade, create_time FROM user WHERE id=%s",
                (current_user_id,)
            )
            user = cursor.fetchone()
        connection.close()
        
        if not user:
            return error_response('用户不存在')
        
        return success_response(user, '获取用户信息成功')
        
    except Exception as e:
        return error_response(f'获取用户信息失败: {str(e)}')

@api_v1.route('/auth/profile', methods=['PUT'])
@token_required
def update_profile(current_user_id):
    """更新用户信息"""
    try:
        data = request.get_json()
        email = data.get('email')
        avatar = data.get('avatar')
        
        with get_db_connection() as db:
            db.modify(
                "UPDATE user SET email=%s, avatar=%s WHERE id=%s",
                (email, avatar, current_user_id)
            )
        
        return success_response(None, '更新用户信息成功')
        
    except Exception as e:
        return error_response(f'更新用户信息失败: {str(e)}')

@api_v1.route('/auth/change-password', methods=['POST'])
@token_required
def change_password(current_user_id):
    """修改密码"""
    try:
        data = request.get_json()
        old_password = data.get('old_password')
        new_password = data.get('new_password')
        
        if not all([old_password, new_password]):
            return error_response('旧密码和新密码不能为空')
        
        # 验证旧密码
        old_password_hash = hashlib.md5(old_password.encode()).hexdigest()
        new_password_hash = hashlib.md5(new_password.encode()).hexdigest()
        
        # 使用直接数据库连接
        import pymysql
        config = {
            'host': '127.0.0.1',
            'port': 3306,
            'user': 'root',
            'password': '123456',
            'database': 'yolo',
            'charset': 'utf8mb4',
            'autocommit': True,
            'cursorclass': pymysql.cursors.DictCursor
        }
        connection = pymysql.connect(**config)
        with connection.cursor() as cursor:
            cursor.execute(
                "SELECT id FROM user WHERE id=%s AND password=%s",
                (current_user_id, old_password_hash)
            )
            user = cursor.fetchone()

            if not user:
                connection.close()
                return error_response('旧密码错误')

            # 更新密码
            cursor.execute(
                "UPDATE user SET password=%s WHERE id=%s",
                (new_password_hash, current_user_id)
            )
        connection.close()
        
        return success_response(None, '密码修改成功')
        
    except Exception as e:
        return error_response(f'密码修改失败: {str(e)}')
