# 基于Yolov8与ByteTrack的高速公路智慧监控平台 - 环境配置指南

## 1. 重新创建虚拟环境

### 1.1 删除旧环境（如果存在）
```bash
# 如果之前有虚拟环境，先删除
rm -rf venv
# 或者在Windows上
rmdir /s venv
```

### 1.2 创建新的虚拟环境
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate
```

### 1.3 升级pip
```bash
python -m pip install --upgrade pip
```

## 2. 安装依赖包

### 2.1 安装主要依赖
```bash
# 安装主项目依赖
pip install -r requirements.txt

# 安装后端专用依赖
pip install -r backend/requirements.txt
```

### 2.2 如果安装失败，分步安装
```bash
# 核心依赖
pip install flask flask-cors flask-socketio
pip install pymysql python-dotenv
pip install ultralytics opencv-python supervision
pip install numpy pillow pandas

# 可选依赖（如果上面的成功了再安装）
pip install redis psutil apscheduler
pip install gunicorn eventlet
pip install pytest black flake8
```

## 3. 数据库配置

### 3.1 确保MySQL服务运行
```bash
# Windows (以管理员身份运行)
net start mysql

# Linux
sudo systemctl start mysql
# 或
sudo service mysql start

# Mac
brew services start mysql
```

### 3.2 创建数据库
```sql
-- 连接到MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE IF NOT EXISTS yolo CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户（可选）
CREATE USER 'yolo_user'@'localhost' IDENTIFIED BY 'yolo_password_123';
GRANT ALL PRIVILEGES ON yolo.* TO 'yolo_user'@'localhost';
FLUSH PRIVILEGES;

-- 退出
EXIT;
```

### 3.3 导入数据库结构和数据
```bash
# 方法1：使用mysql命令行
mysql -u root -p yolo < yolo.sql

# 方法2：使用Navicat
# 1. 打开Navicat
# 2. 连接到MySQL服务器
# 3. 选择yolo数据库
# 4. 右键 -> 运行SQL文件 -> 选择yolo.sql
# 5. 点击运行
```

## 4. 配置文件检查

### 4.1 检查环境配置文件
```bash
# 确保配置文件存在
ls config/end-back.env

# 如果不存在，复制示例文件
cp config/end-back.env.example config/end-back.env
```

### 4.2 修改数据库配置
编辑 `config/end-back.env` 文件：
```ini
# 数据库配置
MYSQL_HOST=127.0.0.1
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=123456
MYSQL_DATABASE=yolo
MYSQL_CHARSET=utf8mb4

# 如果使用了新创建的用户
# MYSQL_USER=yolo_user
# MYSQL_PASSWORD=yolo_password_123
```

## 5. 测试数据库连接

### 5.1 创建测试脚本
```python
# test_db.py
import os
import sys
sys.path.append('.')

from dotenv import load_dotenv
load_dotenv(override=True, dotenv_path='config/end-back.env')

try:
    from backend.utils.database import init_database, get_db_connection
    
    print("正在测试数据库连接...")
    
    # 测试连接
    if init_database():
        print("✓ 数据库连接成功")
        
        # 测试查询
        with get_db_connection() as db:
            result = db.get_list("SHOW TABLES")
            print(f"✓ 数据库中有 {len(result)} 个表")
            for table in result:
                table_name = list(table.values())[0]
                count = db.get_one(f"SELECT COUNT(*) as count FROM {table_name}")
                print(f"  - {table_name}: {count['count']} 条记录")
    else:
        print("✗ 数据库连接失败")
        
except Exception as e:
    print(f"✗ 测试失败: {e}")
```

### 5.2 运行测试
```bash
python test_db.py
```

## 6. 启动系统

### 6.1 检查模型文件
```bash
# 创建模型目录
mkdir -p models

# 检查是否有模型文件
ls models/

# 如果没有模型文件，系统会自动下载yolov8n.pt
# 或者手动下载：
# wget https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8n.pt -O models/yolov8n.pt
```

### 6.2 启动应用
```bash
# 方法1：使用新的主启动文件（推荐）
python app_main.py

# 方法2：使用原有启动文件
python app.py

# 方法3：使用增强版后端
python backend/app_enhanced.py
```

### 6.3 验证启动
打开浏览器访问：
- 主页：http://127.0.0.1:5500/
- 健康检查：http://127.0.0.1:5500/health
- API文档：http://127.0.0.1:5500/api/v1/docs

## 7. 常见问题解决

### 7.1 依赖包安装失败
```bash
# 如果某些包安装失败，尝试使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 或者分别安装核心包
pip install flask==2.3.3
pip install ultralytics==8.0.196
pip install opencv-python==********
```

### 7.2 数据库连接失败
```bash
# 检查MySQL服务状态
# Windows:
sc query mysql
# Linux:
systemctl status mysql

# 检查端口是否被占用
netstat -an | grep 3306

# 重置MySQL密码（如果忘记）
# 停止MySQL服务，然后以安全模式启动
```

### 7.3 模型加载失败
```bash
# 手动下载模型文件
mkdir -p models
cd models

# 下载YOLOv8模型
curl -L https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8n.pt -o yolov8n.pt
curl -L https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8s.pt -o yolov8s.pt
```

### 7.4 端口被占用
```bash
# 查看端口占用
netstat -ano | findstr 5500

# 修改端口（在config/end-back.env中）
PORT=5501
```

## 8. 开发模式启动

### 8.1 启用调试模式
在 `config/end-back.env` 中设置：
```ini
DEBUG=true
```

### 8.2 实时重载
```bash
# 使用Flask开发服务器
export FLASK_APP=app_main.py
export FLASK_ENV=development
flask run --host=0.0.0.0 --port=5500
```

## 9. 生产环境部署

### 9.1 使用Gunicorn
```bash
# 安装gunicorn
pip install gunicorn

# 启动生产服务器
gunicorn --bind 0.0.0.0:5500 --workers 4 --worker-class eventlet app_main:app
```

### 9.2 使用Docker（可选）
```bash
# 构建镜像
docker build -t highway-yolo-backend -f deploy/Dockerfile.backend .

# 运行容器
docker run -p 5500:5500 highway-yolo-backend
```

## 10. 验证系统功能

### 10.1 测试API接口
```bash
# 测试基本接口
curl http://127.0.0.1:5500/
curl http://127.0.0.1:5500/health
curl http://127.0.0.1:5500/api/test

# 测试登录接口
curl -X POST http://127.0.0.1:5500/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"123456"}'
```

### 10.2 检查日志
```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log
```

按照这个指南操作，应该能够成功配置和启动系统。如果遇到问题，请告诉我具体的错误信息。
