# <AUTHOR> Enhanced by Cascade
import sys
from PySide6.QtWidgets import QApplication, QWidget
from PySide6.QtCore import Signal
from PySide6.QtGui import QPixmap
from ui.dialog.tech_tracking_dialog import TechTrackingWindow


class TechTrackingDialog(TechTrackingWindow):
    # 定义信号（确保与TechTrackingWindow的idConfirmed信号类型匹配）
    id_confirmed = Signal(int)
    
    def __init__(self):
        super(TechTrackingDialog, self).__init__()
        # 连接按钮信号到槽
        self.trackButton.clicked.connect(self.on_track_button_clicked)
        
    def on_track_button_clicked(self):
        # 获取ID并发送信号
        try:
            target_id = int(self.idEdit.text())
            self.id_confirmed.emit(target_id)
            self.statusLabel.setText(f"正在追踪目标 ID: {target_id}")
            self.statusLabel.setStyleSheet("color: rgb(0, 200, 100); font: 500 10pt \"Segoe UI\";") 
        except ValueError:
            self.statusLabel.setText("错误: 请输入有效的ID数字")
            self.statusLabel.setStyleSheet("color: rgb(255, 100, 100); font: 500 10pt \"Segoe UI\";") 
    
    # 确保方法名称与TechTrackingWindow中的一致
    def update_target_preview(self, pixmap):
        """更新目标预览图像"""
        self.updateTargetImage(pixmap)
    
    def update_status(self, message, color="green"):
        """更新状态信息"""
        self.statusLabel.setText(message)
        if color == "green":
            self.statusLabel.setStyleSheet("color: rgb(0, 200, 100); font: 500 10pt \"Segoe UI\";") 
        elif color == "red":
            self.statusLabel.setStyleSheet("color: rgb(255, 100, 100); font: 500 10pt \"Segoe UI\";") 
        elif color == "orange":
            self.statusLabel.setStyleSheet("color: rgb(255, 150, 50); font: 500 10pt \"Segoe UI\";") 
        else:
            self.statusLabel.setStyleSheet("color: rgb(220, 220, 220); font: 500 10pt \"Segoe UI\";") 


if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = TechTrackingDialog()
    window.show()
    sys.exit(app.exec())
