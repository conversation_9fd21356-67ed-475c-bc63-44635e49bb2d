#!/bin/bash

# 基于Yolov8与ByteTrack的高速公路智慧监控平台 - 后端启动脚本

set -e

echo "=========================================="
echo "启动基于Yolov8与ByteTrack的高速公路智慧监控平台后端服务"
echo "=========================================="

# 等待数据库启动
echo "等待数据库连接..."
while ! nc -z ${MYSQL_HOST:-mysql} ${MYSQL_PORT:-3306}; do
    echo "等待MySQL数据库启动..."
    sleep 2
done
echo "数据库连接成功！"

# 等待Redis启动
echo "等待Redis连接..."
while ! nc -z ${REDIS_HOST:-redis} ${REDIS_PORT:-6379}; do
    echo "等待Redis启动..."
    sleep 2
done
echo "Redis连接成功！"

# 初始化数据库
echo "初始化数据库..."
cd /app
python -c "
from backend.utils.database import init_database, execute_sql_file
import os

# 初始化数据库连接
if init_database():
    print('数据库连接初始化成功')
    
    # 执行SQL文件（如果存在）
    sql_file = 'yolo.sql'
    if os.path.exists(sql_file):
        print('执行数据库初始化脚本...')
        execute_sql_file(sql_file)
        print('数据库初始化完成')
    else:
        print('未找到数据库初始化脚本，跳过')
else:
    print('数据库连接初始化失败')
    exit(1)
"

# 检查模型文件
echo "检查模型文件..."
if [ ! -f "/app/models/car.pt" ]; then
    echo "警告: 未找到car.pt模型文件，将使用默认YOLOv8模型"
    mkdir -p /app/models
    # 这里可以添加下载默认模型的逻辑
fi

# 设置环境变量
export PYTHONPATH=/app
export FLASK_APP=backend.app_enhanced
export FLASK_ENV=${FLASK_ENV:-production}

# 创建日志目录
mkdir -p /app/logs

# 启动应用
echo "启动Flask应用..."
cd /app

# 使用gunicorn启动生产环境
if [ "${FLASK_ENV}" = "production" ]; then
    echo "使用Gunicorn启动生产环境..."
    exec gunicorn \
        --bind 0.0.0.0:5500 \
        --workers 4 \
        --worker-class eventlet \
        --worker-connections 1000 \
        --timeout 120 \
        --keepalive 2 \
        --max-requests 1000 \
        --max-requests-jitter 100 \
        --preload \
        --access-logfile /app/logs/access.log \
        --error-logfile /app/logs/error.log \
        --log-level info \
        --capture-output \
        backend.app_enhanced:app
else
    echo "使用Flask开发服务器启动..."
    exec python -m backend.app_enhanced
fi
