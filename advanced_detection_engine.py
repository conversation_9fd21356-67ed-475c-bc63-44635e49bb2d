# -*- coding: utf-8 -*-
# @Description : 高级检测引擎 - 违规检测、事故检测
# @Date : 2025年6月21日

import cv2
import numpy as np
import math
from datetime import datetime, timedelta
from collections import defaultdict, deque
import json

class ViolationDetector:
    """违规检测器"""
    
    def __init__(self):
        self.parking_zones = {}  # 停车区域定义
        self.lane_lines = {}     # 车道线定义
        self.direction_vectors = {}  # 方向向量
        self.stationary_threshold = 30  # 静止帧数阈值
        self.speed_threshold = 5  # 最低速度阈值(像素/帧)
        
    def set_parking_zones(self, monitor_id, zones):
        """设置禁停区域"""
        self.parking_zones[monitor_id] = zones
        
    def set_lane_lines(self, monitor_id, lanes):
        """设置车道线"""
        self.lane_lines[monitor_id] = lanes
        
    def set_direction_vectors(self, monitor_id, vectors):
        """设置正常行驶方向"""
        self.direction_vectors[monitor_id] = vectors
    
    def detect_illegal_parking(self, monitor_id, tracked_objects, frame_count):
        """检测违规停车"""
        violations = []
        
        if monitor_id not in self.parking_zones:
            return violations
            
        zones = self.parking_zones[monitor_id]
        
        for obj in tracked_objects:
            track_id = obj['track_id']
            bbox = obj['bbox']
            center = [(bbox[0] + bbox[2]) / 2, (bbox[1] + bbox[3]) / 2]
            
            # 检查是否在禁停区域
            for zone_name, zone_points in zones.items():
                if self._point_in_polygon(center, zone_points):
                    # 检查是否静止时间过长
                    if self._is_stationary_too_long(track_id, center, frame_count):
                        violations.append({
                            'type': 'illegal_parking',
                            'track_id': track_id,
                            'location': center,
                            'zone': zone_name,
                            'duration': self._get_stationary_duration(track_id),
                            'severity': 'medium',
                            'description': f'车辆在{zone_name}违规停车'
                        })
        
        return violations
    
    def detect_wrong_direction(self, monitor_id, tracked_objects):
        """检测逆行"""
        violations = []
        
        if monitor_id not in self.direction_vectors:
            return violations
            
        normal_direction = self.direction_vectors[monitor_id]
        
        for obj in tracked_objects:
            if 'velocity' in obj:
                velocity = obj['velocity']
                
                # 计算速度向量与正常方向的夹角
                angle = self._calculate_angle(velocity, normal_direction)
                
                # 如果角度大于90度，认为是逆行
                if angle > 90:
                    violations.append({
                        'type': 'wrong_direction',
                        'track_id': obj['track_id'],
                        'location': [(obj['bbox'][0] + obj['bbox'][2]) / 2, 
                                   (obj['bbox'][1] + obj['bbox'][3]) / 2],
                        'angle': angle,
                        'severity': 'high',
                        'description': f'车辆逆行，偏离正常方向{angle:.1f}度'
                    })
        
        return violations
    
    def detect_lane_violation(self, monitor_id, tracked_objects):
        """检测违规变道"""
        violations = []
        
        if monitor_id not in self.lane_lines:
            return violations
            
        # 实现车道违规检测逻辑
        # 这里简化实现，实际需要更复杂的算法
        
        return violations
    
    def _point_in_polygon(self, point, polygon):
        """判断点是否在多边形内"""
        x, y = point
        n = len(polygon)
        inside = False
        
        p1x, p1y = polygon[0]
        for i in range(1, n + 1):
            p2x, p2y = polygon[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y
        
        return inside
    
    def _is_stationary_too_long(self, track_id, center, frame_count):
        """检查是否静止时间过长"""
        # 这里需要维护每个轨迹的历史位置
        # 简化实现
        return frame_count > self.stationary_threshold
    
    def _get_stationary_duration(self, track_id):
        """获取静止持续时间"""
        # 简化实现，返回估算时间
        return 30  # 秒
    
    def _calculate_angle(self, vector1, vector2):
        """计算两个向量的夹角"""
        dot_product = np.dot(vector1, vector2)
        norms = np.linalg.norm(vector1) * np.linalg.norm(vector2)
        
        if norms == 0:
            return 0
            
        cos_angle = dot_product / norms
        cos_angle = np.clip(cos_angle, -1.0, 1.0)
        angle = np.arccos(cos_angle) * 180 / np.pi
        
        return angle

class AccidentDetector:
    """事故检测器"""
    
    def __init__(self):
        self.collision_threshold = 50  # 碰撞距离阈值
        self.sudden_stop_threshold = 20  # 急停速度变化阈值
        self.congestion_threshold = 0.8  # 拥堵密度阈值
        self.track_history = defaultdict(deque)  # 轨迹历史
        self.max_history = 30  # 保留30帧历史
        
    def detect_collision(self, tracked_objects):
        """检测碰撞事故"""
        accidents = []
        
        for i, obj1 in enumerate(tracked_objects):
            for j, obj2 in enumerate(tracked_objects[i+1:], i+1):
                distance = self._calculate_distance(obj1['bbox'], obj2['bbox'])
                
                if distance < self.collision_threshold:
                    # 检查速度变化
                    if self._has_sudden_speed_change(obj1) or self._has_sudden_speed_change(obj2):
                        accidents.append({
                            'type': 'collision',
                            'involved_tracks': [obj1['track_id'], obj2['track_id']],
                            'location': self._get_midpoint(obj1['bbox'], obj2['bbox']),
                            'distance': distance,
                            'severity': 'critical',
                            'description': f'检测到车辆碰撞，涉及车辆ID: {obj1["track_id"]}, {obj2["track_id"]}'
                        })
        
        return accidents
    
    def detect_sudden_stop(self, tracked_objects):
        """检测急停"""
        accidents = []
        
        for obj in tracked_objects:
            track_id = obj['track_id']
            
            # 更新轨迹历史
            self._update_track_history(track_id, obj)
            
            if self._has_sudden_stop(track_id):
                accidents.append({
                    'type': 'sudden_stop',
                    'track_id': track_id,
                    'location': [(obj['bbox'][0] + obj['bbox'][2]) / 2, 
                               (obj['bbox'][1] + obj['bbox'][3]) / 2],
                    'severity': 'high',
                    'description': f'车辆{track_id}急停'
                })
        
        return accidents
    
    def detect_congestion(self, tracked_objects, frame_area):
        """检测交通拥堵"""
        if not tracked_objects:
            return []
            
        # 计算车辆密度
        vehicle_count = len(tracked_objects)
        density = vehicle_count / frame_area
        
        if density > self.congestion_threshold:
            # 检查车辆平均速度
            avg_speed = self._calculate_average_speed(tracked_objects)
            
            if avg_speed < 5:  # 平均速度很低
                return [{
                    'type': 'congestion',
                    'vehicle_count': vehicle_count,
                    'density': density,
                    'avg_speed': avg_speed,
                    'severity': 'medium',
                    'description': f'检测到交通拥堵，车辆数量: {vehicle_count}, 平均速度: {avg_speed:.1f}'
                }]
        
        return []
    
    def _calculate_distance(self, bbox1, bbox2):
        """计算两个边界框的距离"""
        center1 = [(bbox1[0] + bbox1[2]) / 2, (bbox1[1] + bbox1[3]) / 2]
        center2 = [(bbox2[0] + bbox2[2]) / 2, (bbox2[1] + bbox2[3]) / 2]
        
        return math.sqrt((center1[0] - center2[0])**2 + (center1[1] - center2[1])**2)
    
    def _get_midpoint(self, bbox1, bbox2):
        """获取两个边界框的中点"""
        center1 = [(bbox1[0] + bbox1[2]) / 2, (bbox1[1] + bbox1[3]) / 2]
        center2 = [(bbox2[0] + bbox2[2]) / 2, (bbox2[1] + bbox2[3]) / 2]
        
        return [(center1[0] + center2[0]) / 2, (center1[1] + center2[1]) / 2]
    
    def _has_sudden_speed_change(self, obj):
        """检查是否有急剧速度变化"""
        if 'velocity' in obj and 'prev_velocity' in obj:
            current_speed = np.linalg.norm(obj['velocity'])
            prev_speed = np.linalg.norm(obj['prev_velocity'])
            
            speed_change = abs(current_speed - prev_speed)
            return speed_change > self.sudden_stop_threshold
        
        return False
    
    def _update_track_history(self, track_id, obj):
        """更新轨迹历史"""
        center = [(obj['bbox'][0] + obj['bbox'][2]) / 2, (obj['bbox'][1] + obj['bbox'][3]) / 2]
        
        self.track_history[track_id].append({
            'center': center,
            'timestamp': datetime.now()
        })
        
        # 保持历史长度
        if len(self.track_history[track_id]) > self.max_history:
            self.track_history[track_id].popleft()
    
    def _has_sudden_stop(self, track_id):
        """检查是否急停"""
        if track_id not in self.track_history or len(self.track_history[track_id]) < 5:
            return False
            
        history = list(self.track_history[track_id])
        
        # 计算最近几帧的速度变化
        recent_speeds = []
        for i in range(1, min(5, len(history))):
            prev_pos = history[-i-1]['center']
            curr_pos = history[-i]['center']
            
            distance = math.sqrt((curr_pos[0] - prev_pos[0])**2 + (curr_pos[1] - prev_pos[1])**2)
            recent_speeds.append(distance)
        
        if len(recent_speeds) >= 3:
            # 检查速度是否急剧下降
            speed_change = recent_speeds[0] - recent_speeds[-1]
            return speed_change > self.sudden_stop_threshold
        
        return False
    
    def _calculate_average_speed(self, tracked_objects):
        """计算平均速度"""
        if not tracked_objects:
            return 0
            
        total_speed = 0
        count = 0
        
        for obj in tracked_objects:
            if 'velocity' in obj:
                speed = np.linalg.norm(obj['velocity'])
                total_speed += speed
                count += 1
        
        return total_speed / count if count > 0 else 0

class TrafficAnalyzer:
    """交通分析器"""
    
    def __init__(self):
        self.vehicle_counts = defaultdict(list)  # 车辆计数历史
        self.speed_data = defaultdict(list)      # 速度数据
        self.flow_data = defaultdict(list)       # 流量数据
        
    def update_statistics(self, monitor_id, tracked_objects, timestamp):
        """更新统计数据"""
        vehicle_count = len(tracked_objects)
        
        # 记录车辆数量
        self.vehicle_counts[monitor_id].append({
            'count': vehicle_count,
            'timestamp': timestamp
        })
        
        # 记录速度数据
        speeds = []
        for obj in tracked_objects:
            if 'velocity' in obj:
                speed = np.linalg.norm(obj['velocity'])
                speeds.append(speed)
        
        if speeds:
            self.speed_data[monitor_id].append({
                'avg_speed': np.mean(speeds),
                'max_speed': np.max(speeds),
                'min_speed': np.min(speeds),
                'timestamp': timestamp
            })
        
        # 计算流量（车辆/分钟）
        self._calculate_flow_rate(monitor_id, timestamp)
    
    def get_hourly_statistics(self, monitor_id, date=None):
        """获取小时统计"""
        if date is None:
            date = datetime.now().date()
            
        hourly_stats = {}
        
        for hour in range(24):
            start_time = datetime.combine(date, datetime.min.time()) + timedelta(hours=hour)
            end_time = start_time + timedelta(hours=1)
            
            # 获取该小时的数据
            hour_data = [
                item for item in self.vehicle_counts[monitor_id]
                if start_time <= item['timestamp'] < end_time
            ]
            
            if hour_data:
                counts = [item['count'] for item in hour_data]
                hourly_stats[hour] = {
                    'avg_count': np.mean(counts),
                    'max_count': np.max(counts),
                    'min_count': np.min(counts),
                    'total_records': len(hour_data)
                }
            else:
                hourly_stats[hour] = {
                    'avg_count': 0,
                    'max_count': 0,
                    'min_count': 0,
                    'total_records': 0
                }
        
        return hourly_stats
    
    def get_daily_statistics(self, monitor_id, start_date, end_date):
        """获取日统计"""
        daily_stats = {}
        
        current_date = start_date
        while current_date <= end_date:
            start_time = datetime.combine(current_date, datetime.min.time())
            end_time = start_time + timedelta(days=1)
            
            # 获取该天的数据
            day_data = [
                item for item in self.vehicle_counts[monitor_id]
                if start_time <= item['timestamp'] < end_time
            ]
            
            if day_data:
                counts = [item['count'] for item in day_data]
                daily_stats[current_date.isoformat()] = {
                    'avg_count': np.mean(counts),
                    'max_count': np.max(counts),
                    'min_count': np.min(counts),
                    'total_vehicles': sum(counts),
                    'peak_hour': self._find_peak_hour(day_data)
                }
            
            current_date += timedelta(days=1)
        
        return daily_stats
    
    def get_speed_analysis(self, monitor_id, hours=24):
        """获取速度分析"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        recent_data = [
            item for item in self.speed_data[monitor_id]
            if item['timestamp'] >= cutoff_time
        ]
        
        if not recent_data:
            return None
            
        avg_speeds = [item['avg_speed'] for item in recent_data]
        max_speeds = [item['max_speed'] for item in recent_data]
        
        return {
            'avg_speed': np.mean(avg_speeds),
            'max_speed': np.max(max_speeds),
            'min_speed': np.min([item['min_speed'] for item in recent_data]),
            'speed_variance': np.var(avg_speeds),
            'data_points': len(recent_data)
        }
    
    def _calculate_flow_rate(self, monitor_id, timestamp):
        """计算流量率"""
        # 计算过去1分钟的车辆数量
        cutoff_time = timestamp - timedelta(minutes=1)
        
        recent_counts = [
            item['count'] for item in self.vehicle_counts[monitor_id]
            if item['timestamp'] >= cutoff_time
        ]
        
        if recent_counts:
            flow_rate = sum(recent_counts) / len(recent_counts)  # 车辆/分钟
            
            self.flow_data[monitor_id].append({
                'flow_rate': flow_rate,
                'timestamp': timestamp
            })
    
    def _find_peak_hour(self, day_data):
        """找到高峰时段"""
        hourly_counts = defaultdict(list)
        
        for item in day_data:
            hour = item['timestamp'].hour
            hourly_counts[hour].append(item['count'])
        
        peak_hour = 0
        max_avg = 0
        
        for hour, counts in hourly_counts.items():
            avg_count = np.mean(counts)
            if avg_count > max_avg:
                max_avg = avg_count
                peak_hour = hour
        
        return peak_hour
