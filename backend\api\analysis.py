# -*- coding: utf-8 -*-
# @Description : 数据分析相关API接口
# @Date : 2025年6月20日

from datetime import datetime, timedelta
from flask import request, jsonify
from . import api_v1
from .auth import token_required
from utils.response import success_response, error_response
from utils.database import get_db_connection
from services.analysis_service import AnalysisService

# 初始化分析服务
analysis_service = AnalysisService()

@api_v1.route('/analysis/alarms', methods=['GET'])
@token_required
def get_alarms(current_user_id):
    """获取警报列表"""
    try:
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 10))
        location = request.args.get('location', '')
        highway_section = request.args.get('highway_section', '')
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        
        offset = (page - 1) * page_size
        
        # 构建查询条件
        where_conditions = []
        params = []
        
        if location:
            where_conditions.append("a.location LIKE %s")
            params.append(f"%{location}%")
        
        if highway_section:
            where_conditions.append("a.highway_section LIKE %s")
            params.append(f"%{highway_section}%")
        
        if start_date:
            where_conditions.append("a.create_time >= %s")
            params.append(start_date)
        
        if end_date:
            where_conditions.append("a.create_time <= %s")
            params.append(end_date)
        
        where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""
        
        with get_db_connection() as db:
            # 获取总数
            count_sql = f"SELECT COUNT(*) as total FROM alarm a{where_clause}"
            total = db.get_one(count_sql, params)['total']
            
            # 获取列表数据
            list_sql = f"""
                SELECT a.id, a.location, a.highway_section, a.description, a.vehicle_count,
                       a.detection_details, a.confidence_level, a.threshold, a.photo, a.pid,
                       a.create_time, a.remark, m.person as monitor_person
                FROM alarm a
                LEFT JOIN monitor m ON a.pid = m.id
                {where_clause}
                ORDER BY a.create_time DESC
                LIMIT %s OFFSET %s
            """
            params.extend([page_size, offset])
            alarms = db.get_list(list_sql, params)
            
            # 处理检测详情JSON数据
            for alarm in alarms:
                if alarm['detection_details']:
                    try:
                        alarm['detection_details'] = json.loads(alarm['detection_details'])
                    except:
                        alarm['detection_details'] = {}
        
        return success_response({
            'list': alarms,
            'total': total,
            'page': page,
            'page_size': page_size
        }, '获取警报列表成功')
        
    except Exception as e:
        return error_response(f'获取警报列表失败: {str(e)}')

@api_v1.route('/analysis/alarm/<int:alarm_id>', methods=['GET'])
@token_required
def get_alarm_detail(current_user_id, alarm_id):
    """获取警报详情"""
    try:
        with get_db_connection() as db:
            alarm = db.get_one(
                """SELECT a.*, m.person as monitor_person, m.location as monitor_location
                   FROM alarm a
                   LEFT JOIN monitor m ON a.pid = m.id
                   WHERE a.id=%s""",
                (alarm_id,)
            )
        
        if not alarm:
            return error_response('警报不存在')
        
        # 处理检测详情JSON数据
        if alarm['detection_details']:
            try:
                alarm['detection_details'] = json.loads(alarm['detection_details'])
            except:
                alarm['detection_details'] = {}
        
        return success_response(alarm, '获取警报详情成功')
        
    except Exception as e:
        return error_response(f'获取警报详情失败: {str(e)}')

@api_v1.route('/analysis/statistics/overview', methods=['GET'])
@token_required
def get_overview_statistics(current_user_id):
    """获取概览统计数据"""
    try:
        # 获取时间范围参数
        days = int(request.args.get('days', 7))
        start_date = datetime.now() - timedelta(days=days)
        
        with get_db_connection() as db:
            # 监控点总数
            monitor_count = db.get_one("SELECT COUNT(*) as count FROM monitor")['count']
            
            # 活跃监控点数量
            active_monitor_count = db.get_one(
                "SELECT COUNT(*) as count FROM monitor WHERE is_alarm='开启'"
            )['count']
            
            # 警报总数
            total_alarms = db.get_one(
                "SELECT COUNT(*) as count FROM alarm WHERE create_time >= %s",
                (start_date,)
            )['count']
            
            # 今日警报数
            today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            today_alarms = db.get_one(
                "SELECT COUNT(*) as count FROM alarm WHERE create_time >= %s",
                (today_start,)
            )['count']
            
            # 平均车辆数
            avg_vehicles = db.get_one(
                "SELECT AVG(vehicle_count) as avg_count FROM alarm WHERE create_time >= %s",
                (start_date,)
            )['avg_count'] or 0

            # 最高车辆数
            max_vehicles = db.get_one(
                "SELECT MAX(vehicle_count) as max_count FROM alarm WHERE create_time >= %s",
                (start_date,)
            )['max_count'] or 0
        
        return success_response({
            'monitor_count': monitor_count,
            'active_monitor_count': active_monitor_count,
            'total_alarms': total_alarms,
            'today_alarms': today_alarms,
            'avg_vehicles': round(float(avg_vehicles), 2),
            'max_vehicles': max_vehicles,
            'time_range': f'最近{days}天'
        }, '获取概览统计成功')
        
    except Exception as e:
        return error_response(f'获取概览统计失败: {str(e)}')

@api_v1.route('/analysis/statistics/traffic-flow', methods=['GET'])
@token_required
def get_traffic_flow_statistics(current_user_id):
    """获取交通流量统计"""
    try:
        # 获取参数
        days = int(request.args.get('days', 7))
        highway_section = request.args.get('highway_section', '')
        
        stats = analysis_service.get_traffic_flow_stats(days, highway_section)
        
        return success_response(stats, '获取交通流量统计成功')
        
    except Exception as e:
        return error_response(f'获取交通流量统计失败: {str(e)}')

@api_v1.route('/analysis/statistics/hourly-distribution', methods=['GET'])
@token_required
def get_hourly_distribution(current_user_id):
    """获取小时分布统计"""
    try:
        days = int(request.args.get('days', 7))
        
        stats = analysis_service.get_hourly_distribution(days)
        
        return success_response(stats, '获取小时分布统计成功')
        
    except Exception as e:
        return error_response(f'获取小时分布统计失败: {str(e)}')

@api_v1.route('/analysis/statistics/location-ranking', methods=['GET'])
@token_required
def get_location_ranking(current_user_id):
    """获取地点排行统计"""
    try:
        days = int(request.args.get('days', 7))
        limit = int(request.args.get('limit', 10))
        
        stats = analysis_service.get_location_ranking(days, limit)
        
        return success_response(stats, '获取地点排行统计成功')
        
    except Exception as e:
        return error_response(f'获取地点排行统计失败: {str(e)}')

@api_v1.route('/analysis/heatmap', methods=['GET'])
@token_required
def get_heatmap_data(current_user_id):
    """获取热力图数据"""
    try:
        monitor_id = request.args.get('monitor_id')
        days = int(request.args.get('days', 1))
        
        if not monitor_id:
            return error_response('监控点ID不能为空')
        
        heatmap_data = analysis_service.generate_heatmap_data(monitor_id, days)
        
        return success_response(heatmap_data, '获取热力图数据成功')
        
    except Exception as e:
        return error_response(f'获取热力图数据失败: {str(e)}')

@api_v1.route('/analysis/export/alarms', methods=['POST'])
@token_required
def export_alarms(current_user_id):
    """导出警报数据"""
    try:
        data = request.get_json()
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        location = data.get('location', '')
        format_type = data.get('format', 'excel')  # excel, csv, pdf
        
        if not start_date or not end_date:
            return error_response('开始日期和结束日期不能为空')
        
        file_path = analysis_service.export_alarms(
            start_date, end_date, location, format_type
        )
        
        return success_response({
            'file_path': file_path,
            'download_url': f'/api/v1/analysis/download/{file_path.split("/")[-1]}'
        }, '导出警报数据成功')
        
    except Exception as e:
        return error_response(f'导出警报数据失败: {str(e)}')

@api_v1.route('/analysis/download/<filename>', methods=['GET'])
@token_required
def download_file(current_user_id, filename):
    """下载导出文件"""
    try:
        return send_from_directory('exports', filename, as_attachment=True)
    except Exception as e:
        return error_response(f'文件下载失败: {str(e)}')

@api_v1.route('/analysis/predictions/traffic', methods=['GET'])
@token_required
def get_traffic_predictions(current_user_id):
    """获取交通流量预测"""
    try:
        monitor_id = request.args.get('monitor_id')
        hours = int(request.args.get('hours', 24))
        
        predictions = analysis_service.predict_traffic_flow(monitor_id, hours)
        
        return success_response(predictions, '获取交通流量预测成功')
        
    except Exception as e:
        return error_response(f'获取交通流量预测失败: {str(e)}')

@api_v1.route('/analysis/reports/daily', methods=['GET'])
@token_required
def get_daily_report(current_user_id):
    """获取日报"""
    try:
        date = request.args.get('date', datetime.now().strftime('%Y-%m-%d'))
        
        report = analysis_service.generate_daily_report(date)
        
        return success_response(report, '获取日报成功')
        
    except Exception as e:
        return error_response(f'获取日报失败: {str(e)}')

@api_v1.route('/analysis/reports/weekly', methods=['GET'])
@token_required
def get_weekly_report(current_user_id):
    """获取周报"""
    try:
        week_start = request.args.get('week_start')
        
        if not week_start:
            # 默认为本周
            today = datetime.now()
            week_start = (today - timedelta(days=today.weekday())).strftime('%Y-%m-%d')
        
        report = analysis_service.generate_weekly_report(week_start)
        
        return success_response(report, '获取周报成功')
        
    except Exception as e:
        return error_response(f'获取周报失败: {str(e)}')
