# -*- coding: utf-8 -*-
# @Description : YOLO检测服务
# @Date : 2025年6月20日

import os
import cv2
import uuid
import json
import threading
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
import numpy as np
from ultralytics import YOLO
import supervision as sv

class YOLOService:
    """YOLO检测服务类"""
    
    def __init__(self):
        self.model = None
        self.current_model_name = None
        self.detection_tasks = {}
        self.rtsp_tasks = {}
        self.box_annotator = sv.BoxAnnotator(
            thickness=2,
            text_thickness=1,
            text_scale=0.5
        )
        self.load_default_model()
    
    def load_default_model(self):
        """加载默认模型"""
        try:
            default_model_path = "./models/car.pt"
            if os.path.exists(default_model_path):
                self.model = YOLO(default_model_path)
                self.current_model_name = "car_custom"
                print(f"默认模型加载成功: {default_model_path}")
            else:
                # 尝试加载YOLOv8n模型
                self.model = YOLO("yolov8n.pt")
                self.current_model_name = "yolov8n"
                print("加载YOLOv8n模型")
        except Exception as e:
            print(f"模型加载失败: {str(e)}")
            self.model = None
    
    def get_available_models(self) -> List[Dict]:
        """获取可用的模型列表"""
        models = []
        model_dir = "./models"
        
        if os.path.exists(model_dir):
            for file in os.listdir(model_dir):
                if file.endswith(('.pt', '.onnx', '.engine')):
                    model_info = {
                        'name': file,
                        'path': os.path.join(model_dir, file),
                        'size': os.path.getsize(os.path.join(model_dir, file)),
                        'type': file.split('.')[-1],
                        'is_current': file == f"{self.current_model_name}.pt"
                    }
                    models.append(model_info)
        
        return models
    
    def switch_model(self, model_name: str) -> bool:
        """切换检测模型"""
        try:
            model_path = f"./models/{model_name}"
            if not os.path.exists(model_path):
                return False
            
            self.model = YOLO(model_path)
            self.current_model_name = model_name.split('.')[0]
            print(f"模型切换成功: {model_path}")
            return True
        except Exception as e:
            print(f"模型切换失败: {str(e)}")
            return False
    
    def detect_image(self, image: np.ndarray, conf_threshold: float = 0.4, 
                    iou_threshold: float = 0.5) -> Dict[str, Any]:
        """图像检测"""
        if self.model is None:
            raise Exception("模型未加载")
        
        try:
            # 执行检测
            results = self.model(image, conf=conf_threshold, iou=iou_threshold)
            
            # 处理检测结果
            detections = []
            if len(results) > 0:
                result = results[0]
                if result.boxes is not None:
                    boxes = result.boxes.xyxy.cpu().numpy()
                    confidences = result.boxes.conf.cpu().numpy()
                    class_ids = result.boxes.cls.cpu().numpy().astype(int)
                    
                    for i, (box, conf, cls_id) in enumerate(zip(boxes, confidences, class_ids)):
                        detection = {
                            'id': i,
                            'bbox': box.tolist(),
                            'confidence': float(conf),
                            'class_id': int(cls_id),
                            'class_name': self.model.names[cls_id] if cls_id in self.model.names else 'unknown'
                        }
                        detections.append(detection)
            
            return {
                'detections': detections,
                'image_shape': image.shape,
                'model_name': self.current_model_name,
                'parameters': {
                    'conf_threshold': conf_threshold,
                    'iou_threshold': iou_threshold
                }
            }
        except Exception as e:
            raise Exception(f"图像检测失败: {str(e)}")
    
    def draw_detections(self, image: np.ndarray, results: Dict[str, Any], 
                       show_labels: bool = True) -> np.ndarray:
        """在图像上绘制检测结果"""
        try:
            annotated_image = image.copy()
            detections = results['detections']
            
            if not detections:
                return annotated_image
            
            # 准备supervision格式的数据
            boxes = np.array([det['bbox'] for det in detections])
            confidences = np.array([det['confidence'] for det in detections])
            class_ids = np.array([det['class_id'] for det in detections])
            
            # 创建supervision的Detections对象
            sv_detections = sv.Detections(
                xyxy=boxes,
                confidence=confidences,
                class_id=class_ids
            )
            
            # 准备标签
            labels = None
            if show_labels:
                labels = [
                    f"{det['class_name']} {det['confidence']:.2f}"
                    for det in detections
                ]
            
            # 绘制检测框
            annotated_image = self.box_annotator.annotate(
                scene=annotated_image,
                detections=sv_detections,
                labels=labels
            )
            
            return annotated_image
        except Exception as e:
            print(f"绘制检测结果失败: {str(e)}")
            return image
    
    def get_detection_stats(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """获取检测统计信息"""
        detections = results['detections']
        
        if not detections:
            return {
                'total_objects': 0,
                'class_counts': {},
                'avg_confidence': 0.0,
                'max_confidence': 0.0,
                'min_confidence': 0.0
            }
        
        # 统计各类别数量
        class_counts = {}
        confidences = []
        
        for det in detections:
            class_name = det['class_name']
            confidence = det['confidence']
            
            class_counts[class_name] = class_counts.get(class_name, 0) + 1
            confidences.append(confidence)
        
        return {
            'total_objects': len(detections),
            'class_counts': class_counts,
            'avg_confidence': np.mean(confidences),
            'max_confidence': np.max(confidences),
            'min_confidence': np.min(confidences)
        }
    
    def detect_video(self, video_path: str, conf_threshold: float = 0.4,
                    iou_threshold: float = 0.5, show_labels: bool = True,
                    save_result: bool = True) -> Dict[str, Any]:
        """视频检测"""
        task_id = str(uuid.uuid4())
        
        # 创建检测任务
        task_info = {
            'task_id': task_id,
            'status': 'pending',
            'progress': 0,
            'video_path': video_path,
            'parameters': {
                'conf_threshold': conf_threshold,
                'iou_threshold': iou_threshold,
                'show_labels': show_labels,
                'save_result': save_result
            },
            'start_time': datetime.now(),
            'results': []
        }
        
        self.detection_tasks[task_id] = task_info
        
        # 启动检测线程
        thread = threading.Thread(
            target=self._process_video_detection,
            args=(task_id,)
        )
        thread.daemon = True
        thread.start()
        
        return {
            'task_id': task_id,
            'status': 'started',
            'message': '视频检测任务已启动'
        }
    
    def _process_video_detection(self, task_id: str):
        """处理视频检测（在独立线程中运行）"""
        task_info = self.detection_tasks[task_id]
        
        try:
            task_info['status'] = 'running'
            video_path = task_info['video_path']
            params = task_info['parameters']
            
            # 打开视频文件
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                raise Exception("无法打开视频文件")
            
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            
            # 准备输出视频（如果需要保存）
            output_writer = None
            if params['save_result']:
                output_path = f"results/video_{task_id}.mp4"
                os.makedirs(os.path.dirname(output_path), exist_ok=True)
                
                fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                frame_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                frame_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                output_writer = cv2.VideoWriter(output_path, fourcc, fps, (frame_width, frame_height))
            
            frame_count = 0
            detection_results = []
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # 执行检测
                results = self.detect_image(
                    frame,
                    conf_threshold=params['conf_threshold'],
                    iou_threshold=params['iou_threshold']
                )
                
                # 绘制检测结果
                if params['show_labels']:
                    frame = self.draw_detections(frame, results, True)
                
                # 保存结果
                if output_writer:
                    output_writer.write(frame)
                
                # 记录检测结果
                frame_result = {
                    'frame_number': frame_count,
                    'timestamp': frame_count / fps,
                    'detections': results['detections'],
                    'stats': self.get_detection_stats(results)
                }
                detection_results.append(frame_result)
                
                # 更新进度
                frame_count += 1
                progress = int((frame_count / total_frames) * 100)
                task_info['progress'] = progress
                
            # 清理资源
            cap.release()
            if output_writer:
                output_writer.release()
            
            # 更新任务状态
            task_info['status'] = 'completed'
            task_info['progress'] = 100
            task_info['end_time'] = datetime.now()
            task_info['results'] = detection_results
            task_info['output_path'] = output_path if params['save_result'] else None
            
        except Exception as e:
            task_info['status'] = 'failed'
            task_info['error'] = str(e)
            task_info['end_time'] = datetime.now()
    
    def start_rtsp_detection(self, rtsp_url: str, conf_threshold: float = 0.4,
                           iou_threshold: float = 0.5, show_labels: bool = True,
                           monitor_id: Optional[int] = None, user_id: Optional[int] = None) -> str:
        """启动RTSP流检测"""
        task_id = str(uuid.uuid4())
        
        # 创建RTSP检测任务
        task_info = {
            'task_id': task_id,
            'rtsp_url': rtsp_url,
            'monitor_id': monitor_id,
            'user_id': user_id,
            'status': 'running',
            'parameters': {
                'conf_threshold': conf_threshold,
                'iou_threshold': iou_threshold,
                'show_labels': show_labels
            },
            'start_time': datetime.now(),
            'frame_count': 0,
            'detection_count': 0,
            'stop_flag': False
        }
        
        self.rtsp_tasks[task_id] = task_info
        
        # 启动RTSP检测线程
        thread = threading.Thread(
            target=self._process_rtsp_detection,
            args=(task_id,)
        )
        thread.daemon = True
        thread.start()
        
        return task_id
    
    def _process_rtsp_detection(self, task_id: str):
        """处理RTSP流检测（在独立线程中运行）"""
        task_info = self.rtsp_tasks[task_id]
        
        try:
            rtsp_url = task_info['rtsp_url']
            params = task_info['parameters']
            
            # 打开RTSP流
            cap = cv2.VideoCapture(rtsp_url)
            if not cap.isOpened():
                raise Exception("无法连接RTSP流")
            
            while not task_info['stop_flag']:
                ret, frame = cap.read()
                if not ret:
                    time.sleep(0.1)
                    continue
                
                # 执行检测
                results = self.detect_image(
                    frame,
                    conf_threshold=params['conf_threshold'],
                    iou_threshold=params['iou_threshold']
                )
                
                # 更新统计信息
                task_info['frame_count'] += 1
                task_info['detection_count'] += len(results['detections'])
                task_info['last_detection'] = results
                task_info['last_frame_time'] = datetime.now()
                
                # 这里可以添加实时数据推送逻辑
                # 例如：推送到WebSocket、保存到数据库等
                
                time.sleep(0.033)  # 约30fps
            
            cap.release()
            task_info['status'] = 'stopped'
            task_info['end_time'] = datetime.now()
            
        except Exception as e:
            task_info['status'] = 'failed'
            task_info['error'] = str(e)
            task_info['end_time'] = datetime.now()
    
    def stop_rtsp_detection(self, task_id: str) -> bool:
        """停止RTSP流检测"""
        if task_id in self.rtsp_tasks:
            self.rtsp_tasks[task_id]['stop_flag'] = True
            return True
        return False
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        if task_id in self.detection_tasks:
            return self.detection_tasks[task_id]
        elif task_id in self.rtsp_tasks:
            return self.rtsp_tasks[task_id]
        return None
    
    def get_user_tasks(self, user_id: int) -> List[Dict[str, Any]]:
        """获取用户的检测任务"""
        user_tasks = []
        
        # 检查视频检测任务
        for task in self.detection_tasks.values():
            if task.get('user_id') == user_id:
                user_tasks.append(task)
        
        # 检查RTSP检测任务
        for task in self.rtsp_tasks.values():
            if task.get('user_id') == user_id:
                user_tasks.append(task)
        
        return user_tasks
