# 基于Yolov8与ByteTrack的高速公路智慧监控平台 - Docker Compose配置
version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: yolo_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-123456}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-yolo}
      MYSQL_USER: ${MYSQL_USER:-yolo_user}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-yolo_pass}
    ports:
      - "${MYSQL_PORT:-3306}:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d
      - ./mysql/conf.d:/etc/mysql/conf.d
    networks:
      - yolo_network
    command: --default-authentication-plugin=mysql_native_password

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: yolo_redis
    restart: unless-stopped
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    networks:
      - yolo_network
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_pass}

  # YOLO后端服务
  backend:
    build:
      context: ..
      dockerfile: deploy/Dockerfile.backend
    container_name: yolo_backend
    restart: unless-stopped
    environment:
      - MYSQL_HOST=mysql
      - MYSQL_PORT=3306
      - MYSQL_USER=${MYSQL_USER:-yolo_user}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD:-yolo_pass}
      - MYSQL_DATABASE=${MYSQL_DATABASE:-yolo}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-redis_pass}
      - HOST_NAME=0.0.0.0
      - PORT=5500
      - DEBUG=false
    ports:
      - "${BACKEND_PORT:-5500}:5500"
    volumes:
      - ../models:/app/models
      - ../static:/app/static
      - ../uploads:/app/uploads
      - ../logs:/app/logs
      - ../backups:/app/backups
    networks:
      - yolo_network
    depends_on:
      - mysql
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5500/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: yolo_nginx
    restart: unless-stopped
    ports:
      - "${HTTP_PORT:-80}:80"
      - "${HTTPS_PORT:-443}:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./ssl:/etc/nginx/ssl
      - ../static:/var/www/static
    networks:
      - yolo_network
    depends_on:
      - backend

  # 前端服务（可选，如果使用Vue等前端框架）
  frontend:
    build:
      context: ../frontend
      dockerfile: Dockerfile
    container_name: yolo_frontend
    restart: unless-stopped
    environment:
      - VUE_APP_API_BASE_URL=http://backend:5500/api/v1
      - VUE_APP_WS_URL=ws://backend:5500
    ports:
      - "${FRONTEND_PORT:-3000}:80"
    networks:
      - yolo_network
    depends_on:
      - backend

  # 监控服务（可选）
  prometheus:
    image: prom/prometheus:latest
    container_name: yolo_prometheus
    restart: unless-stopped
    ports:
      - "${PROMETHEUS_PORT:-9090}:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - yolo_network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'

  # Grafana仪表板（可选）
  grafana:
    image: grafana/grafana:latest
    container_name: yolo_grafana
    restart: unless-stopped
    ports:
      - "${GRAFANA_PORT:-3001}:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - yolo_network
    depends_on:
      - prometheus

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  yolo_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
