# 基于Yolov8与ByteTrack的高速公路智慧监控平台 - API接口文档 (前端对接版)

## 📋 基本信息
- **基础URL**: `http://localhost:5500/api/v1`
- **版本**: v2.0.0
- **认证方式**: <PERSON><PERSON> (JWT)
- **数据格式**: JSON
- **WebSocket**: `ws://localhost:5500/socket.io`
- **数据库**: MySQL 8.0 (统一数据表设计)

## 🏗️ 系统架构
- **YOLOv8**: 高精度目标检测算法
- **ByteTrack**: 多目标追踪算法  
- **碰撞检测**: 实时事故预警系统
- **多路视频流**: 支持6路RTSP视频流
- **实时分析**: WebSocket实时数据推送
- **用户角色**: admin(管理员) + operator(监控员)

## 👥 用户角色说明
- **admin**: 完整系统管理权限
- **operator**: 监控员权限，无系统管理功能

## 🔐 1. 认证接口 (Authentication)

### 1.1 用户登录
**接口**: `POST /auth/login`
**权限**: 无需认证

**请求参数**:
```json
{
  "username": "admin",
  "password": "123456"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "user": {
      "id": 1,
      "username": "admin",
      "grade": "admin",
      "email": "<EMAIL>",
      "last_login_time": "2024-12-24T08:00:00",
      "create_time": "2024-01-01T00:00:00"
    }
  }
}
```

### 1.2 获取用户信息
**接口**: `GET /auth/profile`
**权限**: Bearer Token

### 1.3 用户登出
**接口**: `POST /auth/logout`
**权限**: Bearer Token

## 📹 2. 监控管理接口 (Monitor)

### 2.1 获取监控点列表
**接口**: `GET /monitor/list`
**权限**: Bearer Token
**前端页面**: 监控管理 → 监控点列表

**查询参数**:
- `page`: 页码 (默认: 1)
- `size`: 每页数量 (默认: 20)
- `location`: 位置筛选
- `status`: 状态筛选 (online/offline)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "monitors": [
      {
        "id": 1,
        "name": "杭州收费站监控点",
        "location": "杭州收费站",
        "highway_section": "K0+000",
        "camera_position": "收费站出入口",
        "latitude": 30.2741,
        "longitude": 120.1551,
        "threshold": 15,
        "person": "admin",
        "url": "rtsp://admin:123456@192.168.1.101:554/stream1",
        "connection_status": "online",
        "is_alarm": "开启",
        "mode": "tracking",
        "enable_tracking": true,
        "tracker_type": "bytetrack",
        "status": 1,
        "create_time": "2024-01-01T00:00:00"
      }
    ],
    "total": 6,
    "page": 1,
    "size": 20
  }
}
```

### 2.2 创建监控点
**接口**: `POST /monitor`
**权限**: Bearer Token (仅admin)
**前端页面**: 监控管理 → 配置管理

### 2.3 更新监控点
**接口**: `PUT /monitor/{monitor_id}`
**权限**: Bearer Token (仅admin)

### 2.4 删除监控点
**接口**: `DELETE /monitor/{monitor_id}`
**权限**: Bearer Token (仅admin)

### 2.5 测试监控点连接
**接口**: `POST /monitor/{monitor_id}/test-connection`
**权限**: Bearer Token
**前端页面**: 监控管理 → 连接测试

## 🎯 3. 检测中心接口 (Detection)

### 3.1 图像检测
**接口**: `POST /detection/image`
**权限**: Bearer Token
**前端页面**: 检测中心 → 图像检测
**Content-Type**: `multipart/form-data`

**请求参数**:
- `file`: 图像文件
- `conf_threshold`: 置信度阈值 (默认: 0.4)
- `iou_threshold`: IOU阈值 (默认: 0.5)
- `show_labels`: 是否显示标签 (默认: true)

**响应示例**:
```json
{
  "success": true,
  "message": "图像检测成功",
  "data": {
    "task_id": "TASK_20241224_001",
    "original_image": "/static/images/original/20241224_123456.jpg",
    "result_image": "/static/images/detection/20241224_123456.jpg",
    "detections": [
      {
        "id": 0,
        "bbox": [100, 100, 200, 200],
        "confidence": 0.85,
        "class_id": 2,
        "class_name": "car"
      }
    ],
    "statistics": {
      "total_objects": 5,
      "class_counts": {"car": 3, "truck": 2},
      "avg_confidence": 0.82
    }
  }
}
```

### 3.2 视频检测
**接口**: `POST /detection/video`
**权限**: Bearer Token
**前端页面**: 检测中心 → 视频检测

### 3.3 RTSP流检测
**接口**: `POST /detection/rtsp`
**权限**: Bearer Token
**前端页面**: 检测中心 → RTSP流检测

**请求参数**:
```json
{
  "monitor_id": 1,
  "conf_threshold": 0.5,
  "iou_threshold": 0.4,
  "enable_tracking": true,
  "tracker_type": "bytetrack"
}
```

### 3.4 获取检测任务列表
**接口**: `GET /detection/tasks`
**权限**: Bearer Token
**前端页面**: 检测中心 → 任务管理

### 3.5 停止检测任务
**接口**: `POST /detection/task/{task_id}/stop`
**权限**: Bearer Token

## 🚗 4. 多目标追踪接口 (Tracking)

### 4.1 启动追踪
**接口**: `POST /tracking/start`
**权限**: Bearer Token
**前端页面**: 多目标追踪 → 实时追踪

**请求参数**:
```json
{
  "monitor_id": 1,
  "algorithm": "bytetrack",
  "config_params": {
    "track_thresh": 0.5,
    "track_buffer": 30,
    "match_thresh": 0.8
  }
}
```

### 4.2 停止追踪
**接口**: `POST /tracking/stop`
**权限**: Bearer Token

### 4.3 获取活动目标
**接口**: `GET /tracking/targets/active`
**权限**: Bearer Token
**前端页面**: 多目标追踪 → 目标轨迹分析

### 4.4 切换追踪算法
**接口**: `POST /tracking/switch-algorithm`
**权限**: Bearer Token
**前端页面**: 多目标追踪 → 追踪算法配置

## 🚨 5. 事故检测接口 (Accident)

### 5.1 启动事故检测
**接口**: `POST /accident/start`
**权限**: Bearer Token
**前端页面**: 事故检测 → 碰撞检测

### 5.2 获取事故记录
**接口**: `GET /accident/records`
**权限**: Bearer Token
**前端页面**: 事故检测 → 事故记录

**查询参数**:
- `monitor_id`: 监控点ID
- `accident_type`: 事故类型
- `start_date`: 开始日期
- `end_date`: 结束日期
- `page`: 页码
- `page_size`: 每页数量

**响应示例**:
```json
{
  "success": true,
  "data": {
    "records": [
      {
        "record_id": "ACC_20241224_001",
        "monitor_id": 3,
        "accident_type": "collision",
        "severity": "high",
        "location": {"x": 320, "y": 240},
        "description": "富阳互通两车追尾事故，无人员伤亡",
        "confidence": 0.89,
        "status": "resolved",
        "evidence_image": "/evidence/accident_001.jpg",
        "create_time": "2024-12-24T09:15:23"
      }
    ],
    "total": 2,
    "page": 1,
    "page_size": 20
  }
}
```

### 5.3 更新事故检测配置
**接口**: `POST /accident/config`
**权限**: Bearer Token
**前端页面**: 事故检测 → 预警配置

## 📊 6. 数据分析接口 (Analysis)

### 6.1 获取概览统计
**接口**: `GET /analysis/statistics/overview`
**权限**: Bearer Token
**前端页面**: 仪表板 → 系统概览

**响应示例**:
```json
{
  "success": true,
  "data": {
    "total_monitors": 6,
    "online_monitors": 6,
    "active_detections": 15,
    "active_targets": 8,
    "today_accidents": 2,
    "active_alarms": 1,
    "active_users": 2
  }
}
```

### 6.2 获取警报统计
**接口**: `GET /analysis/alarms`
**权限**: Bearer Token
**前端页面**: 数据分析 → 警报统计

### 6.3 获取交通流量统计
**接口**: `GET /analysis/traffic-flow`
**权限**: Bearer Token
**前端页面**: 数据分析 → 交通流量分析

### 6.4 导出数据
**接口**: `POST /analysis/export`
**权限**: Bearer Token
**前端页面**: 数据分析 → 数据导出

## ⚙️ 7. 系统管理接口 (System) [仅admin]

### 7.1 获取用户列表
**接口**: `GET /system/users`
**权限**: Bearer Token (仅admin)
**前端页面**: 系统管理 → 用户管理

### 7.2 获取系统配置
**接口**: `GET /system/config`
**权限**: Bearer Token (仅admin)
**前端页面**: 系统管理 → 系统配置

### 7.3 获取系统信息
**接口**: `GET /system/info`
**权限**: Bearer Token
**前端页面**: 系统管理 → 性能监控

## 🌐 8. WebSocket实时通信

### 连接WebSocket
```javascript
const socket = io('ws://localhost:5500', {
  auth: { token: 'your_jwt_token' }
});
```

### 事件监听
```javascript
// 监听检测结果 (检测中心页面)
socket.on('detection_result', (data) => {
  // 更新检测结果显示
});

// 监听追踪数据 (多目标追踪页面)
socket.on('tracking_data', (data) => {
  // 更新追踪轨迹显示
});

// 监听事故警报 (事故检测页面)
socket.on('accident_alert', (data) => {
  // 显示事故警报
});

// 监听系统警报 (仪表板页面)
socket.on('system_alert', (data) => {
  // 显示系统警报
});
```

## 🎯 前端页面与API对应关系

| 前端页面 | 主要API接口 | 数据表 |
|---------|------------|--------|
| 🏠 仪表板 | `/analysis/statistics/overview` | `v_system_overview` |
| 📹 监控管理 | `/monitor/list`, `/monitor/{id}` | `monitor` |
| 🎯 检测中心 | `/detection/*` | `unified_record` (detection) |
| 🚗 多目标追踪 | `/tracking/*` | `unified_record` (tracking) |
| 🚨 事故检测 | `/accident/*` | `unified_record` (accident) |
| 📊 数据分析 | `/analysis/*` | `unified_record` (alarm/traffic) |
| ⚙️ 系统管理 | `/system/*` | `user`, `system_config` |

## 📝 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 (operator访问admin功能) |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

**此API文档与前端开发计划完全对应，支持逐步开发验证！** 🚀
