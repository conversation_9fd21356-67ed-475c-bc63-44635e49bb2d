#!/usr/bin/env python
# -*- coding: utf-8 -*-
# 创建流量图标

from PIL import Image, ImageDraw

def create_chart_icon():
    """创建流量图标"""
    img = Image.new('RGBA', (32, 32), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 绘制折线图
    points = [(5, 25), (10, 15), (15, 20), (20, 10), (25, 5)]
    draw.line(points, fill=(60, 160, 230, 255), width=3)
    
    # 添加数据点
    for x, y in points:
        draw.ellipse([(x-2, y-2), (x+2, y+2)], fill=(60, 160, 230, 255))
    
    img.save('ui/img/chart.png')
    print("流量图标创建成功：ui/img/chart.png")

if __name__ == "__main__":
    create_chart_icon()
    print("图标创建完成！") 