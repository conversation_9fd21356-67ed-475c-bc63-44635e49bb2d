# -*- coding: utf-8 -*-
# <AUTHOR> 优化版多目标智能追踪控制台 - 统一UI风格
import sys
import time
import random

from PySide6.QtCore import Qt, QSize, Signal, QTimer, QPropertyAnimation, QEasingCurve
from PySide6.QtWidgets import (QApplication, QDialog, QFrame, QHBoxLayout, QVBoxLayout, QLabel, QPushButton,
                             QLineEdit, QListWidget, QListWidgetItem, QGridLayout, QScrollArea, QGroupBox,
                             QTableWidget, QTableWidgetItem, QHeaderView, QProgressBar, QWidget)
from PySide6.QtGui import QPixmap, QColor, QIcon, QImage

class MultiTargetTrackingDialog(QDialog):
    """
    基于Arco Design风格的高科技多目标追踪对话框，支持选择和输入目标车辆ID进行追踪，
    提供实时预览和动态效果。
    """

    # 定义信号
    tracking_started = Signal(list)  # 当开始追踪时发出，传递追踪的车辆ID列表
    tracking_stopped = Signal()  # 当停止追踪时发出
    target_added = Signal(int)  # 当添加目标时发出，传递目标ID
    target_removed = Signal(int)  # 当移除目标时发出，传递目标ID

    def __init__(self, parent=None):
        super(MultiTargetTrackingDialog, self).__init__(parent)

        # 设置窗口属性
        self.setWindowTitle("多目标智能追踪控制台")
        self.setMinimumSize(1200, 750)  # 增大窗口尺寸以容纳更多信息
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint | Qt.WindowMinimizeButtonHint)

        # 使用现代化浅色透明风格
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.5 rgba(248, 250, 252, 0.98),
                    stop:1 rgba(241, 245, 249, 0.95));
                border: 1px solid rgba(203, 213, 225, 0.6);
                border-radius: 15px;
            }
            QScrollBar:vertical {
                border: none;
                background: rgba(241, 245, 249, 0.8);
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.8), stop:1 rgba(37, 99, 235, 0.9));
                min-height: 30px;
                border-radius: 6px;
                border: 1px solid rgba(59, 130, 246, 0.6);
            }
            QScrollBar::handle:vertical:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(37, 99, 235, 0.9), stop:1 rgba(29, 78, 216, 1.0));
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                border: none;
                background: none;
            }
            QScrollBar:horizontal {
                border: none;
                background: rgba(0, 30, 70, 120);
                height: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(0, 150, 220, 200), stop:1 rgba(0, 180, 255, 240));
                min-width: 30px;
                border-radius: 6px;
                border: 1px solid rgba(0, 200, 255, 0.8);
            }
            QScrollBar::handle:horizontal:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(0, 180, 255, 240), stop:1 rgba(0, 220, 255, 255));
            }
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
                border: none;
                background: none;
            }
        """)

        # 设置应用图标
        self.setWindowIcon(QIcon("./ui/img/tracking.png"))

        # 追踪状态标志
        self.is_tracking = False

        # 追踪目标列表及状态
        self.tracked_targets = []  # 当前追踪的车辆ID列表
        self.available_targets = []  # 可用的目标车辆ID列表
        self.target_previews = {}  # 存储每个目标的预览图像
        self.target_statuses = {}  # 存储每个目标的状态信息 (速度、位置等)
        self.highlighted_target = None  # 当前高亮显示的目标ID

        # 设置定时器
        self.update_timer = QTimer(self)
        self.update_timer.timeout.connect(self.update_tracking_info)
        self.blinking_timer = QTimer(self)  # 用于控制闪烁效果
        self.blinking_timer.timeout.connect(self.update_blinking_effect)
        self.tracking_time = 0  # 追踪运行时间（秒）
        self.blink_state = False  # 闪烁状态

        # 系统状态
        self.system_status = "就绪"
        self.cpu_usage = 0
        self.memory_usage = 0

        # 初始化UI
        self.setup_ui()

        # 连接信号槽
        self.start_button.clicked.connect(self.toggle_tracking)
        self.close_button.clicked.connect(self.close)
        self.add_target_button.clicked.connect(self.add_target)
        self.clear_targets_button.clicked.connect(self.clear_targets)
        self.target_id_input.returnPressed.connect(self.add_target)  # 回车键添加目标
        self.tracked_targets_table.cellClicked.connect(self.highlight_target)  # 点击表格行高亮目标

        # 初始化动画效果
        self._setup_animations()

        # 加载可用目标
        self.load_available_targets()

        # 模拟系统资源变化
        self.resource_timer = QTimer(self)
        self.resource_timer.timeout.connect(self.update_system_resources)
        self.resource_timer.start(2000)  # 每2秒更新一次系统资源

        print("高科技多目标追踪控制台已创建")

    def setup_ui(self):
        # 主布局 - 使用垂直布局，上方为标题栏，下方为内容区域
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(8, 8, 8, 8)
        self.main_layout.setSpacing(0)

        # 添加标题栏
        self.create_title_bar()

        # 主内容区域 - 使用水平布局分为三个区域
        self.content_frame = QFrame()
        self.content_frame.setStyleSheet("""
            QFrame {
                background: rgba(0, 20, 45, 170);
                border-radius: 10px;
                border: 1px solid rgba(0, 130, 200, 0.6);
            }
        """)
        self.content_layout = QHBoxLayout(self.content_frame)
        self.content_layout.setContentsMargins(15, 15, 15, 15)
        self.content_layout.setSpacing(20)

        # 创建左侧面板 - 目标管理区域
        self.create_left_panel()

        # 创建中间面板 - 当前追踪目标
        self.create_center_panel()

        # 创建右侧面板 - 预览和详情
        self.create_right_panel()

        # 添加主内容区域到主布局
        self.main_layout.addWidget(self.content_frame)

        # 添加状态栏
        self.create_status_bar()

    def create_title_bar(self):
        # 标题栏包含标题和系统信息
        self.title_bar = QFrame()
        self.title_bar.setFixedHeight(70)
        self.title_bar.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(0, 80, 150, 180), stop:1 rgba(0, 150, 220, 200));
                border-top-left-radius: 15px;
                border-top-right-radius: 15px;
                border-bottom: 2px solid rgba(0, 200, 255, 0.8);
            }
        """)

        # 标题栏布局
        title_layout = QHBoxLayout(self.title_bar)
        title_layout.setContentsMargins(20, 0, 20, 0)

        # 标题图标和文字
        title_icon = QLabel()
        title_icon.setPixmap(QPixmap("./ui/img/tracking.png").scaled(40, 40, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        title_layout.addWidget(title_icon)

        title_label = QLabel("多目标智能追踪控制台 | Multi-Target Tracking System")
        title_label.setStyleSheet("""
            color: white;
            font-size: 20px;
            font-weight: bold;
            font-family: 'Microsoft YaHei';
        """)
        title_layout.addWidget(title_label)

        title_layout.addStretch()

        # 系统状态指示器
        self.system_indicator = QFrame()
        self.system_indicator.setFixedSize(18, 18)
        self.system_indicator.setStyleSheet("""
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(0, 255, 100, 255), stop:1 rgba(0, 200, 80, 255));
            border-radius: 9px;
            border: 2px solid rgba(0, 255, 150, 0.8);
        """)
        title_layout.addWidget(self.system_indicator)

        # 系统状态文本
        self.system_status_label = QLabel("系统就绪")
        self.system_status_label.setStyleSheet("""
            color: rgba(0, 255, 150, 255);
            font-size: 16px;
            font-weight: bold;
            font-family: 'Microsoft YaHei';
        """)
        title_layout.addWidget(self.system_status_label)

        # 添加到主布局
        self.main_layout.addWidget(self.title_bar)

    def create_left_panel(self):
        # 左侧面板 - 目标管理区域
        self.left_panel = QFrame()
        self.left_panel.setFixedWidth(320)  # 增大宽度
        self.left_panel.setStyleSheet("""
            QFrame {
                background: rgba(0, 30, 70, 120);
                border-radius: 10px;
                border: 1px solid rgba(0, 150, 230, 0.4);
            }
            QLabel {
                color: rgb(180, 220, 255);
                font-size: 14px;
                font-family: 'Microsoft YaHei';
            }
            QGroupBox {
                color: rgb(0, 220, 255);
                font-size: 14px;
                font-weight: bold;
                border: 1px solid rgba(0, 130, 200, 0.6);
                border-radius: 8px;
                margin-top: 16px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 8px;
                background: rgba(0, 30, 70, 120);
            }
        """)

        # 左侧面板布局
        self.left_layout = QVBoxLayout(self.left_panel)
        self.left_layout.setContentsMargins(12, 12, 12, 12)
        self.left_layout.setSpacing(10)

        # 系统状态区域
        self.system_group = QGroupBox("系统监控")
        self.system_group_layout = QVBoxLayout(self.system_group)

        # CPU和内存使用情况
        cpu_layout = QHBoxLayout()
        cpu_label = QLabel("CPU使用率:")
        self.cpu_progress = QProgressBar()
        self.cpu_progress.setRange(0, 100)
        self.cpu_progress.setValue(30)  # 初始值
        self.cpu_progress.setTextVisible(True)
        self.cpu_progress.setFormat("%p%")
        self.cpu_progress.setStyleSheet("""
            QProgressBar {
                background-color: #1D2129;
                color: white;
                border-radius: 3px;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #4080FF;
                border-radius: 3px;
            }
        """)
        cpu_layout.addWidget(cpu_label)
        cpu_layout.addWidget(self.cpu_progress)
        self.system_group_layout.addLayout(cpu_layout)

        memory_layout = QHBoxLayout()
        memory_label = QLabel("内存使用率:")
        self.memory_progress = QProgressBar()
        self.memory_progress.setRange(0, 100)
        self.memory_progress.setValue(45)  # 初始值
        self.memory_progress.setTextVisible(True)
        self.memory_progress.setFormat("%p%")
        self.memory_progress.setStyleSheet("""
            QProgressBar {
                background-color: #1D2129;
                color: white;
                border-radius: 3px;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #00B42A;
                border-radius: 3px;
            }
        """)
        memory_layout.addWidget(memory_label)
        memory_layout.addWidget(self.memory_progress)
        self.system_group_layout.addLayout(memory_layout)

        # 运行时间指示器
        runtime_layout = QHBoxLayout()
        runtime_label = QLabel("运行时间:")
        self.runtime_value = QLabel("00:00:00")
        self.runtime_value.setStyleSheet("color: #4080FF; font-weight: bold;")
        runtime_layout.addWidget(runtime_label)
        runtime_layout.addWidget(self.runtime_value)
        self.system_group_layout.addLayout(runtime_layout)

        # 添加到左侧面板
        self.left_layout.addWidget(self.system_group)

        # 可用目标组
        target_selection_group = QGroupBox("可用目标")
        target_selection_layout = QVBoxLayout(target_selection_group)

        # 目标搜索区
        search_layout = QHBoxLayout()
        self.target_search = QLineEdit()
        self.target_search.setPlaceholderText("搜索目标ID...")
        self.target_search.setStyleSheet("""
            QLineEdit {
                background-color: #1D2129;
                color: white;
                border: 1px solid #4E5969;
                border-radius: 4px;
                padding: 5px;
                font-size: 13px;
            }
            QLineEdit:focus {
                border: 1px solid #4080FF;
            }
        """)
        search_layout.addWidget(self.target_search)

        # 搜索按钮
        search_button = QPushButton("搜索")
        search_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(0, 80, 150, 180), stop:1 rgba(0, 150, 220, 200));
                color: white;
                border-radius: 8px;
                padding: 8px 15px;
                font-weight: bold;
                font-family: 'Microsoft YaHei';
                border: 1px solid rgba(0, 200, 255, 0.8);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(0, 100, 180, 220), stop:1 rgba(0, 180, 255, 240));
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(0, 60, 120, 200), stop:1 rgba(0, 120, 180, 220));
            }
        """)
        search_layout.addWidget(search_button)
        target_selection_layout.addLayout(search_layout)

        # 可用目标列表
        self.available_targets_list = QListWidget()
        self.available_targets_list.setStyleSheet("""
            QListWidget {
                background-color: #1D2129;
                color: white;
                border: 1px solid #2A2E39;
                border-radius: 4px;
                padding: 5px;
                font-size: 13px;
            }
            QListWidget::item {
                border-bottom: 1px solid #2A2E39;
                padding: 5px;
            }
            QListWidget::item:selected {
                background-color: #4080FF;
                color: white;
            }
            QListWidget::item:hover {
                background-color: #2B5ED9;
            }
        """)
        target_selection_layout.addWidget(self.available_targets_list)

        # 目标输入区域
        target_input_layout = QHBoxLayout()
        self.target_id_input = QLineEdit()
        self.target_id_input.setPlaceholderText("输入车辆ID或从列表选择")
        self.target_id_input.setStyleSheet("""
            QLineEdit {
                background-color: #1D2129;
                color: white;
                border: 1px solid #4E5969;
                border-radius: 4px;
                padding: 5px;
                font-size: 13px;
            }
            QLineEdit:focus {
                border: 1px solid #4080FF;
            }
        """)
        target_input_layout.addWidget(self.target_id_input)

        # 添加按钮
        self.add_target_button = QPushButton("添加")
        self.add_target_button.setStyleSheet("""
            QPushButton {
                background-color: #00B42A;
                color: white;
                border-radius: 4px;
                padding: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #00C83C;
            }
        """)
        target_input_layout.addWidget(self.add_target_button)
        target_selection_layout.addLayout(target_input_layout)

        # 清除按钮
        self.clear_targets_button = QPushButton("清除所有追踪目标")
        self.clear_targets_button.setStyleSheet("""
            QPushButton {
                background-color: #F53F3F;
                color: white;
                border-radius: 4px;
                padding: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #FC6161;
            }
        """)
        target_selection_layout.addWidget(self.clear_targets_button)

        # 添加到左侧面板
        self.left_layout.addWidget(target_selection_group)

        # 添加左侧面板到布局
        self.content_layout.addWidget(self.left_panel)

    def create_center_panel(self):
        # 中间面板 - 当前追踪目标
        self.center_panel = QFrame()
        self.center_panel.setMinimumWidth(300)
        self.center_panel.setStyleSheet("""
            QFrame {
                background-color: #232734;
                border-radius: 8px;
                border: 1px solid #2A2E39;
            }
            QLabel {
                color: #F2F3F5;
                font-size: 14px;
            }
            QGroupBox {
                color: #86909C;
                font-size: 14px;
                font-weight: bold;
                border: 1px solid #2A2E39;
                border-radius: 4px;
                margin-top: 16px;
                padding-top: 8px;
            }
        """)

        # 中间面板布局
        self.center_layout = QVBoxLayout(self.center_panel)
        self.center_layout.setContentsMargins(12, 12, 12, 12)
        self.center_layout.setSpacing(12)

        # 标题和操作区
        header_layout = QHBoxLayout()

        # 当前追踪目标标题
        title_label = QLabel("当前追踪目标")
        title_label.setStyleSheet("""
            color: #F2F3F5;
            font-size: 16px;
            font-weight: bold;
        """)
        header_layout.addWidget(title_label)

        # 计数标签
        self.target_count_label = QLabel("0/20")
        self.target_count_label.setStyleSheet("""
            color: #4080FF;
            font-size: 14px;
            background-color: rgba(64, 128, 255, 0.1);
            border-radius: 10px;
            padding: 2px 8px;
        """)
        header_layout.addWidget(self.target_count_label)

        header_layout.addStretch()

        # 追踪控制按钮
        self.start_button = QPushButton("开始追踪")
        self.start_button.setMinimumWidth(120)
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #00B42A;
                color: white;
                border-radius: 4px;
                padding: 8px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #00C83C;
            }
        """)
        header_layout.addWidget(self.start_button)

        self.center_layout.addLayout(header_layout)

        # 追踪目标表格
        self.tracked_targets_table = QTableWidget()
        self.tracked_targets_table.setColumnCount(4)
        self.tracked_targets_table.setHorizontalHeaderLabels(["ID", "类型", "状态", "关联度"])
        self.tracked_targets_table.setStyleSheet("""
            QTableWidget {
                background-color: #1D2129;
                color: white;
                gridline-color: #2A2E39;
                border: 1px solid #2A2E39;
                border-radius: 4px;
                font-size: 13px;
            }
            QTableWidget::item {
                padding: 5px;
            }
            QTableWidget::item:selected {
                background-color: #4080FF;
                color: white;
            }
            QHeaderView::section {
                background-color: #232734;
                color: #86909C;
                padding: 5px;
                border: none;
                border-right: 1px solid #2A2E39;
                border-bottom: 1px solid #2A2E39;
            }
        """)
        self.tracked_targets_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.tracked_targets_table.verticalHeader().setVisible(False)
        self.tracked_targets_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.tracked_targets_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.center_layout.addWidget(self.tracked_targets_table)

        # 追踪状态信息
        status_group = QGroupBox("追踪状态")
        status_layout = QGridLayout(status_group)

        # 使用加密密度版的状态信息显示
        self.add_info_item("追踪状态:", "未开始", 0, 0, status_layout)
        self.add_info_item("目标数量:", "0", 1, 0, status_layout)
        self.add_info_item("运行时间:", "0秒", 0, 2, status_layout)
        self.add_info_item("系统状态:", "就绪", 1, 2, status_layout)

        self.center_layout.addWidget(status_group)

        # 关闭按钮
        self.close_button = QPushButton("关闭")
        self.close_button.setStyleSheet("""
            QPushButton {
                background-color: #4E5969;
                color: white;
                border-radius: 4px;
                padding: 8px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #5E6B7D;
            }
        """)
        self.center_layout.addWidget(self.close_button)

        # 添加中间面板到布局
        self.content_layout.addWidget(self.center_panel)

    def create_right_panel(self):
        # 右侧面板 - 智能目标预览分析区域
        self.right_panel = QFrame()
        self.right_panel.setStyleSheet("""
            QFrame {
                background: rgba(0, 30, 70, 120);
                border-radius: 10px;
                border: 1px solid rgba(0, 150, 230, 0.4);
            }
            QLabel {
                color: rgb(180, 220, 255);
                font-family: 'Microsoft YaHei';
            }
        """)

        # 右侧面板布局
        self.right_layout = QVBoxLayout(self.right_panel)
        self.right_layout.setContentsMargins(15, 15, 15, 15)
        self.right_layout.setSpacing(15)

        # 顶部信息区
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: rgba(0, 20, 45, 170);
                border-radius: 8px;
                border: 1px solid rgba(0, 130, 200, 0.6);
            }
        """)
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(15, 10, 15, 10)

        # 右侧标题
        title_label = QLabel("智能目标预览分析")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: rgb(0, 220, 255);
            font-family: 'Microsoft YaHei';
        """)
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # 追踪状态指示器
        self.tracking_status_indicator = QLabel("● 待机中")
        self.tracking_status_indicator.setStyleSheet("""
            color: rgb(255, 165, 0);
            font-size: 14px;
            font-weight: bold;
            font-family: 'Microsoft YaHei';
        """)
        header_layout.addWidget(self.tracking_status_indicator)

        self.right_layout.addWidget(header_frame)

        # 预览区域 - 只有在有追踪目标时才显示内容
        self.preview_scroll = QScrollArea()
        self.preview_scroll.setWidgetResizable(True)
        self.preview_scroll.setStyleSheet("""
            QScrollArea {
                border: none;
                background: transparent;
            }
        """)

        self.preview_container = QWidget()
        self.preview_container.setStyleSheet("background: transparent;")
        self.preview_grid = QGridLayout(self.preview_container)
        self.preview_grid.setContentsMargins(10, 10, 10, 10)
        self.preview_grid.setSpacing(15)
        self.preview_grid.setAlignment(Qt.AlignTop)

        self.preview_scroll.setWidget(self.preview_container)

        # 初始状态：显示提示信息而不是占位符
        self.create_empty_state_widget()

        self.right_layout.addWidget(self.preview_scroll)

    def create_empty_state_widget(self):
        """创建空状态提示组件"""
        self.empty_state_widget = QFrame()
        self.empty_state_widget.setStyleSheet("""
            QFrame {
                background: rgba(0, 40, 80, 100);
                border: 2px dashed rgba(0, 150, 255, 0.3);
                border-radius: 12px;
            }
        """)

        empty_layout = QVBoxLayout(self.empty_state_widget)
        empty_layout.setContentsMargins(30, 40, 30, 40)
        empty_layout.setSpacing(15)

        # 图标
        icon_label = QLabel("🎯")
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("""
            font-size: 48px;
            color: rgba(0, 150, 255, 0.6);
        """)
        empty_layout.addWidget(icon_label)

        # 主要提示文字
        main_text = QLabel("智能目标预览分析")
        main_text.setAlignment(Qt.AlignCenter)
        main_text.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: rgb(0, 180, 255);
            font-family: 'Microsoft YaHei';
        """)
        empty_layout.addWidget(main_text)

        # 说明文字
        desc_text = QLabel("选择并开始追踪目标后\n此区域将显示实时车辆信息")
        desc_text.setAlignment(Qt.AlignCenter)
        desc_text.setStyleSheet("""
            font-size: 14px;
            color: rgba(180, 220, 255, 0.8);
            font-family: 'Microsoft YaHei';
            line-height: 1.5;
        """)
        empty_layout.addWidget(desc_text)

        # 添加到预览网格
        self.preview_grid.addWidget(self.empty_state_widget, 0, 0)

        # 分析区域
        analytics_group = QGroupBox("目标追踪分析")
        analytics_group.setStyleSheet("""
            QGroupBox {
                color: #86909C;
                font-size: 14px;
                font-weight: bold;
                border: 1px solid #2A2E39;
                border-radius: 4px;
                margin-top: 16px;
                padding: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 5px;
            }
        """)

        analytics_layout = QVBoxLayout(analytics_group)

        # 添加一些高科技感的预设数据
        tracking_data_label = QLabel("追踪精确度: <span style='color:#00C875; font-weight:bold;'>96.7%</span>")
        analytics_layout.addWidget(tracking_data_label)

        stats_label = QLabel("平均目标速度: <span style='color:#FF7D00; font-weight:bold;'>45.2 km/h</span>")
        analytics_layout.addWidget(stats_label)

        motion_label = QLabel("目标跟踪算法: <span style='color:#4080FF; font-weight:bold;'>深度学习增强型</span>")
        analytics_layout.addWidget(motion_label)

        self.right_layout.addWidget(analytics_group)

        # 添加右侧面板到布局
        self.content_layout.addWidget(self.right_panel)

    def create_status_bar(self):
        # 状态栏
        status_bar = QFrame()
        status_bar.setFixedHeight(30)
        status_bar.setStyleSheet("""
            QFrame {
                background-color: #1D2129;
                border-top: 1px solid #2A2E39;
            }
        """)

        status_layout = QHBoxLayout(status_bar)
        status_layout.setContentsMargins(10, 0, 10, 0)

        # 版本信息
        version_label = QLabel("v1.0.0")
        version_label.setStyleSheet("color: #86909C; font-size: 12px;")
        status_layout.addWidget(version_label)

        status_layout.addStretch()

        # 当前时间
        self.time_label = QLabel()
        self.time_label.setStyleSheet("color: #86909C; font-size: 12px;")
        self.time_label.setText(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))
        status_layout.addWidget(self.time_label)

        # 更新时间显示
        self.time_timer = QTimer(self)
        self.time_timer.timeout.connect(self.update_time)
        self.time_timer.start(1000)  # 每秒更新一次

        # 添加到主布局
        self.main_layout.addWidget(status_bar)

    def create_preview_placeholder(self):
        # 创建预览占位符
        placeholder = QFrame()
        placeholder.setFixedSize(200, 180)
        placeholder.setStyleSheet("""
            QFrame {
                background-color: #1D2129;
                border: 1px dashed #4E5969;
                border-radius: 8px;
            }
        """)

        placeholder_layout = QVBoxLayout(placeholder)
        placeholder_layout.setAlignment(Qt.AlignCenter)

        icon_label = QLabel()
        icon_label.setPixmap(QPixmap("./ui/img/tracking.png").scaled(32, 32, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        placeholder_layout.addWidget(icon_label)

        text_label = QLabel("目标预览区")
        text_label.setStyleSheet("color: #86909C; font-size: 14px;")
        placeholder_layout.addWidget(text_label)

        hint_label = QLabel("选择目标并开始追踪后显示")
        hint_label.setStyleSheet("color: #4E5969; font-size: 12px;")
        placeholder_layout.addWidget(hint_label)

        return placeholder

    def _setup_animations(self):
        # 设置动画效果
        self.highlight_animation = QPropertyAnimation(self, b'highlight_alpha')
        self.highlight_animation.setStartValue(0)
        self.highlight_animation.setEndValue(100)
        self.highlight_animation.setDuration(300)
        self.highlight_animation.setEasingCurve(QEasingCurve.OutCubic)

        # 启动闪烁定时器
        self.blinking_timer.start(500)  # 每500毫秒闪烁一次

    def update_time(self):
        # 更新状态栏中的时间显示
        self.time_label.setText(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))

    def update_system_resources(self):
        # 模拟更新CPU和内存使用情况
        self.cpu_usage = min(100, self.cpu_usage + random.randint(-5, 8))
        if self.cpu_usage < 10:
            self.cpu_usage = 10 + random.randint(0, 10)

        self.memory_usage = min(100, self.memory_usage + random.randint(-3, 5))
        if self.memory_usage < 20:
            self.memory_usage = 20 + random.randint(0, 15)

        # 更新UI显示
        self.cpu_progress.setValue(self.cpu_usage)
        self.memory_progress.setValue(self.memory_usage)

    def update_blinking_effect(self):
        # 当正在追踪时产生闪烁效果
        if self.is_tracking:
            self.blink_state = not self.blink_state

            # 更新系统状态指示器
            if self.blink_state:
                self.system_indicator.setStyleSheet("background-color: #00C875; border-radius: 7px;")
            else:
                self.system_indicator.setStyleSheet("background-color: #F53F3F; border-radius: 7px;")

            # 如果有高亮目标，对其预览框进行闪烁效果
            if self.highlighted_target is not None and self.highlighted_target in self.target_previews:
                preview_frame = self.target_previews[self.highlighted_target]['frame']

                if self.blink_state:
                    preview_frame.setStyleSheet("""
                        background-color: #2B579A;
                        border: 2px solid #4080FF;
                        border-radius: 8px;
                    """)
                else:
                    preview_frame.setStyleSheet("""
                        background-color: #1D2129;
                        border: 2px solid #4080FF;
                        border-radius: 8px;
                    """)

    def highlight_target(self, row, column):
        # 高亮显示选中的目标
        if row >= 0 and self.tracked_targets_table.rowCount() > 0:
            target_id = int(self.tracked_targets_table.item(row, 0).text())

            # 设置当前高亮目标
            self.highlighted_target = target_id

            # 更新高亮目标信息显示
            self.highlighted_info.setText(f"目标 ID: {target_id}")

            # 如果该目标有预览框，将其设置为高亮状态
            if target_id in self.target_previews:
                for t_id, preview in self.target_previews.items():
                    if t_id == target_id:
                        preview['frame'].setStyleSheet("""
                            background-color: #1D2129;
                            border: 2px solid #4080FF;
                            border-radius: 8px;
                        """)
                    else:
                        preview['frame'].setStyleSheet("""
                            background-color: #1D2129;
                            border: 1px solid #2A2E39;
                            border-radius: 8px;
                        """)

        # 添加状态信息字段
        self.add_info_item("追踪状态:", "未开始", 0, 0)
        self.add_info_item("目标数量:", "0", 1, 0)
        self.add_info_item("运行时间:", "0秒", 2, 0)
        self.add_info_item("系统状态:", "就绪", 3, 0)

        self.center_layout.addWidget(self.status_group)

        # 按钮区域
        self.buttons_layout = QHBoxLayout()
        self.start_button = QPushButton("开始追踪")
        self.start_button.setMinimumHeight(40)
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: rgb(0, 150, 80);
                color: white;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: rgb(0, 170, 90);
            }
        """)
        self.close_button = QPushButton("关闭")
        self.close_button.setMinimumHeight(40)
        self.close_button.setStyleSheet("""
            QPushButton {
                background-color: rgb(100, 100, 100);
                color: white;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: rgb(120, 120, 120);
            }
        """)
        self.buttons_layout.addWidget(self.start_button)
        self.buttons_layout.addWidget(self.close_button)
        self.center_layout.addLayout(self.buttons_layout)

        self.main_layout.addWidget(self.center_panel, 1)

        # 右侧面板 - 预览
        self.right_panel = QFrame(self)
        self.right_panel.setFrameShape(QFrame.StyledPanel)
        self.right_panel.setStyleSheet("background-color: rgb(45, 50, 60); border-radius: 8px;")
        self.right_layout = QVBoxLayout(self.right_panel)
        self.right_layout.setContentsMargins(10, 10, 10, 10)

        # 右侧标题
        self.right_title = QLabel("追踪预览")
        self.right_title.setStyleSheet("color: white; font-size: 16px; font-weight: bold;")
        self.right_layout.addWidget(self.right_title)

        # 预览区域
        self.preview_area = QScrollArea()
        self.preview_area.setWidgetResizable(True)
        self.preview_area.setStyleSheet("background-color: rgb(35, 40, 48); border-radius: 4px;")
        self.preview_container = QWidget()
        self.preview_container.setStyleSheet("background-color: rgb(35, 40, 48);")
        self.preview_grid = QGridLayout(self.preview_container)
        self.preview_grid.setContentsMargins(10, 10, 10, 10)
        self.preview_grid.setSpacing(10)
        self.preview_area.setWidget(self.preview_container)
        self.right_layout.addWidget(self.preview_area)

        # 更新状态信息
        self.update_status_field("追踪状态:", "已停止")
        self.update_status_field("系统状态:", "就绪")

        # 停止定时器
        self.update_timer.stop()

        # 发送停止追踪信号
        self.tracking_stopped.emit()
        print("停止追踪所有目标")

    def add_info_item(self, label_text, value_text, row, col, layout=None):
        """添加信息项到指定布局中"""
        # 如果没有指定布局，使用状态布局
        if layout is None:
            layout = self.status_layout if hasattr(self, 'status_layout') else None
            if layout is None:
                print("错误: 未指定布局")
                return

        # 创建标签文本
        label = QLabel(label_text)
        label.setStyleSheet("color: #86909C; font-size: 13px;")
        layout.addWidget(label, row, col)

        # 创建值文本
        value = QLabel(value_text)
        value.setStyleSheet("color: #F2F3F5; font-size: 13px; font-weight: bold;")
        layout.addWidget(value, row, col + 1)

        # 保存引用以便于后续更新
        field_name = f"{label_text.replace(':', '').strip().lower()}_value"
        setattr(self, field_name, value)

    def update_status_field(self, label_text, value_text):
        """更新状态字段的值"""
        field_name = f"{label_text.replace(':', '').strip().lower()}_value"
        if hasattr(self, field_name):
            getattr(self, field_name).setText(value_text)

            # 根据状态更新颜色
            if '追踪状态' in label_text:
                if '追踪中' in value_text:
                    getattr(self, field_name).setStyleSheet("color: #00C875; font-weight: bold;")
                elif '已停止' in value_text or '未开始' in value_text:
                    getattr(self, field_name).setStyleSheet("color: #F53F3F; font-weight: bold;")
            elif '系统状态' in label_text:
                if '正常运行' in value_text or '就绪' in value_text:
                    getattr(self, field_name).setStyleSheet("color: #00C875; font-weight: bold;")
                else:
                    getattr(self, field_name).setStyleSheet("color: #FF7D00; font-weight: bold;")

    def add_target(self):
        """添加目标到追踪列表"""
        target_id = self.target_id_input.text().strip()
        if not target_id:
            # 检查是否有从列表中选择的目标
            selected_items = self.available_targets_list.selectedItems()
            if not selected_items:
                return
            target_id = selected_items[0].text().split(':')[0].strip()

        try:
            target_id = int(target_id)
            if target_id in self.tracked_targets:
                return  # 避免重复添加

            # 添加到追踪列表
            self.tracked_targets.append(target_id)

            # 更新表格显示
            self.update_tracked_targets_table()

            # 清空输入框
            self.target_id_input.clear()

            # 更新目标数量显示
            self.target_count_label.setText(f"{len(self.tracked_targets)}/20")

            # 更新状态字段
            self.update_status_field("目标数量:", str(len(self.tracked_targets)))

            # 发送信号
            self.target_added.emit(target_id)

            print(f"添加目标 ID: {target_id}")
        except ValueError:
            print("目标ID必须是数字")

    def clear_targets(self):
        """清除所有追踪目标"""
        self.tracked_targets.clear()

        # 清空表格
        self.tracked_targets_table.setRowCount(0)

        # 更新计数
        self.target_count_label.setText("0/20")
        self.update_status_field("目标数量:", "0")

        # 清除预览
        for i in reversed(range(self.preview_grid.count())):
            widget = self.preview_grid.itemAt(i).widget()
            if widget is not None:
                widget.setParent(None)

        self.target_previews.clear()
        print("已清除所有追踪目标")

    def update_tracked_targets_table(self):
        """更新追踪目标表格显示"""
        self.tracked_targets_table.setRowCount(len(self.tracked_targets))

        for idx, target_id in enumerate(self.tracked_targets):
            # ID列
            id_item = QTableWidgetItem(str(target_id))
            id_item.setTextAlignment(Qt.AlignCenter)
            self.tracked_targets_table.setItem(idx, 0, id_item)

            # 类型列 (默认为车辆)
            type_item = QTableWidgetItem("车辆")
            type_item.setTextAlignment(Qt.AlignCenter)
            self.tracked_targets_table.setItem(idx, 1, type_item)

            # 状态列
            status_text = "追踪中" if self.is_tracking else "就绪"
            status_item = QTableWidgetItem(status_text)
            status_item.setTextAlignment(Qt.AlignCenter)
            if self.is_tracking:
                status_item.setForeground(QColor("#00C875"))  # 绿色
            else:
                status_item.setForeground(QColor("#86909C"))  # 灰色
            self.tracked_targets_table.setItem(idx, 2, status_item)

            # 关联度列 (随机生成数据)
            confidence = random.randint(80, 99)
            confidence_item = QTableWidgetItem(f"{confidence}%")
            confidence_item.setTextAlignment(Qt.AlignCenter)
            if confidence >= 95:
                confidence_item.setForeground(QColor("#00C875"))  # 高绿色
            elif confidence >= 85:
                confidence_item.setForeground(QColor("#4080FF"))  # 中蓝色
            else:
                confidence_item.setForeground(QColor("#FF7D00"))  # 低黄色
            self.tracked_targets_table.setItem(idx, 3, confidence_item)

        # 更新目标数量状态字段
        self.update_status_field("目标数量:", str(len(self.tracked_targets)))

    def load_available_targets(self):
        """加载可用的目标列表"""
        # 这里应该从主应用获取可用目标
        # 但现在先模拟一些数据
        self.available_targets = [i+1 for i in range(20)]  # 生成 1-20 的ID
        self.available_targets_list.clear()

        for target_id in self.available_targets:
            item = QListWidgetItem(f"{target_id}: 车辆")
            item.setData(Qt.UserRole, target_id)
            self.available_targets_list.addItem(item)

    def update_available_targets(self, targets_data):
        """更新可用目标列表

        Args:
            targets_data: 包含ID和类型的目标列表
        """
        if not targets_data:
            return

        self.available_targets = []
        self.available_targets_list.clear()

        for target in targets_data:
            target_id = target.get('id')
            target_class = target.get('class', '车辆')

            self.available_targets.append(target_id)
            item = QListWidgetItem(f"{target_id}: {target_class}")
            item.setData(Qt.UserRole, target_id)
            self.available_targets_list.addItem(item)

    def add_preview_for_target(self, target_id, pixmap):
        """在预览区添加目标的预览图"""
        if target_id not in self.tracked_targets:
            return

        # 先移除占位符(如果这是第一个预览)
        if self.preview_grid.count() == 1:
            item = self.preview_grid.itemAt(0)
            if item and item.widget() and isinstance(item.widget(), QFrame):
                item.widget().setParent(None)

        # 计算行和列位置用于添加新预览
        row = len(self.target_previews) // 2  # 两列布局
        col = len(self.target_previews) % 2

        if target_id not in self.target_previews:
            # 创建新的预览框
            preview_frame = QFrame()
            preview_frame.setFixedSize(200, 180)
            preview_frame.setStyleSheet("""
                background-color: #1D2129;
                border: 1px solid #2A2E39;
                border-radius: 8px;
            """)
            preview_layout = QVBoxLayout(preview_frame)
            preview_layout.setContentsMargins(8, 8, 8, 8)
            preview_layout.setSpacing(6)

            # 标题栏
            header_layout = QHBoxLayout()

            id_label = QLabel(f"ID: {target_id}")
            id_label.setStyleSheet("""
                color: #F2F3F5;
                font-weight: bold;
                font-size: 14px;
            """)
            header_layout.addWidget(id_label)

            type_label = QLabel("车辆")
            type_label.setStyleSheet("""
                color: #4080FF;
                background-color: rgba(64, 128, 255, 0.1);
                border-radius: 4px;
                padding: 2px 6px;
                font-size: 11px;
            """)
            header_layout.addWidget(type_label)
            header_layout.addStretch()

            # 添加状态指示器
            indicator = QLabel()
            indicator.setFixedSize(12, 12)
            indicator.setStyleSheet("""
                background-color: #00C875;
                border-radius: 6px;
            """)
            header_layout.addWidget(indicator)

            preview_layout.addLayout(header_layout)

            # 图像预览
            image_container = QFrame()
            image_container.setStyleSheet("""
                background-color: #121212;
                border-radius: 4px;
            """)
            image_layout = QVBoxLayout(image_container)
            image_layout.setContentsMargins(1, 1, 1, 1)

            image_label = QLabel()
            image_label.setMinimumSize(QSize(180, 100))
            image_label.setScaledContents(True)
            image_label.setAlignment(Qt.AlignCenter)
            image_layout.addWidget(image_label)

            preview_layout.addWidget(image_container)

            # 添加一些统计信息
            info_layout = QHBoxLayout()

            # 置信度
            conf_layout = QVBoxLayout()
            conf_label = QLabel("置信度")
            conf_label.setStyleSheet("color: #86909C; font-size: 11px;")
            conf_layout.addWidget(conf_label)

            conf_value = QLabel(f"{random.randint(90, 99)}%")
            conf_value.setStyleSheet("color: #00C875; font-weight: bold;")
            conf_layout.addWidget(conf_value)
            info_layout.addLayout(conf_layout)

            # 速度
            speed_layout = QVBoxLayout()
            speed_label = QLabel("速度")
            speed_label.setStyleSheet("color: #86909C; font-size: 11px;")
            speed_layout.addWidget(speed_label)

            speed_value = QLabel(f"{random.randint(30, 80)} km/h")
            speed_value.setStyleSheet("color: #FF7D00; font-weight: bold;")
            speed_layout.addWidget(speed_value)
            info_layout.addLayout(speed_layout)

            preview_layout.addLayout(info_layout)

            # 保存组件引用
            self.target_previews[target_id] = {
                'frame': preview_frame,
                'image': image_label,
                'indicator': indicator,
                'conf': conf_value,
                'speed': speed_value
            }

            # 添加到网格布局
            self.preview_grid.addWidget(preview_frame, row, col)

        # 更新图像
        if pixmap and not pixmap.isNull():
            self.target_previews[target_id]['image'].setPixmap(pixmap)
        else:
            # 如果没有图像，显示占位图
            placeholder_pixmap = QPixmap("./ui/img/tracking.png").scaled(
                180, 100, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            self.target_previews[target_id]['image'].setPixmap(placeholder_pixmap)

        # 更新统计数据 (在实际应用中这应该是真实数据)
        self.target_previews[target_id]['conf'].setText(f"{random.randint(90, 99)}%")
        self.target_previews[target_id]['speed'].setText(f"{random.randint(30, 80)} km/h")

        # 让UI刷新
        QApplication.processEvents()

    def update_preview(self, target_id, pixmap):
        """更新目标的预览图"""
        # 如果目标已经有预览，更新它
        if target_id in self.target_previews and pixmap:
            self.target_previews[target_id]['image'].setPixmap(pixmap)
            # 更新置信度和速度
            self.target_previews[target_id]['conf'].setText(f"{random.randint(90, 99)}%")
            self.target_previews[target_id]['speed'].setText(f"{random.randint(30, 80)} km/h")
            # 更新状态指示器
            self.target_previews[target_id]['indicator'].setStyleSheet("background-color: #00C875; border-radius: 6px;")
        # 如果目标没有预览，创建一个
        elif target_id in self.tracked_targets and pixmap:
            self.add_preview_for_target(target_id, pixmap)

    def update_tracking_info(self):
        """定时更新追踪信息"""
        if self.is_tracking:
            self.tracking_time += 1
            self.update_status_field("运行时间:", f"{self.tracking_time}秒")
            self.update_status_field("目标数量:", str(len(self.tracked_targets)))

    def toggle_tracking(self):
        """切换追踪状态"""
        self.is_tracking = not self.is_tracking

        if self.is_tracking:
            # 检查是否有目标被选择
            if not self.tracked_targets:
                # 没有选择任何目标，不能开始追踪
                self.is_tracking = False
                print("请至少添加一个要追踪的目标")
                return

            # 重置追踪时间
            self.tracking_time = 0

            # 开始追踪
            self.start_button.setText("停止追踪")
            self.start_button.setStyleSheet("""
                QPushButton {
                    background-color: rgb(204, 60, 60);
                    color: white;
                    border-radius: 5px;
                    font-weight: bold;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background-color: rgb(228, 80, 80);
                }
            """)

            # 禁用添加和清除按钮
            self.add_target_button.setEnabled(False)
            self.clear_targets_button.setEnabled(False)
            self.target_id_input.setEnabled(False)
            self.available_targets_list.setEnabled(False)

            # 更新所有预览的状态指示器
            for target_id, preview in self.target_previews.items():
                preview['indicator'].setStyleSheet("background-color: #00C875; border-radius: 6px;")

            # 更新状态信息
            self.update_status_field("追踪状态:", "追踪中")
            self.update_status_field("系统状态:", "正常运行")

            # 开始定时器
            self.update_timer.start(1000)  # 每秒更新一次

            # 发送开始追踪信号，传递追踪目标ID列表
            self.tracking_started.emit(self.tracked_targets)
            print(f"开始追踪 {len(self.tracked_targets)} 个目标: {self.tracked_targets}")
        else:
            # 停止追踪
            self.start_button.setText("开始追踪")
            self.start_button.setStyleSheet("""
                QPushButton {
                    background-color: rgb(0, 150, 80);
                    color: white;
                    border-radius: 5px;
                    font-weight: bold;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background-color: rgb(0, 170, 90);
                }
            """)

            # 重新启用添加和清除按钮
            self.add_target_button.setEnabled(True)
            self.clear_targets_button.setEnabled(True)
            self.target_id_input.setEnabled(True)
            self.available_targets_list.setEnabled(True)

            # 更新所有预览的状态指示器
            for target_id, preview in self.target_previews.items():
                preview['indicator'].setStyleSheet("background-color: #86909C; border-radius: 6px;")

            # 更新状态信息
            self.update_status_field("追踪状态:", "已停止")
            self.update_status_field("系统状态:", "就绪")

            # 停止定时器
            self.update_timer.stop()

            # 发送停止追踪信号
            self.tracking_stopped.emit()
            print("停止追踪所有目标")

# 测试代码
if __name__ == "__main__":
    app = QApplication(sys.argv)
    dialog = MultiTargetTrackingDialog()
    dialog.show()
    sys.exit(app.exec())
