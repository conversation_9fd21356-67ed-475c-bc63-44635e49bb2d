# -*- coding: utf-8 -*-
# @Description : 依赖包安装脚本
# @Date : 2025年6月20日

import subprocess
import sys
import os

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"\n{description}...")
    print(f"执行命令: {command}")
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✓ {description}成功")
            if result.stdout:
                print(f"输出: {result.stdout.strip()}")
        else:
            print(f"✗ {description}失败")
            if result.stderr:
                print(f"错误: {result.stderr.strip()}")
            return False
    except Exception as e:
        print(f"✗ {description}异常: {e}")
        return False
    
    return True

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("✗ Python版本过低，建议使用Python 3.8+")
        return False
    else:
        print("✓ Python版本符合要求")
        return True

def upgrade_pip():
    """升级pip"""
    return run_command(f"{sys.executable} -m pip install --upgrade pip", "升级pip")

def install_core_packages():
    """安装核心包"""
    core_packages = [
        "flask==2.3.3",
        "flask-cors==4.0.0",
        "pymysql==1.1.0",
        "python-dotenv==1.0.0",
        "numpy==1.24.3",
        "pillow==10.0.1"
    ]
    
    print("\n安装核心包...")
    for package in core_packages:
        if not run_command(f"{sys.executable} -m pip install {package}", f"安装 {package}"):
            print(f"核心包 {package} 安装失败，尝试继续...")
    
    return True

def install_yolo_packages():
    """安装YOLO相关包"""
    yolo_packages = [
        "ultralytics==8.0.196",
        "opencv-python==********",
        "supervision==0.16.0"
    ]
    
    print("\n安装YOLO相关包...")
    for package in yolo_packages:
        if not run_command(f"{sys.executable} -m pip install {package}", f"安装 {package}"):
            print(f"YOLO包 {package} 安装失败，尝试继续...")
    
    return True

def install_optional_packages():
    """安装可选包"""
    optional_packages = [
        "flask-socketio==5.3.6",
        "redis==5.0.1",
        "pandas==2.0.3",
        "apscheduler==3.10.4",
        "psutil==5.9.6",
        "gunicorn==21.2.0"
    ]
    
    print("\n安装可选包...")
    for package in optional_packages:
        if not run_command(f"{sys.executable} -m pip install {package}", f"安装 {package}"):
            print(f"可选包 {package} 安装失败，跳过...")
    
    return True

def install_from_requirements():
    """从requirements.txt安装"""
    if os.path.exists("requirements.txt"):
        print("\n从requirements.txt安装依赖...")
        return run_command(f"{sys.executable} -m pip install -r requirements.txt", "安装requirements.txt")
    else:
        print("未找到requirements.txt文件")
        return True

def install_backend_requirements():
    """从backend/requirements.txt安装"""
    backend_req = "backend/requirements.txt"
    if os.path.exists(backend_req):
        print(f"\n从{backend_req}安装依赖...")
        return run_command(f"{sys.executable} -m pip install -r {backend_req}", f"安装{backend_req}")
    else:
        print(f"未找到{backend_req}文件")
        return True

def test_imports():
    """测试关键包导入"""
    test_packages = [
        ("flask", "Flask"),
        ("pymysql", "PyMySQL"),
        ("cv2", "OpenCV"),
        ("numpy", "NumPy"),
        ("PIL", "Pillow"),
        ("ultralytics", "Ultralytics"),
        ("dotenv", "python-dotenv")
    ]
    
    print("\n测试包导入...")
    failed_imports = []
    
    for package, name in test_packages:
        try:
            __import__(package)
            print(f"✓ {name} 导入成功")
        except ImportError:
            print(f"✗ {name} 导入失败")
            failed_imports.append(name)
    
    if failed_imports:
        print(f"\n以下包导入失败: {', '.join(failed_imports)}")
        return False
    else:
        print("\n✓ 所有关键包导入成功")
        return True

def create_directories():
    """创建必要的目录"""
    directories = [
        "logs",
        "uploads",
        "static/images",
        "static/after_img",
        "static/before_img",
        "models",
        "backups",
        "exports"
    ]
    
    print("\n创建必要目录...")
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"✓ 创建目录: {directory}")
        except Exception as e:
            print(f"✗ 创建目录失败 {directory}: {e}")

def main():
    """主函数"""
    print("=" * 80)
    print("基于Yolov8与ByteTrack的高速公路智慧监控平台 - 依赖安装脚本")
    print("=" * 80)
    
    # 检查Python版本
    if not check_python_version():
        return
    
    # 升级pip
    upgrade_pip()
    
    # 安装核心包
    install_core_packages()
    
    # 安装YOLO包
    install_yolo_packages()
    
    # 安装可选包
    install_optional_packages()
    
    # 从requirements文件安装
    install_from_requirements()
    install_backend_requirements()
    
    # 创建目录
    create_directories()
    
    # 测试导入
    if test_imports():
        print("\n" + "=" * 80)
        print("✓ 依赖安装完成！")
        print("=" * 80)
        print("\n下一步操作:")
        print("1. 配置数据库连接: 编辑 config/end-back.env")
        print("2. 导入数据库结构: mysql -u root -p yolo < yolo.sql")
        print("3. 测试数据库连接: python test_db.py")
        print("4. 启动系统: python start_server.py")
    else:
        print("\n" + "=" * 80)
        print("✗ 部分依赖安装失败")
        print("=" * 80)
        print("\n建议:")
        print("1. 检查网络连接")
        print("2. 尝试使用国内镜像: pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/")
        print("3. 手动安装失败的包")

if __name__ == "__main__":
    main()
