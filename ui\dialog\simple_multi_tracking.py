# -*- coding: utf-8 -*-
# <AUTHOR> Enhanced by Cascade
import sys
from PySide6.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QPushButton, QDialog
from PySide6.QtCore import Signal
from PySide6.QtGui import QPixmap

# u521bu5efau4e00u4e2au7b80u5355u7684u5bf9u8bddu6846u57fau7c7buff0cu7c7bu4f3cu4e8e TechTrackingWindow
class SimpleMultiTrackingWindow(QWidget):
    """u7b80u5355u7684u591au76eeu6807u8ffdu8e2au7a97u53e3u57fau7c7b"""
    
    def __init__(self):
        super(SimpleMultiTrackingWindow, self).__init__()
        self.setWindowTitle("u591au76eeu6807u8ffdu8e2a")
        self.resize(600, 400)
        
        # u521bu5efau57fau672cu5e03u5c40
        self.layout = QVBoxLayout(self)
        
        # u6dfbu52a0u6807u9898
        self.titleLabel = QLabel("u591au76eeu6807u8ffdu8e2au5bf9u8bddu6846")
        self.titleLabel.setStyleSheet("font-size: 18px; font-weight: bold; color: blue;")
        self.layout.addWidget(self.titleLabel)
        
        # u6dfbu52a0u8bf4u660e
        self.infoLabel = QLabel("u8be5u5bf9u8bddu6846u7528u4e8eu591au76eeu6807u8ffdu8e2au529fu80fdu3002\n\nu8bf7u70b9u51fbu6309u94aeu5f00u59cbu8ffdu8e2au3002")
        self.infoLabel.setStyleSheet("font-size: 14px;")
        self.layout.addWidget(self.infoLabel)
        
        # u6dfbu52a0u5f00u59cbu8ffdu8e2au6309u94ae
        self.trackButton = QPushButton("u5f00u59cbu8ffdu8e2a")
        self.trackButton.setStyleSheet("background-color: #007bff; color: white; padding: 8px;")
        self.layout.addWidget(self.trackButton)
        
        # u6dfbu52a0u72b6u6001u6807u7b7e
        self.statusLabel = QLabel("u5c31u7eea")
        self.statusLabel.setStyleSheet("color: green; font-size: 14px;")
        self.layout.addWidget(self.statusLabel)
        

class SimpleMultiTrackingDialog(SimpleMultiTrackingWindow):
    """u7b80u5355u7684u591au76eeu6807u8ffdu8e2au5bf9u8bddu6846uff0cu7c7bu4f3cu4e8e TechTrackingDialog"""
    
    def __init__(self, parent=None):
        super(SimpleMultiTrackingDialog, self).__init__()
        print("u521bu5efau7b80u5316u591au76eeu6807u8ffdu8e2au5bf9u8bddu6846")
        
        # u8fdeu63a5u6309u94aeu4fe1u53f7
        self.trackButton.clicked.connect(self.on_track_button_clicked)
        
    def on_track_button_clicked(self):
        self.statusLabel.setText("u6b63u5728u8ffdu8e2au591au4e2au76eeu6807...")
        self.statusLabel.setStyleSheet("color: rgb(0, 200, 100); font-size: 14px;")


if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = SimpleMultiTrackingDialog()
    window.show()
    sys.exit(app.exec())
