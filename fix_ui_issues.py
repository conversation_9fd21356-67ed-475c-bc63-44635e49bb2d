#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复UI问题脚本：
1. 修复行人检测系统对话框字体被遮挡问题
2. 修改车辆碰撞检测系统对话框黑色阴影为浅色
"""

import os
import re
import sys

def fix_pedestrian_dialog():
    """修复行人检测对话框字体问题"""
    print("正在修复行人检测对话框字体问题...")
    
    file_path = "ui/dialog/simple_pedestrian_dialog.py"
    if not os.path.exists(file_path):
        print(f"错误：找不到文件 {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 为所有标签添加文字阴影
    content = re.sub(
        r'(QLabel\s*{[^}]*?color:\s*white[^}]*?font-size:[^}]*?)(})',
        r'\1text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\n            }',
        content
    )
    
    # 为数字标签添加更强的阴影
    content = re.sub(
        r'(QLabel\s*{[^}]*?font-size:\s*36px[^}]*?)(})',
        r'\1text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.7);\n            }',
        content
    )
    
    # 减小主阴影效果
    content = re.sub(
        r'shadow\.setBlurRadius\(30\)',
        r'shadow.setBlurRadius(20)',
        content
    )
    
    content = re.sub(
        r'shadow\.setColor\(QColor\(0,\s*0,\s*0,\s*40\)\)',
        r'shadow.setColor(QColor(0, 0, 0, 30))',
        content
    )
    
    content = re.sub(
        r'shadow\.setOffset\(0,\s*10\)',
        r'shadow.setOffset(0, 8)',
        content
    )
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 行人检测对话框字体问题修复完成")
    return True

def fix_collision_dialog():
    """修复车辆碰撞检测对话框阴影问题"""
    print("正在修复车辆碰撞检测对话框阴影问题...")
    
    file_path = "ui/dialog/collision_detection_dialog.py"
    if not os.path.exists(file_path):
        print(f"错误：找不到文件 {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找并替换阴影效果方法
    shadow_pattern = r'def add_shadow_effect\(self\):[^}]*?shadow\.setColor\(QColor\([^)]*?\)\)[^}]*?self\.setGraphicsEffect\(shadow\)'
    replacement = '''def add_shadow_effect(self):
        """添加阴影效果 - 改为浅色阴影"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(25)
        shadow.setColor(QColor(100, 180, 255, 35))  # 浅蓝色阴影
        shadow.setOffset(0, 8)
        self.setGraphicsEffect(shadow)'''
    
    new_content = re.sub(shadow_pattern, replacement, content, flags=re.DOTALL)
    
    # 如果没有找到匹配项，尝试在文件末尾添加
    if new_content == content:
        print("警告：无法找到阴影效果方法，尝试在文件末尾添加...")
        lines = content.splitlines()
        for i in range(len(lines)-1, 0, -1):
            if lines[i].strip() == "}" or lines[i].strip() == "":
                lines.insert(i, replacement)
                new_content = "\n".join(lines)
                break
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print("✅ 车辆碰撞检测对话框阴影问题修复完成")
    return True

def main():
    """主函数"""
    print("开始修复UI问题...")
    
    pedestrian_fixed = fix_pedestrian_dialog()
    collision_fixed = fix_collision_dialog()
    
    if pedestrian_fixed and collision_fixed:
        print("\n✅ 所有UI问题修复完成！")
    else:
        print("\n⚠️ 部分UI问题修复失败，请检查日志")
    
if __name__ == "__main__":
    main()