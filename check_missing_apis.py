#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查缺失的API接口
"""

import requests
import json

def check_all_possible_apis():
    """检查所有可能的API接口"""
    base_url = "http://127.0.0.1:5500"
    
    print("🔐 获取token...")
    
    # 登录
    login_data = {"username": "admin", "password": "123456"}
    try:
        response = requests.post(f"{base_url}/api/v1/auth/login", json=login_data, timeout=5)
        if response.status_code != 200:
            print(f"❌ 登录失败: {response.status_code}")
            return
        
        login_result = response.json()
        if not login_result.get('success'):
            print(f"❌ 登录失败: {login_result.get('message')}")
            return
        
        token = login_result['data']['token']
        headers = {"Authorization": f"Bearer {token}"}
        print("✅ 登录成功")
        
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return
    
    # 前端可能请求的所有API
    possible_apis = [
        # 认证相关
        ("/api/v1/auth/profile", "用户资料"),
        ("/api/v1/auth/logout", "登出"),
        
        # 监控管理
        ("/api/v1/monitor/list", "监控点列表"),
        ("/api/v1/monitor/status", "监控点状态"),
        ("/api/v1/monitor/config", "监控点配置"),
        
        # 事故检测
        ("/api/v1/accident/alerts/realtime", "实时警报"),
        ("/api/v1/accident/records", "事故记录"),
        ("/api/v1/accident/detection/config", "事故检测配置"),
        ("/api/v1/accident/detection/start", "启动事故检测"),
        ("/api/v1/accident/detection/stop", "停止事故检测"),
        ("/api/v1/accident/detection/status", "事故检测状态"),
        
        # 多目标追踪
        ("/api/v1/tracking/start", "启动追踪"),
        ("/api/v1/tracking/stop", "停止追踪"),
        ("/api/v1/tracking/targets", "追踪目标"),
        ("/api/v1/tracking/targets/active", "活跃追踪目标"),
        ("/api/v1/tracking/metrics", "追踪指标"),
        ("/api/v1/tracking/config", "追踪配置"),
        
        # 检测中心
        ("/api/v1/detection/tasks", "检测任务"),
        ("/api/v1/detection/rtsp", "RTSP检测"),
        ("/api/v1/detection/upload", "上传检测"),
        ("/api/v1/detection/config", "检测配置"),
        ("/api/v1/detection/models", "检测模型"),
        
        # 数据分析
        ("/api/v1/analysis/statistics/overview", "概览统计"),
        ("/api/v1/analysis/alarms", "警报统计"),
        ("/api/v1/analysis/traffic-flow", "交通流量"),
        ("/api/v1/analysis/heatmap", "热力图"),
        ("/api/v1/analysis/performance", "性能分析"),
        ("/api/v1/analysis/trends", "趋势分析"),
        
        # 系统管理
        ("/api/v1/system/users", "系统用户"),
        ("/api/v1/system/config", "系统配置"),
        ("/api/v1/system/logs", "系统日志"),
        ("/api/v1/system/health", "系统健康"),
        
        # 其他可能的API
        ("/api/v1/notifications", "通知"),
        ("/api/v1/reports", "报告"),
        ("/api/v1/exports", "导出"),
        ("/api/v1/uploads", "上传"),
    ]
    
    print(f"\n🧪 检查 {len(possible_apis)} 个可能的API...")
    
    missing_apis = []
    working_apis = []
    error_apis = []
    
    for endpoint, name in possible_apis:
        try:
            response = requests.get(f"{base_url}{endpoint}", headers=headers, timeout=3)
            
            if response.status_code == 404:
                missing_apis.append((endpoint, name))
                print(f"   ❌ 404 - {name}: {endpoint}")
            elif response.status_code == 200:
                working_apis.append((endpoint, name))
                print(f"   ✅ 200 - {name}: {endpoint}")
            else:
                error_apis.append((endpoint, name, response.status_code))
                print(f"   ⚠️ {response.status_code} - {name}: {endpoint}")
                
        except requests.exceptions.Timeout:
            error_apis.append((endpoint, name, "TIMEOUT"))
            print(f"   ⏱️ TIMEOUT - {name}: {endpoint}")
        except Exception as e:
            error_apis.append((endpoint, name, str(e)))
            print(f"   ❌ ERROR - {name}: {endpoint} - {e}")
    
    # 总结
    print(f"\n" + "=" * 80)
    print(f"📊 API检查结果:")
    print(f"   ✅ 正常工作: {len(working_apis)}")
    print(f"   ❌ 404缺失: {len(missing_apis)}")
    print(f"   ⚠️ 其他错误: {len(error_apis)}")
    
    if missing_apis:
        print(f"\n❌ 缺失的API (可能导致前端卡死):")
        for endpoint, name in missing_apis:
            print(f"   {name}: {endpoint}")
    
    if error_apis:
        print(f"\n⚠️ 有错误的API:")
        for endpoint, name, error in error_apis:
            print(f"   {name}: {endpoint} - {error}")
    
    print("=" * 80)

def main():
    """主函数"""
    print("=" * 80)
    print("🚀 检查缺失的API接口")
    print("=" * 80)
    
    check_all_possible_apis()
    
    print("\n💡 解决方案:")
    print("1. 对于404的API，需要在后端添加对应的接口")
    print("2. 对于前端必需的API，优先实现")
    print("3. 对于可选的API，可以返回空数据或默认配置")
    print("4. 确保所有API都有正确的错误处理")

if __name__ == "__main__":
    main()
