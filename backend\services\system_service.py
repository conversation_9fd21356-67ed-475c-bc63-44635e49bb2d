# -*- coding: utf-8 -*-
# @Description : 系统管理服务
# @Date : 2025年6月20日

import os
import json
import time
import shutil
import subprocess
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import psutil
from utils.database import get_db_connection, backup_database, restore_database

class SystemService:
    """系统管理服务类"""
    
    def __init__(self):
        self.config_file = 'config/system_config.json'
        self.backup_dir = 'backups'
        self.log_dir = 'logs'
        
        # 确保目录存在
        os.makedirs(self.backup_dir, exist_ok=True)
        os.makedirs(self.log_dir, exist_ok=True)
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取系统性能指标"""
        try:
            # CPU信息
            cpu_info = {
                'usage_percent': psutil.cpu_percent(interval=1),
                'count': psutil.cpu_count(),
                'frequency': psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None,
                'load_average': psutil.getloadavg() if hasattr(psutil, 'getloadavg') else [0, 0, 0]
            }
            
            # 内存信息
            memory = psutil.virtual_memory()
            memory_info = {
                'total': memory.total,
                'available': memory.available,
                'used': memory.used,
                'percent': memory.percent,
                'free': memory.free
            }
            
            # 磁盘信息
            disk = psutil.disk_usage('/')
            disk_info = {
                'total': disk.total,
                'used': disk.used,
                'free': disk.free,
                'percent': (disk.used / disk.total) * 100
            }
            
            # 网络信息
            network = psutil.net_io_counters()
            network_info = {
                'bytes_sent': network.bytes_sent,
                'bytes_recv': network.bytes_recv,
                'packets_sent': network.packets_sent,
                'packets_recv': network.packets_recv,
                'errin': network.errin,
                'errout': network.errout,
                'dropin': network.dropin,
                'dropout': network.dropout
            }
            
            # 进程信息
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
                try:
                    proc_info = proc.info
                    if proc_info['cpu_percent'] > 1.0 or proc_info['memory_percent'] > 1.0:
                        processes.append(proc_info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # 按CPU使用率排序，取前10个
            processes.sort(key=lambda x: x['cpu_percent'], reverse=True)
            top_processes = processes[:10]
            
            return {
                'timestamp': datetime.now().isoformat(),
                'cpu': cpu_info,
                'memory': memory_info,
                'disk': disk_info,
                'network': network_info,
                'top_processes': top_processes,
                'uptime': time.time() - psutil.boot_time()
            }
        except Exception as e:
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def get_logs(self, log_type: str = 'app', lines: int = 100) -> List[str]:
        """获取系统日志"""
        try:
            log_files = {
                'app': os.path.join(self.log_dir, 'app.log'),
                'error': os.path.join(self.log_dir, 'error.log'),
                'access': os.path.join(self.log_dir, 'access.log')
            }
            
            log_file = log_files.get(log_type, log_files['app'])
            
            if not os.path.exists(log_file):
                return []
            
            # 读取最后N行
            with open(log_file, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                return all_lines[-lines:] if len(all_lines) > lines else all_lines
        except Exception as e:
            return [f"读取日志失败: {str(e)}"]
    
    def get_system_config(self) -> Dict[str, Any]:
        """获取系统配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                # 返回默认配置
                default_config = {
                    'system': {
                        'name': '基于Yolov8与ByteTrack的高速公路智慧监控平台',
                        'version': '1.0.0',
                        'debug': False,
                        'log_level': 'INFO'
                    },
                    'detection': {
                        'default_conf_threshold': 0.4,
                        'default_iou_threshold': 0.5,
                        'max_concurrent_tasks': 10,
                        'result_retention_days': 30
                    },
                    'alert': {
                        'email_enabled': True,
                        'sms_enabled': False,
                        'webhook_enabled': False,
                        'alert_cooldown_minutes': 5
                    },
                    'storage': {
                        'max_video_size_gb': 100,
                        'max_image_size_gb': 50,
                        'cleanup_enabled': True,
                        'backup_enabled': True,
                        'backup_retention_days': 90
                    },
                    'performance': {
                        'max_cpu_usage': 80,
                        'max_memory_usage': 85,
                        'max_disk_usage': 90
                    }
                }
                
                # 保存默认配置
                self.update_system_config(default_config)
                return default_config
        except Exception as e:
            return {'error': str(e)}
    
    def update_system_config(self, config_data: Dict[str, Any]) -> bool:
        """更新系统配置"""
        try:
            # 确保配置目录存在
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            
            # 备份当前配置
            if os.path.exists(self.config_file):
                backup_file = f"{self.config_file}.backup.{int(time.time())}"
                shutil.copy2(self.config_file, backup_file)
            
            # 保存新配置
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            return True
        except Exception as e:
            print(f"更新系统配置失败: {str(e)}")
            return False
    
    def create_backup(self, backup_type: str = 'database') -> Dict[str, Any]:
        """创建系统备份"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_info = {
                'id': f"backup_{timestamp}",
                'type': backup_type,
                'timestamp': datetime.now().isoformat(),
                'status': 'in_progress'
            }
            
            if backup_type == 'database':
                # 数据库备份
                backup_file = os.path.join(self.backup_dir, f"database_backup_{timestamp}.sql")
                success = backup_database(backup_file)
                
                if success:
                    backup_info.update({
                        'status': 'completed',
                        'file_path': backup_file,
                        'file_size': os.path.getsize(backup_file)
                    })
                else:
                    backup_info['status'] = 'failed'
            
            elif backup_type == 'full':
                # 完整系统备份
                backup_file = os.path.join(self.backup_dir, f"full_backup_{timestamp}.tar.gz")
                
                # 创建tar压缩包
                import tarfile
                with tarfile.open(backup_file, 'w:gz') as tar:
                    # 添加配置文件
                    if os.path.exists('config'):
                        tar.add('config', arcname='config')
                    
                    # 添加模型文件
                    if os.path.exists('models'):
                        tar.add('models', arcname='models')
                    
                    # 添加日志文件
                    if os.path.exists(self.log_dir):
                        tar.add(self.log_dir, arcname='logs')
                
                # 数据库备份
                db_backup_file = os.path.join(self.backup_dir, f"database_{timestamp}.sql")
                db_success = backup_database(db_backup_file)
                
                if db_success:
                    # 将数据库备份添加到tar包
                    with tarfile.open(backup_file, 'a:gz') as tar:
                        tar.add(db_backup_file, arcname='database.sql')
                    
                    # 删除临时数据库备份文件
                    os.remove(db_backup_file)
                
                backup_info.update({
                    'status': 'completed',
                    'file_path': backup_file,
                    'file_size': os.path.getsize(backup_file)
                })
            
            return backup_info
        except Exception as e:
            return {
                'status': 'failed',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def get_backup_list(self) -> List[Dict[str, Any]]:
        """获取备份列表"""
        try:
            backups = []
            
            if not os.path.exists(self.backup_dir):
                return backups
            
            for filename in os.listdir(self.backup_dir):
                file_path = os.path.join(self.backup_dir, filename)
                if os.path.isfile(file_path):
                    stat = os.stat(file_path)
                    
                    backup_info = {
                        'id': filename,
                        'filename': filename,
                        'file_path': file_path,
                        'size': stat.st_size,
                        'created_time': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                        'type': 'database' if filename.endswith('.sql') else 'full'
                    }
                    backups.append(backup_info)
            
            # 按创建时间倒序排列
            backups.sort(key=lambda x: x['created_time'], reverse=True)
            return backups
        except Exception as e:
            return []
    
    def restore_backup(self, backup_id: str) -> bool:
        """恢复备份"""
        try:
            backup_file = os.path.join(self.backup_dir, backup_id)
            
            if not os.path.exists(backup_file):
                return False
            
            if backup_file.endswith('.sql'):
                # 恢复数据库备份
                return restore_database(backup_file)
            elif backup_file.endswith('.tar.gz'):
                # 恢复完整备份
                import tarfile
                
                # 创建临时目录
                temp_dir = os.path.join(self.backup_dir, 'temp_restore')
                os.makedirs(temp_dir, exist_ok=True)
                
                try:
                    # 解压备份文件
                    with tarfile.open(backup_file, 'r:gz') as tar:
                        tar.extractall(temp_dir)
                    
                    # 恢复配置文件
                    config_src = os.path.join(temp_dir, 'config')
                    if os.path.exists(config_src):
                        if os.path.exists('config'):
                            shutil.rmtree('config')
                        shutil.copytree(config_src, 'config')
                    
                    # 恢复数据库
                    db_file = os.path.join(temp_dir, 'database.sql')
                    if os.path.exists(db_file):
                        restore_database(db_file)
                    
                    return True
                finally:
                    # 清理临时目录
                    if os.path.exists(temp_dir):
                        shutil.rmtree(temp_dir)
            
            return False
        except Exception as e:
            print(f"恢复备份失败: {str(e)}")
            return False
    
    def cleanup_system(self, cleanup_options: Dict[str, Any]) -> Dict[str, Any]:
        """系统清理"""
        try:
            cleanup_result = {
                'timestamp': datetime.now().isoformat(),
                'cleaned_items': [],
                'freed_space': 0,
                'errors': []
            }
            
            # 清理临时文件
            if cleanup_options.get('temp_files', False):
                temp_dirs = ['temp', 'tmp', '__pycache__']
                for temp_dir in temp_dirs:
                    if os.path.exists(temp_dir):
                        try:
                            size_before = self._get_dir_size(temp_dir)
                            shutil.rmtree(temp_dir)
                            cleanup_result['cleaned_items'].append(f'临时目录: {temp_dir}')
                            cleanup_result['freed_space'] += size_before
                        except Exception as e:
                            cleanup_result['errors'].append(f'清理{temp_dir}失败: {str(e)}')
            
            # 清理旧日志
            if cleanup_options.get('old_logs', False):
                log_retention_days = cleanup_options.get('log_retention_days', 30)
                cutoff_date = datetime.now() - timedelta(days=log_retention_days)
                
                try:
                    freed_space = self._cleanup_old_files(self.log_dir, cutoff_date, ['.log'])
                    cleanup_result['cleaned_items'].append(f'旧日志文件 (>{log_retention_days}天)')
                    cleanup_result['freed_space'] += freed_space
                except Exception as e:
                    cleanup_result['errors'].append(f'清理旧日志失败: {str(e)}')
            
            # 清理旧备份
            if cleanup_options.get('old_backups', False):
                backup_retention_days = cleanup_options.get('backup_retention_days', 90)
                cutoff_date = datetime.now() - timedelta(days=backup_retention_days)
                
                try:
                    freed_space = self._cleanup_old_files(self.backup_dir, cutoff_date, ['.sql', '.tar.gz'])
                    cleanup_result['cleaned_items'].append(f'旧备份文件 (>{backup_retention_days}天)')
                    cleanup_result['freed_space'] += freed_space
                except Exception as e:
                    cleanup_result['errors'].append(f'清理旧备份失败: {str(e)}')
            
            # 清理数据库
            if cleanup_options.get('database_cleanup', False):
                try:
                    self._cleanup_database(cleanup_options)
                    cleanup_result['cleaned_items'].append('数据库清理')
                except Exception as e:
                    cleanup_result['errors'].append(f'数据库清理失败: {str(e)}')
            
            return cleanup_result
        except Exception as e:
            return {
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }
    
    def _get_dir_size(self, directory: str) -> int:
        """获取目录大小"""
        total_size = 0
        for dirpath, dirnames, filenames in os.walk(directory):
            for filename in filenames:
                file_path = os.path.join(dirpath, filename)
                if os.path.exists(file_path):
                    total_size += os.path.getsize(file_path)
        return total_size
    
    def _cleanup_old_files(self, directory: str, cutoff_date: datetime, extensions: List[str]) -> int:
        """清理旧文件"""
        freed_space = 0
        
        if not os.path.exists(directory):
            return 0
        
        for filename in os.listdir(directory):
            file_path = os.path.join(directory, filename)
            
            if os.path.isfile(file_path):
                # 检查文件扩展名
                if any(filename.endswith(ext) for ext in extensions):
                    # 检查文件修改时间
                    file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                    
                    if file_mtime < cutoff_date:
                        file_size = os.path.getsize(file_path)
                        os.remove(file_path)
                        freed_space += file_size
        
        return freed_space
    
    def _cleanup_database(self, cleanup_options: Dict[str, Any]):
        """清理数据库"""
        try:
            with get_db_connection() as db:
                # 清理旧的检测结果
                if cleanup_options.get('old_detection_results', False):
                    retention_days = cleanup_options.get('detection_retention_days', 30)
                    cutoff_date = datetime.now() - timedelta(days=retention_days)
                    
                    db.modify("""
                        DELETE FROM detection_result 
                        WHERE timestamp < %s
                    """, (cutoff_date,))
                
                # 清理旧的系统日志
                if cleanup_options.get('old_system_logs', False):
                    retention_days = cleanup_options.get('log_retention_days', 90)
                    cutoff_date = datetime.now() - timedelta(days=retention_days)
                    
                    db.modify("""
                        DELETE FROM system_log 
                        WHERE create_time < %s
                    """, (cutoff_date,))
                
                # 清理旧的实时数据
                if cleanup_options.get('old_realtime_data', False):
                    cutoff_date = datetime.now() - timedelta(hours=24)
                    
                    db.modify("""
                        DELETE FROM realtime_data 
                        WHERE timestamp < %s OR expire_time < NOW()
                    """, (cutoff_date,))
        except Exception as e:
            raise Exception(f"数据库清理失败: {str(e)}")
    
    def health_check(self) -> Dict[str, Any]:
        """系统健康检查"""
        try:
            health_status = {
                'timestamp': datetime.now().isoformat(),
                'overall_status': 'healthy',
                'checks': {}
            }
            
            # 数据库连接检查
            try:
                with get_db_connection() as db:
                    db.execute("SELECT 1")
                health_status['checks']['database'] = {'status': 'healthy', 'message': '数据库连接正常'}
            except Exception as e:
                health_status['checks']['database'] = {'status': 'unhealthy', 'message': str(e)}
                health_status['overall_status'] = 'unhealthy'
            
            # 磁盘空间检查
            disk_usage = psutil.disk_usage('/')
            disk_percent = (disk_usage.used / disk_usage.total) * 100
            
            if disk_percent > 90:
                health_status['checks']['disk'] = {'status': 'critical', 'usage': disk_percent}
                health_status['overall_status'] = 'critical'
            elif disk_percent > 80:
                health_status['checks']['disk'] = {'status': 'warning', 'usage': disk_percent}
                if health_status['overall_status'] == 'healthy':
                    health_status['overall_status'] = 'warning'
            else:
                health_status['checks']['disk'] = {'status': 'healthy', 'usage': disk_percent}
            
            # 内存使用检查
            memory = psutil.virtual_memory()
            if memory.percent > 90:
                health_status['checks']['memory'] = {'status': 'critical', 'usage': memory.percent}
                health_status['overall_status'] = 'critical'
            elif memory.percent > 80:
                health_status['checks']['memory'] = {'status': 'warning', 'usage': memory.percent}
                if health_status['overall_status'] == 'healthy':
                    health_status['overall_status'] = 'warning'
            else:
                health_status['checks']['memory'] = {'status': 'healthy', 'usage': memory.percent}
            
            # CPU使用检查
            cpu_percent = psutil.cpu_percent(interval=1)
            if cpu_percent > 90:
                health_status['checks']['cpu'] = {'status': 'critical', 'usage': cpu_percent}
                health_status['overall_status'] = 'critical'
            elif cpu_percent > 80:
                health_status['checks']['cpu'] = {'status': 'warning', 'usage': cpu_percent}
                if health_status['overall_status'] == 'healthy':
                    health_status['overall_status'] = 'warning'
            else:
                health_status['checks']['cpu'] = {'status': 'healthy', 'usage': cpu_percent}
            
            return health_status
        except Exception as e:
            return {
                'timestamp': datetime.now().isoformat(),
                'overall_status': 'error',
                'error': str(e)
            }
    
    def restart_services(self):
        """重启系统服务"""
        try:
            # 这里可以添加重启逻辑
            # 例如：重启Flask应用、清理缓存等
            print("系统服务重启指令已发送")
            
            # 实际项目中可能需要：
            # 1. 停止当前服务
            # 2. 清理资源
            # 3. 重新启动服务
            
            return True
        except Exception as e:
            print(f"重启服务失败: {str(e)}")
            return False
    
    def check_for_updates(self) -> Dict[str, Any]:
        """检查系统更新"""
        try:
            # 这里可以实现更新检查逻辑
            # 例如：检查远程版本、比较本地版本等
            
            current_version = "1.0.0"
            
            return {
                'current_version': current_version,
                'latest_version': current_version,
                'has_update': False,
                'update_info': None,
                'check_time': datetime.now().isoformat()
            }
        except Exception as e:
            return {
                'error': str(e),
                'check_time': datetime.now().isoformat()
            }
    
    def get_system_settings(self) -> Dict[str, Any]:
        """获取系统设置"""
        try:
            with get_db_connection() as db:
                settings = db.get_list("""
                    SELECT config_key, config_value, config_type, description, category
                    FROM system_config
                    ORDER BY category, config_key
                """)
                
                # 按分类组织设置
                organized_settings = {}
                for setting in settings:
                    category = setting['category'] or 'general'
                    if category not in organized_settings:
                        organized_settings[category] = []
                    
                    # 根据类型转换值
                    value = setting['config_value']
                    if setting['config_type'] == 'int':
                        value = int(value)
                    elif setting['config_type'] == 'float':
                        value = float(value)
                    elif setting['config_type'] == 'bool':
                        value = value.lower() in ('true', '1', 'yes')
                    elif setting['config_type'] == 'json':
                        value = json.loads(value)
                    
                    organized_settings[category].append({
                        'key': setting['config_key'],
                        'value': value,
                        'type': setting['config_type'],
                        'description': setting['description']
                    })
                
                return organized_settings
        except Exception as e:
            return {'error': str(e)}
    
    def update_system_settings(self, settings_data: Dict[str, Any]) -> bool:
        """更新系统设置"""
        try:
            with get_db_connection() as db:
                for category, settings in settings_data.items():
                    for setting in settings:
                        key = setting['key']
                        value = setting['value']
                        setting_type = setting.get('type', 'string')
                        
                        # 根据类型转换值为字符串
                        if setting_type == 'json':
                            value_str = json.dumps(value)
                        else:
                            value_str = str(value)
                        
                        # 更新或插入设置
                        existing = db.get_one("""
                            SELECT id FROM system_config WHERE config_key = %s
                        """, (key,))
                        
                        if existing:
                            db.modify("""
                                UPDATE system_config 
                                SET config_value = %s, update_time = NOW()
                                WHERE config_key = %s
                            """, (value_str, key))
                        else:
                            db.create("""
                                INSERT INTO system_config (config_key, config_value, config_type, category)
                                VALUES (%s, %s, %s, %s)
                            """, (key, value_str, setting_type, category))
            
            return True
        except Exception as e:
            print(f"更新系统设置失败: {str(e)}")
            return False
