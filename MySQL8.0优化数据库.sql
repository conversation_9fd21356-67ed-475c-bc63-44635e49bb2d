-- =====================================================
-- 基于Yolov8与ByteTrack的高速公路智慧监控平台 - MySQL 8.0优化版
-- 严格遵循后端API功能设计
-- 创建时间: 2024-12-24
-- =====================================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 删除现有数据库并重新创建
DROP DATABASE IF EXISTS `yolo`;
CREATE DATABASE `yolo` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `yolo`;

-- =====================================================
-- 1. 用户管理表 (支持admin/operator两个角色)
-- =====================================================
CREATE TABLE `user` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码(MD5加密)',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像URL',
  `grade` varchar(20) NOT NULL DEFAULT 'operator' COMMENT '用户级别(admin/operator)',
  `status` tinyint(1) DEFAULT 1 COMMENT '用户状态(0:禁用,1:启用)',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_grade` (`grade`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- =====================================================
-- 2. 监控点管理表 (支持RTSP流和多目标追踪)
-- =====================================================
CREATE TABLE `monitor` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '监控点ID',
  `name` varchar(100) NOT NULL COMMENT '监控点名称',
  `location` varchar(100) NOT NULL COMMENT '监控地点',
  `highway_section` varchar(100) DEFAULT NULL COMMENT '高速公路路段',
  `camera_position` varchar(200) DEFAULT NULL COMMENT '摄像头位置说明',
  `latitude` decimal(10,7) DEFAULT NULL COMMENT '纬度',
  `longitude` decimal(10,7) DEFAULT NULL COMMENT '经度',
  `threshold` int NOT NULL DEFAULT 15 COMMENT '警报阈值',
  `conf_threshold` float DEFAULT 0.4 COMMENT '检测置信度阈值',
  `iou_threshold` float DEFAULT 0.5 COMMENT 'IOU阈值',
  `person` varchar(50) NOT NULL COMMENT '负责人',
  `video` varchar(500) DEFAULT NULL COMMENT '视频文件路径',
  `url` varchar(500) DEFAULT NULL COMMENT 'RTSP流地址',
  `rtsp_format` varchar(50) DEFAULT 'rtsp' COMMENT 'RTSP格式',
  `connection_status` varchar(20) DEFAULT 'unknown' COMMENT '连接状态(online/offline/error/unknown)',
  `is_alarm` varchar(10) DEFAULT '开启' COMMENT '警报状态(开启/关闭)',
  `mode` varchar(20) DEFAULT 'detection' COMMENT '工作模式(detection/tracking)',
  `show_labels` tinyint(1) DEFAULT 1 COMMENT '是否显示标签',
  `enable_tracking` tinyint(1) DEFAULT 0 COMMENT '是否启用多目标追踪',
  `tracker_type` varchar(20) DEFAULT 'bytetrack' COMMENT '追踪器类型(bytetrack/botsort)',
  `status` tinyint(1) DEFAULT 1 COMMENT '监控点状态(0:禁用,1:启用)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_location` (`location`),
  KEY `idx_highway_section` (`highway_section`),
  KEY `idx_connection_status` (`connection_status`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='监控点表';

-- =====================================================
-- 3. 检测任务表 (支持图像/视频/RTSP检测)
-- =====================================================
CREATE TABLE `detection_task` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `task_id` varchar(100) NOT NULL COMMENT '任务唯一标识',
  `task_name` varchar(100) NOT NULL COMMENT '任务名称',
  `task_type` varchar(20) NOT NULL COMMENT '任务类型(image/video/rtsp)',
  `monitor_id` int DEFAULT NULL COMMENT '关联监控点ID',
  `file_path` varchar(500) DEFAULT NULL COMMENT '文件路径',
  `file_size` bigint DEFAULT NULL COMMENT '文件大小(字节)',
  `file_format` varchar(20) DEFAULT NULL COMMENT '文件格式',
  `model_name` varchar(50) DEFAULT 'yolov8n.pt' COMMENT '使用的模型',
  `conf_threshold` float DEFAULT 0.4 COMMENT '置信度阈值',
  `iou_threshold` float DEFAULT 0.5 COMMENT 'IOU阈值',
  `enable_tracking` tinyint(1) DEFAULT 0 COMMENT '是否启用追踪',
  `tracker_type` varchar(20) DEFAULT 'bytetrack' COMMENT '追踪器类型',
  `status` varchar(20) DEFAULT 'pending' COMMENT '任务状态(pending/running/completed/failed/stopped)',
  `progress` int DEFAULT 0 COMMENT '进度百分比',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `result_path` varchar(500) DEFAULT NULL COMMENT '结果文件路径',
  `detection_count` int DEFAULT 0 COMMENT '检测到的目标数量',
  `processing_time` float DEFAULT NULL COMMENT '处理时间(秒)',
  `fps` float DEFAULT NULL COMMENT '处理帧率',
  `error_message` text COMMENT '错误信息',
  `user_id` int DEFAULT NULL COMMENT '创建用户ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_id` (`task_id`),
  KEY `idx_task_type` (`task_type`),
  KEY `idx_monitor_id` (`monitor_id`),
  KEY `idx_status` (`status`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_detection_task_monitor` FOREIGN KEY (`monitor_id`) REFERENCES `monitor` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_detection_task_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='检测任务表';

-- =====================================================
-- 4. 检测结果表 (存储YOLO检测结果)
-- =====================================================
CREATE TABLE `detection_result` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '结果ID',
  `task_id` varchar(100) NOT NULL COMMENT '任务ID',
  `frame_number` int DEFAULT NULL COMMENT '帧号',
  `timestamp` decimal(15,3) DEFAULT NULL COMMENT '时间戳',
  `object_count` int DEFAULT 0 COMMENT '检测到的对象数量',
  `detection_data` json COMMENT '检测数据(JSON格式)',
  `tracking_data` json COMMENT '追踪数据(JSON格式)',
  `inference_time` float DEFAULT NULL COMMENT '推理时间(毫秒)',
  `confidence_avg` float DEFAULT NULL COMMENT '平均置信度',
  `confidence_max` float DEFAULT NULL COMMENT '最高置信度',
  `class_counts` json COMMENT '类别统计(JSON格式)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_frame_number` (`frame_number`),
  KEY `idx_timestamp` (`timestamp`),
  KEY `idx_object_count` (`object_count`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='检测结果表';

-- =====================================================
-- 5. 多目标追踪表 (ByteTrack追踪结果)
-- =====================================================
CREATE TABLE `tracking_target` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '目标ID',
  `track_id` int NOT NULL COMMENT '追踪ID',
  `monitor_id` int NOT NULL COMMENT '监控点ID',
  `object_type` varchar(50) NOT NULL COMMENT '目标类型(car/truck/bus/person)',
  `confidence` float NOT NULL COMMENT '置信度',
  `bbox_x` float NOT NULL COMMENT '边界框X坐标',
  `bbox_y` float NOT NULL COMMENT '边界框Y坐标',
  `bbox_w` float NOT NULL COMMENT '边界框宽度',
  `bbox_h` float NOT NULL COMMENT '边界框高度',
  `velocity_x` float DEFAULT NULL COMMENT 'X方向速度',
  `velocity_y` float DEFAULT NULL COMMENT 'Y方向速度',
  `speed` float DEFAULT NULL COMMENT '速度(km/h)',
  `direction` float DEFAULT NULL COMMENT '方向角度',
  `first_seen` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '首次出现时间',
  `last_seen` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后出现时间',
  `track_length` int DEFAULT 1 COMMENT '追踪长度(帧数)',
  `status` varchar(20) DEFAULT 'active' COMMENT '状态(active/lost/completed)',
  `algorithm` varchar(50) DEFAULT 'bytetrack' COMMENT '使用的追踪算法',
  `metadata` json COMMENT '额外元数据',
  PRIMARY KEY (`id`),
  KEY `idx_track_id` (`track_id`),
  KEY `idx_monitor_id` (`monitor_id`),
  KEY `idx_object_type` (`object_type`),
  KEY `idx_status` (`status`),
  KEY `idx_last_seen` (`last_seen`),
  CONSTRAINT `fk_tracking_target_monitor` FOREIGN KEY (`monitor_id`) REFERENCES `monitor` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='追踪目标表';

-- =====================================================
-- 6. 事故检测表 (碰撞检测结果)
-- =====================================================
CREATE TABLE `accident_record` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '事故记录ID',
  `record_id` varchar(100) NOT NULL COMMENT '事故记录唯一标识',
  `monitor_id` int NOT NULL COMMENT '监控点ID',
  `accident_type` varchar(50) NOT NULL COMMENT '事故类型(collision/sudden_stop/congestion)',
  `severity` varchar(20) DEFAULT 'medium' COMMENT '严重程度(low/medium/high)',
  `location_x` float DEFAULT NULL COMMENT '事故位置X坐标',
  `location_y` float DEFAULT NULL COMMENT '事故位置Y坐标',
  `description` text COMMENT '事故描述',
  `confidence` float DEFAULT NULL COMMENT '检测置信度',
  `involved_vehicles` json COMMENT '涉及车辆信息(JSON格式)',
  `evidence_image` varchar(500) DEFAULT NULL COMMENT '证据图片路径',
  `evidence_video` varchar(500) DEFAULT NULL COMMENT '证据视频路径',
  `status` varchar(20) DEFAULT 'detected' COMMENT '状态(detected/confirmed/resolved)',
  `detection_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '检测时间',
  `response_time` datetime DEFAULT NULL COMMENT '响应时间',
  `resolution_time` datetime DEFAULT NULL COMMENT '解决时间',
  `handled_by` varchar(50) DEFAULT NULL COMMENT '处理人',
  `metadata` json COMMENT '额外元数据',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_record_id` (`record_id`),
  KEY `idx_monitor_id` (`monitor_id`),
  KEY `idx_accident_type` (`accident_type`),
  KEY `idx_severity` (`severity`),
  KEY `idx_status` (`status`),
  KEY `idx_detection_time` (`detection_time`),
  CONSTRAINT `fk_accident_monitor` FOREIGN KEY (`monitor_id`) REFERENCES `monitor` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='事故记录表';

-- =====================================================
-- 7. 警报表 (统一的警报管理)
-- =====================================================
CREATE TABLE `alarm` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '警报ID',
  `monitor_id` int NOT NULL COMMENT '监控点ID',
  `location` varchar(100) NOT NULL COMMENT '警报位置',
  `highway_section` varchar(100) DEFAULT NULL COMMENT '高速路段',
  `alarm_type` varchar(50) NOT NULL COMMENT '警报类型(vehicle_count/accident/violation/system)',
  `description` text NOT NULL COMMENT '警报描述',
  `vehicle_count` int DEFAULT NULL COMMENT '车辆数量',
  `detection_details` json COMMENT '检测详情(JSON格式)',
  `confidence_level` float DEFAULT NULL COMMENT '置信度',
  `threshold` int DEFAULT NULL COMMENT '阈值',
  `photo` varchar(500) DEFAULT NULL COMMENT '相关图片路径',
  `severity` varchar(20) DEFAULT 'medium' COMMENT '严重程度(low/medium/high/critical)',
  `status` varchar(20) DEFAULT 'pending' COMMENT '状态(pending/processing/resolved/dismissed)',
  `handled_by` varchar(50) DEFAULT NULL COMMENT '处理人',
  `handled_time` datetime DEFAULT NULL COMMENT '处理时间',
  `handle_note` text COMMENT '处理备注',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_monitor_id` (`monitor_id`),
  KEY `idx_alarm_type` (`alarm_type`),
  KEY `idx_severity` (`severity`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_alarm_monitor` FOREIGN KEY (`monitor_id`) REFERENCES `monitor` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='警报表';

-- =====================================================
-- 8. 系统配置表
-- =====================================================
CREATE TABLE `system_config` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `config_type` varchar(20) DEFAULT 'string' COMMENT '配置类型(string/int/float/bool/json)',
  `description` varchar(500) DEFAULT NULL COMMENT '配置描述',
  `category` varchar(50) DEFAULT 'general' COMMENT '配置分类',
  `is_editable` tinyint(1) DEFAULT 1 COMMENT '是否可编辑',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`),
  KEY `idx_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- =====================================================
-- 9. 交通统计表
-- =====================================================
CREATE TABLE `traffic_statistics` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `monitor_id` int NOT NULL COMMENT '监控点ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `stat_hour` int DEFAULT NULL COMMENT '统计小时(0-23)',
  `vehicle_count` int DEFAULT 0 COMMENT '车辆数量',
  `car_count` int DEFAULT 0 COMMENT '小汽车数量',
  `truck_count` int DEFAULT 0 COMMENT '卡车数量',
  `bus_count` int DEFAULT 0 COMMENT '公交车数量',
  `avg_speed` float DEFAULT NULL COMMENT '平均速度',
  `max_speed` float DEFAULT NULL COMMENT '最高速度',
  `min_speed` float DEFAULT NULL COMMENT '最低速度',
  `traffic_density` float DEFAULT NULL COMMENT '交通密度',
  `congestion_level` varchar(20) DEFAULT 'normal' COMMENT '拥堵程度(smooth/normal/congested/severe)',
  `weather_condition` varchar(50) DEFAULT NULL COMMENT '天气状况',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_monitor_date_hour` (`monitor_id`, `stat_date`, `stat_hour`),
  KEY `idx_stat_date` (`stat_date`),
  KEY `idx_congestion_level` (`congestion_level`),
  CONSTRAINT `fk_traffic_monitor` FOREIGN KEY (`monitor_id`) REFERENCES `monitor` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交通统计表';

-- =====================================================
-- 10. 模型管理表
-- =====================================================
CREATE TABLE `model_management` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '模型ID',
  `model_name` varchar(100) NOT NULL COMMENT '模型名称',
  `model_version` varchar(50) DEFAULT '1.0.0' COMMENT '模型版本',
  `model_type` varchar(50) NOT NULL COMMENT '模型类型(yolo/tracking)',
  `model_path` varchar(500) NOT NULL COMMENT '模型文件路径',
  `model_size` bigint DEFAULT NULL COMMENT '模型文件大小(字节)',
  `input_size` varchar(20) DEFAULT '640x640' COMMENT '输入尺寸',
  `num_classes` int DEFAULT NULL COMMENT '类别数量',
  `class_names` json COMMENT '类别名称(JSON格式)',
  `performance_metrics` json COMMENT '性能指标(JSON格式)',
  `description` varchar(500) DEFAULT NULL COMMENT '模型描述',
  `is_default` tinyint(1) DEFAULT 0 COMMENT '是否默认模型',
  `status` varchar(20) DEFAULT 'active' COMMENT '状态(active/inactive)',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_model_name_version` (`model_name`, `model_version`),
  KEY `idx_model_type` (`model_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='模型管理表';

-- =====================================================
-- 11. 系统日志表
-- =====================================================
CREATE TABLE `system_log` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `log_level` varchar(10) NOT NULL COMMENT '日志级别(DEBUG/INFO/WARN/ERROR)',
  `module` varchar(50) DEFAULT NULL COMMENT '模块名称',
  `action` varchar(100) DEFAULT NULL COMMENT '操作动作',
  `message` text COMMENT '日志消息',
  `user_id` int DEFAULT NULL COMMENT '用户ID',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `request_data` text COMMENT '请求数据',
  `response_data` text COMMENT '响应数据',
  `execution_time` float DEFAULT NULL COMMENT '执行时间(毫秒)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_log_level` (`log_level`),
  KEY `idx_module` (`module`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统日志表';

-- =====================================================
-- 12. 插入测试数据
-- =====================================================

-- 插入用户数据 (明文密码，后端会MD5加密)
INSERT INTO `user` (`username`, `password`, `email`, `grade`, `status`, `create_by`, `remark`) VALUES
('admin', '123456', '<EMAIL>', 'admin', 1, 'system', '系统管理员账号'),
('operator', '123456', '<EMAIL>', 'operator', 1, 'admin', '收费站班长账号');

-- 插入监控点数据 (杭州-千岛湖高速全程)
INSERT INTO `monitor` (`name`, `location`, `highway_section`, `camera_position`, `latitude`, `longitude`, `threshold`, `person`, `url`, `connection_status`, `is_alarm`, `mode`, `enable_tracking`, `tracker_type`, `create_by`, `remark`) VALUES
('杭州收费站监控点', '杭州收费站', 'K0+000', '收费站出入口', 30.2741, 120.1551, 15, 'admin', 'rtsp://admin:123456@192.168.1.101:554/stream1', 'online', '开启', 'tracking', 1, 'bytetrack', 'admin', '杭州收费站主要监控点'),
('杭州主线监控点', '杭州主线', 'K5+200', '主线车道', 30.2891, 120.1721, 20, 'admin', 'rtsp://admin:123456@192.168.1.102:554/stream1', 'online', '开启', 'tracking', 1, 'bytetrack', 'admin', '杭州主线交通监控'),
('富阳互通监控点', '富阳互通', 'K18+600', '互通匝道', 30.0498, 119.9528, 18, 'operator', 'rtsp://admin:123456@192.168.1.103:554/stream1', 'online', '开启', 'detection', 0, 'bytetrack', 'admin', '富阳互通交通监控'),
('桐庐服务区监控点', '桐庐服务区', 'K35+800', '服务区入口', 29.7971, 119.6811, 12, 'operator', 'rtsp://admin:123456@*************:554/stream1', 'online', '开启', 'tracking', 1, 'bytetrack', 'admin', '桐庐服务区监控'),
('建德互通监控点', '建德互通', 'K58+900', '互通主线', 29.4765, 119.2876, 16, 'operator', 'rtsp://admin:123456@*************:554/stream1', 'online', '开启', 'detection', 0, 'bytetrack', 'admin', '建德互通监控'),
('千岛湖收费站监控点', '千岛湖收费站', 'K95+000', '收费站广场', 29.5678, 118.8765, 18, 'admin', 'rtsp://admin:123456@*************:554/stream1', 'online', '开启', 'tracking', 1, 'bytetrack', 'admin', '千岛湖收费站监控');

-- 插入系统配置数据
INSERT INTO `system_config` (`config_key`, `config_value`, `config_type`, `description`, `category`) VALUES
('system.name', '基于Yolov8与ByteTrack的高速公路智慧监控平台', 'string', '系统名称', 'general'),
('system.version', '2.0.0', 'string', '系统版本', 'general'),
('detection.default_conf_threshold', '0.4', 'float', '默认检测置信度阈值', 'detection'),
('detection.default_iou_threshold', '0.5', 'float', '默认IOU阈值', 'detection'),
('tracking.default_algorithm', 'bytetrack', 'string', '默认追踪算法', 'tracking'),
('accident.detection_enabled', 'true', 'bool', '是否启用事故检测', 'accident');

-- 插入模型管理数据
INSERT INTO `model_management` (`model_name`, `model_type`, `model_path`, `model_size`, `input_size`, `class_names`, `description`, `is_default`, `create_by`) VALUES
('yolov8n.pt', 'yolo', './models/yolov8n.pt', 6237728, '640x640', '["person", "bicycle", "car", "motorcycle", "airplane", "bus", "train", "truck"]', 'YOLOv8 Nano模型，速度快，适合实时检测', 1, 'admin'),
('car.pt', 'yolo', './models/car.pt', 14567890, '640x640', '["car", "truck", "bus", "motorcycle"]', '专门针对车辆检测优化的模型', 0, 'admin');

-- 插入警报数据
INSERT INTO `alarm` (`monitor_id`, `location`, `highway_section`, `alarm_type`, `description`, `vehicle_count`, `detection_details`, `confidence_level`, `threshold`, `severity`, `status`, `create_time`, `remark`) VALUES
(1, '杭州收费站', 'K0+000', 'vehicle_count', '车流量超标：检测到17辆车，超过阈值15', 17, '{"total_vehicles":17,"cars":12,"trucks":3,"buses":2,"confidence_avg":0.85}', 0.85, 15, 'medium', 'resolved', '2024-12-24 08:15:30', '早高峰车流量超标'),
(3, '富阳互通', 'K18+600', 'vehicle_count', '车流量超标：检测到20辆车，超过阈值18', 20, '{"total_vehicles":20,"cars":15,"trucks":3,"buses":2,"confidence_avg":0.78}', 0.78, 18, 'medium', 'pending', '2024-12-24 09:12:15', '互通匝道车辆聚集'),
(6, '千岛湖收费站', 'K95+000', 'accident', '检测到疑似交通事故', 0, '{"accident_type":"collision","confidence":0.92}', 0.92, 0, 'high', 'processing', '2024-12-24 14:30:00', '收费站区域疑似碰撞');

-- 插入交通统计数据
INSERT INTO `traffic_statistics` (`monitor_id`, `stat_date`, `stat_hour`, `vehicle_count`, `car_count`, `truck_count`, `bus_count`, `avg_speed`, `max_speed`, `congestion_level`) VALUES
(1, '2024-12-24', 8, 245, 198, 35, 12, 85.5, 120.0, 'normal'),
(1, '2024-12-24', 9, 312, 251, 45, 16, 78.2, 115.0, 'congested'),
(3, '2024-12-24', 8, 156, 125, 22, 9, 88.7, 118.0, 'normal'),
(6, '2024-12-24', 14, 456, 365, 67, 24, 65.2, 95.0, 'severe');

-- 插入事故记录数据
INSERT INTO `accident_record` (`record_id`, `monitor_id`, `accident_type`, `severity`, `location_x`, `location_y`, `description`, `confidence`, `status`, `detection_time`) VALUES
('ACC_20241224_001', 3, 'collision', 'high', 320.5, 240.8, '两车追尾事故，无人员伤亡', 0.89, 'resolved', '2024-12-24 09:15:23'),
('ACC_20241224_002', 6, 'sudden_stop', 'medium', 450.2, 180.6, '车辆紧急制动，后方车辆避让', 0.76, 'resolved', '2024-12-24 16:20:15');

-- =====================================================
-- 13. 创建索引优化
-- =====================================================
CREATE INDEX `idx_detection_task_status_time` ON `detection_task` (`status`, `create_time`);
CREATE INDEX `idx_tracking_target_monitor_status` ON `tracking_target` (`monitor_id`, `status`, `last_seen`);
CREATE INDEX `idx_accident_record_time_type` ON `accident_record` (`detection_time`, `accident_type`);
CREATE INDEX `idx_alarm_status_level_time` ON `alarm` (`status`, `severity`, `create_time`);

-- =====================================================
-- 14. 创建视图
-- =====================================================
CREATE VIEW `v_monitor_status` AS
SELECT
    m.id,
    m.name,
    m.location,
    m.highway_section,
    m.connection_status,
    m.enable_tracking,
    COUNT(DISTINCT dt.id) as active_tasks,
    COUNT(DISTINCT tt.id) as active_targets,
    COUNT(DISTINCT ar.id) as today_accidents,
    COUNT(DISTINCT a.id) as active_alarms
FROM monitor m
LEFT JOIN detection_task dt ON m.id = dt.monitor_id AND dt.status = 'running'
LEFT JOIN tracking_target tt ON m.id = tt.monitor_id AND tt.status = 'active'
LEFT JOIN accident_record ar ON m.id = ar.monitor_id AND DATE(ar.detection_time) = CURDATE()
LEFT JOIN alarm a ON m.id = a.monitor_id AND a.status IN ('pending', 'processing')
GROUP BY m.id;

SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 7. 警报表 (统一的警报管理)
-- =====================================================
CREATE TABLE `alarm` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '警报ID',
  `monitor_id` int NOT NULL COMMENT '监控点ID',
  `location` varchar(100) NOT NULL COMMENT '警报位置',
  `highway_section` varchar(100) DEFAULT NULL COMMENT '高速路段',
  `alarm_type` varchar(50) NOT NULL COMMENT '警报类型(vehicle_count/accident/violation/system)',
  `description` text NOT NULL COMMENT '警报描述',
  `vehicle_count` int DEFAULT NULL COMMENT '车辆数量',
  `detection_details` json COMMENT '检测详情(JSON格式)',
  `confidence_level` float DEFAULT NULL COMMENT '置信度',
  `threshold` int DEFAULT NULL COMMENT '阈值',
  `photo` varchar(500) DEFAULT NULL COMMENT '相关图片路径',
  `severity` varchar(20) DEFAULT 'medium' COMMENT '严重程度(low/medium/high/critical)',
  `status` varchar(20) DEFAULT 'pending' COMMENT '状态(pending/processing/resolved/dismissed)',
  `handled_by` varchar(50) DEFAULT NULL COMMENT '处理人',
  `handled_time` datetime DEFAULT NULL COMMENT '处理时间',
  `handle_note` text COMMENT '处理备注',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_monitor_id` (`monitor_id`),
  KEY `idx_alarm_type` (`alarm_type`),
  KEY `idx_severity` (`severity`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_alarm_monitor` FOREIGN KEY (`monitor_id`) REFERENCES `monitor` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='警报表';

-- =====================================================
-- 8. 交通统计表 (流量分析)
-- =====================================================
CREATE TABLE `traffic_statistics` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `monitor_id` int NOT NULL COMMENT '监控点ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `stat_hour` int DEFAULT NULL COMMENT '统计小时(0-23)',
  `vehicle_count` int DEFAULT 0 COMMENT '车辆总数',
  `car_count` int DEFAULT 0 COMMENT '小汽车数量',
  `truck_count` int DEFAULT 0 COMMENT '卡车数量',
  `bus_count` int DEFAULT 0 COMMENT '公交车数量',
  `avg_speed` float DEFAULT NULL COMMENT '平均速度(km/h)',
  `max_speed` float DEFAULT NULL COMMENT '最高速度(km/h)',
  `min_speed` float DEFAULT NULL COMMENT '最低速度(km/h)',
  `traffic_density` float DEFAULT NULL COMMENT '交通密度',
  `congestion_level` varchar(20) DEFAULT 'normal' COMMENT '拥堵程度(smooth/normal/congested/severe)',
  `weather_condition` varchar(50) DEFAULT NULL COMMENT '天气状况',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_monitor_date_hour` (`monitor_id`, `stat_date`, `stat_hour`),
  KEY `idx_stat_date` (`stat_date`),
  KEY `idx_congestion_level` (`congestion_level`),
  CONSTRAINT `fk_traffic_monitor` FOREIGN KEY (`monitor_id`) REFERENCES `monitor` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交通统计表';

-- =====================================================
-- 9. 系统配置表 (系统参数管理)
-- =====================================================
CREATE TABLE `system_config` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `config_type` varchar(20) DEFAULT 'string' COMMENT '配置类型(string/int/float/bool/json)',
  `description` varchar(500) DEFAULT NULL COMMENT '配置描述',
  `category` varchar(50) DEFAULT 'general' COMMENT '配置分类',
  `is_editable` tinyint(1) DEFAULT 1 COMMENT '是否可编辑',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`),
  KEY `idx_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- =====================================================
-- 10. 模型管理表 (YOLO模型管理)
-- =====================================================
CREATE TABLE `model_management` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '模型ID',
  `model_name` varchar(100) NOT NULL COMMENT '模型名称',
  `model_version` varchar(50) DEFAULT '1.0.0' COMMENT '模型版本',
  `model_type` varchar(50) NOT NULL COMMENT '模型类型(yolo/custom)',
  `model_path` varchar(500) NOT NULL COMMENT '模型文件路径',
  `model_size` bigint DEFAULT NULL COMMENT '模型文件大小(字节)',
  `input_size` varchar(20) DEFAULT '640x640' COMMENT '输入尺寸',
  `num_classes` int DEFAULT NULL COMMENT '类别数量',
  `class_names` json COMMENT '类别名称(JSON格式)',
  `performance_metrics` json COMMENT '性能指标(JSON格式)',
  `description` varchar(500) DEFAULT NULL COMMENT '模型描述',
  `is_default` tinyint(1) DEFAULT 0 COMMENT '是否默认模型',
  `status` varchar(20) DEFAULT 'active' COMMENT '状态(active/inactive)',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_model_name_version` (`model_name`, `model_version`),
  KEY `idx_model_type` (`model_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='模型管理表';

-- =====================================================
-- 11. 系统日志表 (操作日志)
-- =====================================================
CREATE TABLE `system_log` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `log_level` varchar(10) NOT NULL COMMENT '日志级别(DEBUG/INFO/WARN/ERROR)',
  `module` varchar(50) DEFAULT NULL COMMENT '模块名称',
  `action` varchar(100) DEFAULT NULL COMMENT '操作动作',
  `message` text COMMENT '日志消息',
  `user_id` int DEFAULT NULL COMMENT '用户ID',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `request_data` json COMMENT '请求数据',
  `response_data` json COMMENT '响应数据',
  `execution_time` float DEFAULT NULL COMMENT '执行时间(毫秒)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_log_level` (`log_level`),
  KEY `idx_module` (`module`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统日志表';

-- =====================================================
-- 12. 实时数据表 (WebSocket推送数据)
-- =====================================================
CREATE TABLE `realtime_data` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '数据ID',
  `monitor_id` int NOT NULL COMMENT '监控点ID',
  `data_type` varchar(50) NOT NULL COMMENT '数据类型(detection/tracking/status)',
  `data_content` json NOT NULL COMMENT '数据内容(JSON格式)',
  `frame_number` int DEFAULT NULL COMMENT '帧号',
  `timestamp` decimal(15,3) NOT NULL COMMENT '时间戳',
  `processing_time` float DEFAULT NULL COMMENT '处理时间(毫秒)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_monitor_id` (`monitor_id`),
  KEY `idx_data_type` (`data_type`),
  KEY `idx_timestamp` (`timestamp`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_realtime_monitor` FOREIGN KEY (`monitor_id`) REFERENCES `monitor` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='实时数据表';

-- =====================================================
-- 13. 插入测试数据
-- =====================================================

-- 插入用户数据 (admin和operator两个角色)
INSERT INTO `user` (`username`, `password`, `email`, `grade`, `status`, `create_by`, `remark`) VALUES
('admin', '21232f297a57a5a743894a0e4a801fc3', '<EMAIL>', 'admin', 1, 'system', '系统管理员账号'),
('operator', '4b583376b2767b923c3e1da60d10de59', '<EMAIL>', 'operator', 1, 'admin', '收费站班长账号');

-- 插入监控点数据 (杭州至千岛湖高速公路)
INSERT INTO `monitor` (`name`, `location`, `highway_section`, `camera_position`, `latitude`, `longitude`, `threshold`, `conf_threshold`, `iou_threshold`, `person`, `url`, `connection_status`, `is_alarm`, `mode`, `enable_tracking`, `tracker_type`, `create_by`, `remark`) VALUES
('杭州收费站监控点', '杭州收费站', 'K0+000', '收费站出入口', 30.2741, 120.1551, 15, 0.4, 0.5, 'admin', 'rtsp://admin:123456@192.168.1.101:554/stream1', 'online', '开启', 'tracking', 1, 'bytetrack', 'admin', '杭州收费站主要监控点'),
('杭州主线监控点', '杭州主线', 'K5+200', '主线车道', 30.2891, 120.1721, 20, 0.3, 0.5, 'admin', 'rtsp://admin:123456@192.168.1.102:554/stream1', 'online', '开启', 'tracking', 1, 'bytetrack', 'admin', '杭州主线交通监控'),
('富阳互通监控点', '富阳互通', 'K18+600', '互通匝道', 30.0498, 119.9528, 18, 0.4, 0.5, 'operator', 'rtsp://admin:123456@192.168.1.103:554/stream1', 'online', '开启', 'detection', 0, 'bytetrack', 'admin', '富阳互通交通监控'),
('桐庐服务区监控点', '桐庐服务区', 'K35+800', '服务区入口', 29.7971, 119.6811, 12, 0.3, 0.5, 'operator', 'rtsp://admin:123456@*************:554/stream1', 'online', '开启', 'tracking', 1, 'bytetrack', 'admin', '桐庐服务区监控'),
('千岛湖收费站监控点', '千岛湖收费站', 'K95+000', '收费站广场', 29.5678, 118.8765, 18, 0.4, 0.5, 'admin', 'rtsp://admin:123456@*************:554/stream1', 'online', '开启', 'tracking', 1, 'bytetrack', 'admin', '千岛湖收费站监控');

-- 插入系统配置数据
INSERT INTO `system_config` (`config_key`, `config_value`, `config_type`, `description`, `category`) VALUES
('system.name', '基于Yolov8与ByteTrack的高速公路智慧监控平台', 'string', '系统名称', 'general'),
('system.version', '2.0.0', 'string', '系统版本', 'general'),
('detection.default_conf_threshold', '0.4', 'float', '默认检测置信度阈值', 'detection'),
('detection.default_iou_threshold', '0.5', 'float', '默认IOU阈值', 'detection'),
('tracking.default_algorithm', 'bytetrack', 'string', '默认追踪算法', 'tracking'),
('accident.detection_enabled', 'true', 'bool', '是否启用事故检测', 'accident'),
('storage.retention_days', '30', 'int', '数据保留天数', 'storage');

-- 插入模型管理数据
INSERT INTO `model_management` (`model_name`, `model_type`, `model_path`, `model_size`, `input_size`, `class_names`, `description`, `is_default`, `create_by`) VALUES
('yolov8n.pt', 'yolo', './models/yolov8n.pt', 6237728, '640x640', '["person", "bicycle", "car", "motorcycle", "airplane", "bus", "train", "truck"]', 'YOLOv8 Nano模型，速度快，适合实时检测', 1, 'admin'),
('car.pt', 'yolo', './models/car.pt', 14567890, '640x640', '["car", "truck", "bus", "motorcycle"]', '专门针对车辆检测优化的模型', 0, 'admin');

-- 插入警报数据
INSERT INTO `alarm` (`monitor_id`, `location`, `highway_section`, `alarm_type`, `description`, `vehicle_count`, `detection_details`, `confidence_level`, `threshold`, `severity`, `status`, `create_time`, `remark`) VALUES
(1, '杭州收费站', 'K0+000', 'vehicle_count', '车流量超标：检测到17辆车，超过阈值15', 17, '{"total_vehicles":17,"cars":12,"trucks":3,"buses":2,"confidence_avg":0.85}', 0.85, 15, 'medium', 'resolved', '2024-12-24 08:15:30', '早高峰车流量超标'),
(3, '富阳互通', 'K18+600', 'vehicle_count', '车流量超标：检测到20辆车，超过阈值18', 20, '{"total_vehicles":20,"cars":15,"trucks":3,"buses":2,"confidence_avg":0.78}', 0.78, 18, 'medium', 'pending', '2024-12-24 09:12:15', '互通匝道车辆聚集'),
(5, '千岛湖收费站', 'K95+000', 'vehicle_count', '车流量超标：检测到21辆车，超过阈值18', 21, '{"total_vehicles":21,"cars":16,"trucks":3,"buses":2,"confidence_avg":0.86}', 0.86, 18, 'medium', 'pending', '2024-12-24 16:50:40', '千岛湖收费站排队');

-- 插入交通统计数据
INSERT INTO `traffic_statistics` (`monitor_id`, `stat_date`, `stat_hour`, `vehicle_count`, `car_count`, `truck_count`, `bus_count`, `avg_speed`, `max_speed`, `congestion_level`) VALUES
(1, '2024-12-24', 8, 245, 198, 35, 12, 85.5, 120.0, 'normal'),
(1, '2024-12-24', 9, 312, 251, 45, 16, 78.2, 115.0, 'congested'),
(3, '2024-12-24', 8, 156, 125, 22, 9, 88.7, 118.0, 'normal'),
(5, '2024-12-24', 14, 456, 365, 67, 24, 65.2, 95.0, 'severe');

-- 插入事故记录数据
INSERT INTO `accident_record` (`record_id`, `monitor_id`, `accident_type`, `severity`, `location_x`, `location_y`, `description`, `confidence`, `status`, `detection_time`) VALUES
('ACC_20241224_001', 3, 'collision', 'high', 320.5, 240.8, '两车追尾事故，无人员伤亡，车辆轻微损坏', 0.89, 'resolved', '2024-12-24 09:15:23'),
('ACC_20241224_002', 1, 'congestion', 'medium', 280.1, 320.4, '收费站出口拥堵，车辆缓慢通行', 0.76, 'active', '2024-12-24 18:30:45');

-- =====================================================
-- 14. 创建索引优化
-- =====================================================
CREATE INDEX `idx_detection_task_status_time` ON `detection_task` (`status`, `create_time`);
CREATE INDEX `idx_tracking_target_monitor_status` ON `tracking_target` (`monitor_id`, `status`, `last_seen`);
CREATE INDEX `idx_accident_record_time_type` ON `accident_record` (`detection_time`, `accident_type`);
CREATE INDEX `idx_alarm_status_level_time` ON `alarm` (`status`, `severity`, `create_time`);

-- =====================================================
-- 15. 创建视图
-- =====================================================
CREATE VIEW `v_monitor_status` AS
SELECT
    m.id,
    m.name,
    m.location,
    m.highway_section,
    m.connection_status,
    m.enable_tracking,
    m.tracker_type,
    COUNT(DISTINCT dt.id) as active_tasks,
    COUNT(DISTINCT tt.id) as active_targets,
    COUNT(DISTINCT ar.id) as today_accidents,
    COUNT(DISTINCT a.id) as active_alarms
FROM monitor m
LEFT JOIN detection_task dt ON m.id = dt.monitor_id AND dt.status = 'running'
LEFT JOIN tracking_target tt ON m.id = tt.monitor_id AND tt.status = 'active'
LEFT JOIN accident_record ar ON m.id = ar.monitor_id AND DATE(ar.detection_time) = CURDATE()
LEFT JOIN alarm a ON m.id = a.monitor_id AND a.status = 'pending'
GROUP BY m.id;

SET FOREIGN_KEY_CHECKS = 1;
