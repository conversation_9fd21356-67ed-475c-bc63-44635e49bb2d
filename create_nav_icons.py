#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建统一风格的导航图标
"""
from PIL import Image, ImageDraw
import os

# 确保图标目录存在
if not os.path.exists('ui/img'):
    os.makedirs('ui/img')

# 定义统一的图标尺寸和颜色
ICON_SIZE = (32, 32)
BACKGROUND_COLOR = (0, 0, 0, 0)  # 透明背景
WHITE = (255, 255, 255, 255)

# 定义各个图标的主题色
THEME_COLORS = {
    'file': (41, 128, 185),      # 蓝色 - 本地文件
    'cam': (46, 204, 113),       # 绿色 - 摄像头
    'rtsp': (52, 152, 219),      # 蓝色 - RTSP监控
    'chart': (60, 160, 230),     # 浅蓝色 - 流量图
    'track': (155, 89, 182),     # 紫色 - 单目标追踪
    'heatmap': (243, 156, 18),   # 橙色 - 热力图
    'warning': (231, 76, 60),    # 红色 - 违规行为检测
    'pedestrian': (41, 128, 255) # 天蓝色 - 行人闯入检测
}

def create_file_icon():
    """创建文件图标"""
    img = Image.new('RGBA', ICON_SIZE, BACKGROUND_COLOR)
    draw = ImageDraw.Draw(img)
    
    # 绘制文件夹形状
    color = THEME_COLORS['file']
    # 文件夹主体
    draw.rectangle([5, 10, 27, 26], fill=color, outline=WHITE, width=2)
    # 文件夹顶部
    draw.rectangle([8, 6, 20, 10], fill=color, outline=WHITE, width=2)
    
    img.save('ui/img/file.png')
    print("文件图标已创建")

def create_camera_icon():
    """创建摄像头图标"""
    img = Image.new('RGBA', ICON_SIZE, BACKGROUND_COLOR)
    draw = ImageDraw.Draw(img)
    
    color = THEME_COLORS['cam']
    # 摄像头主体
    draw.rectangle([5, 8, 27, 22], fill=color, outline=WHITE, width=2)
    # 摄像头镜头
    draw.ellipse([10, 10, 22, 20], fill=(0, 0, 0), outline=WHITE, width=2)
    # 摄像头顶部支架
    draw.line([16, 3, 16, 8], fill=WHITE, width=2)
    draw.line([10, 3, 22, 3], fill=WHITE, width=2)
    
    img.save('ui/img/cam.png')
    print("摄像头图标已创建")

def create_rtsp_icon():
    """创建RTSP图标"""
    img = Image.new('RGBA', ICON_SIZE, BACKGROUND_COLOR)
    draw = ImageDraw.Draw(img)
    
    color = THEME_COLORS['rtsp']
    # 网络摄像头主体
    draw.rectangle([5, 8, 27, 22], fill=color, outline=WHITE, width=2)
    # 镜头
    draw.ellipse([10, 10, 22, 20], fill=(0, 0, 0), outline=WHITE, width=2)
    # 信号波纹
    for i in range(3):
        r = 4 + i*3
        x1, y1 = 27, 15 - r
        x2, y2 = 27, 15 + r
        draw.arc([x1, y1, x1+2*r, y2], 270, 90, fill=WHITE, width=1)
    
    img.save('ui/img/RTSP.png')
    print("RTSP图标已创建")

def create_chart_icon():
    """创建流量图图标"""
    img = Image.new('RGBA', ICON_SIZE, BACKGROUND_COLOR)
    draw = ImageDraw.Draw(img)
    
    color = THEME_COLORS['chart']
    # 绘制坐标轴
    draw.line([5, 5, 5, 27], fill=WHITE, width=2)  # Y轴
    draw.line([5, 27, 27, 27], fill=WHITE, width=2)  # X轴
    
    # 绘制折线图
    points = [(8, 20), (12, 15), (16, 18), (20, 10), (24, 8)]
    for i in range(len(points)-1):
        draw.line([points[i], points[i+1]], fill=color, width=2)
    
    # 绘制点
    for point in points:
        draw.ellipse([point[0]-2, point[1]-2, point[0]+2, point[1]+2], fill=WHITE)
    
    img.save('ui/img/chart.png')
    print("流量图图标已创建")

def create_track_icon():
    """创建单目标追踪图标"""
    img = Image.new('RGBA', ICON_SIZE, BACKGROUND_COLOR)
    draw = ImageDraw.Draw(img)
    
    color = THEME_COLORS['track']
    # 绘制目标框
    draw.rectangle([10, 10, 22, 22], outline=WHITE, width=2)
    
    # 绘制十字准星
    draw.line([16, 6, 16, 26], fill=color, width=1)
    draw.line([6, 16, 26, 16], fill=color, width=1)
    
    # 绘制四个角
    draw.line([8, 8, 12, 8], fill=color, width=2)
    draw.line([8, 8, 8, 12], fill=color, width=2)
    
    draw.line([24, 8, 20, 8], fill=color, width=2)
    draw.line([24, 8, 24, 12], fill=color, width=2)
    
    draw.line([8, 24, 12, 24], fill=color, width=2)
    draw.line([8, 24, 8, 20], fill=color, width=2)
    
    draw.line([24, 24, 20, 24], fill=color, width=2)
    draw.line([24, 24, 24, 20], fill=color, width=2)
    
    img.save('ui/img/track.png')
    print("单目标追踪图标已创建")

def create_heatmap_icon():
    """创建热力图图标"""
    img = Image.new('RGBA', ICON_SIZE, BACKGROUND_COLOR)
    draw = ImageDraw.Draw(img)
    
    color = THEME_COLORS['heatmap']
    
    # 创建热力图效果
    gradient_colors = [
        (color[0], color[1], color[2], 50),  # 低密度
        (color[0], color[1], color[2], 100),  # 中密度
        (color[0], color[1], color[2], 200),  # 高密度
        (255, 255, 255, 150)  # 最高密度（白色）
    ]
    
    # 绘制热力图背景
    draw.rectangle([5, 5, 27, 27], outline=WHITE, width=1)
    
    # 绘制热力图区域
    center_x, center_y = 16, 16
    radius = 9
    
    # 从内到外绘制热力区域
    for i, c in enumerate(reversed(gradient_colors)):
        r = radius - i*2
        if r > 0:
            draw.ellipse([center_x-r, center_y-r, center_x+r, center_y+r], fill=c)
    
    img.save('ui/img/heatmap.png')
    print("热力图图标已创建")

def create_warning_icon():
    """创建违规检测图标"""
    img = Image.new('RGBA', ICON_SIZE, BACKGROUND_COLOR)
    draw = ImageDraw.Draw(img)
    
    color = THEME_COLORS['warning']
    
    # 绘制警告三角形
    points = [(16, 5), (5, 27), (27, 27)]
    draw.polygon(points, fill=color, outline=WHITE, width=2)
    
    # 绘制感叹号
    draw.rectangle([15, 12, 17, 20], fill=WHITE)
    draw.ellipse([15, 22, 17, 24], fill=WHITE)
    
    img.save('ui/img/warning.png')
    print("违规检测图标已创建")

def create_pedestrian_icon():
    """创建行人闯入检测图标"""
    img = Image.new('RGBA', ICON_SIZE, BACKGROUND_COLOR)
    draw = ImageDraw.Draw(img)
    
    color = THEME_COLORS['pedestrian']
    
    # 绘制人形图标
    # 头部
    draw.ellipse([13, 5, 19, 11], fill=WHITE)
    
    # 身体
    draw.line([16, 11, 16, 20], fill=WHITE, width=2)
    
    # 手臂
    draw.line([16, 14, 11, 18], fill=WHITE, width=2)  # 左臂
    draw.line([16, 14, 21, 18], fill=WHITE, width=2)  # 右臂
    
    # 腿部
    draw.line([16, 20, 13, 27], fill=WHITE, width=2)  # 左腿
    draw.line([16, 20, 19, 27], fill=WHITE, width=2)  # 右腿
    
    # 绘制禁止标志
    draw.ellipse([7, 7, 25, 25], outline=color, width=2)
    draw.line([7, 7, 25, 25], fill=color, width=2)
    
    img.save('ui/img/pedestrian.png')
    print("行人闯入检测图标已创建")

if __name__ == "__main__":
    print("开始创建统一风格的导航图标...")
    
    create_file_icon()
    create_camera_icon()
    create_rtsp_icon()
    create_chart_icon()
    create_track_icon()
    create_heatmap_icon()
    create_warning_icon()
    create_pedestrian_icon()
    
    print("所有图标创建完成！") 