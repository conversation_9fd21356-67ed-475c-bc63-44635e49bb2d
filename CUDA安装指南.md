# 基于Yolov8与ByteTrack的高速公路智慧监控平台 - CUDA GPU安装指南

## 🚀 快速安装流程

### 步骤1: 准备工作
```bash
# 1. 确保已激活虚拟环境
venv\Scripts\activate  # Windows
# source venv/bin/activate  # Linux/Mac

# 2. 确保wheel文件路径正确
# 检查文件是否存在：
# C:\Users\<USER>\Downloads\torch-2.1.2+cu118-cp311-cp311-win_amd64.whl
# C:\Users\<USER>\Downloads\torchvision-0.16.1+cu118-cp311-cp311-win_amd64.whl
```

### 步骤2: 自动安装CUDA依赖
```bash
# 运行CUDA依赖安装脚本
python install_cuda_dependencies.py
```

### 步骤3: 手动安装（如果自动安装失败）
```bash
# 1. 卸载可能冲突的包
pip uninstall torch torchvision torchaudio opencv-contrib-python -y

# 2. 安装本地CUDA PyTorch
pip install "C:\Users\<USER>\Downloads\torch-2.1.2+cu118-cp311-cp311-win_amd64.whl"
pip install "C:\Users\<USER>\Downloads\torchvision-0.16.1+cu118-cp311-cp311-win_amd64.whl"

# 3. 安装兼容的其他包
pip install -r requirements_cuda.txt
```

### 步骤4: 验证安装
```bash
# 测试CUDA安装
python -c "
import torch
print(f'PyTorch版本: {torch.__version__}')
print(f'CUDA可用: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'GPU设备: {torch.cuda.get_device_name(0)}')
"
```

### 步骤5: 启动系统
```bash
# 启动系统（会自动检测CUDA）
python start_server.py
```

## 🔧 依赖版本兼容性

### PyTorch 2.1.2 + CUDA 11.8 兼容版本
```
torch==2.1.2+cu118
torchvision==0.16.1+cu118
numpy==1.24.3
opencv-python==********
ultralytics>=8.0.0,<9.0.0
pillow>=9.0.0,<11.0.0
```

### 已知冲突包
❌ **避免同时安装**：
- `opencv-python` 和 `opencv-contrib-python`
- `torch` 和 `pytorch`
- `pillow` 和 `pil`

✅ **推荐组合**：
- `opencv-python==********`（不要安装contrib版本）
- `numpy==1.24.3`（PyTorch 2.1.2兼容）
- `pillow>=9.0.0,<11.0.0`

## 🎯 性能优化配置

### 环境变量设置
在 `config/end-back.env` 中添加：
```ini
# CUDA配置
USE_CUDA=true
CUDA_DEVICE=0
YOLO_DEVICE=cuda
YOLO_HALF_PRECISION=true
YOLO_BATCH_SIZE=1

# PyTorch优化
TORCH_CUDNN_BENCHMARK=true
TORCH_BACKENDS_CUDNN_DETERMINISTIC=false
```

### 代码中的优化
```python
# 在检测代码中使用
import os
os.environ['CUDA_VISIBLE_DEVICES'] = '0'  # 指定GPU

# YOLO模型优化
model = YOLO('car.pt')
model.to('cuda')
model.half()  # 使用半精度加速
```

## 🧪 测试CUDA功能

### 基础CUDA测试
```python
# test_cuda.py
import torch
import time
import numpy as np

print("=== CUDA基础测试 ===")
print(f"PyTorch版本: {torch.__version__}")
print(f"CUDA可用: {torch.cuda.is_available()}")

if torch.cuda.is_available():
    print(f"CUDA版本: {torch.version.cuda}")
    print(f"GPU数量: {torch.cuda.device_count()}")
    
    for i in range(torch.cuda.device_count()):
        print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
        props = torch.cuda.get_device_properties(i)
        print(f"  显存: {props.total_memory / 1024**3:.1f} GB")
    
    # 性能测试
    device = torch.device("cuda:0")
    size = 1000
    
    # CPU测试
    start = time.time()
    a_cpu = torch.randn(size, size)
    b_cpu = torch.randn(size, size)
    c_cpu = torch.mm(a_cpu, b_cpu)
    cpu_time = time.time() - start
    
    # GPU测试
    start = time.time()
    a_gpu = torch.randn(size, size).to(device)
    b_gpu = torch.randn(size, size).to(device)
    torch.cuda.synchronize()  # 等待GPU完成
    c_gpu = torch.mm(a_gpu, b_gpu)
    torch.cuda.synchronize()
    gpu_time = time.time() - start
    
    print(f"\n性能对比 ({size}x{size} 矩阵乘法):")
    print(f"CPU时间: {cpu_time:.4f}s")
    print(f"GPU时间: {gpu_time:.4f}s")
    print(f"加速比: {cpu_time/gpu_time:.2f}x")
```

### YOLO CUDA测试
```python
# test_yolo_cuda.py
from utils.yolo_cuda import CudaYOLODetector
import cv2
import time

# 创建检测器
detector = CudaYOLODetector(device="cuda")

# 获取设备信息
device_info = detector.get_device_info()
print("设备信息:", device_info)

# 性能基准测试
benchmark_result = detector.benchmark(iterations=20)
print("基准测试结果:", benchmark_result)

# 实际图像测试
if True:  # 如果有测试图像
    # 创建测试图像
    test_image = cv2.imread("test_image.jpg")
    if test_image is None:
        # 创建随机测试图像
        test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
    
    # 检测测试
    start_time = time.time()
    result_image, detection_result = detector.detect_and_draw(test_image)
    end_time = time.time()
    
    print(f"检测时间: {end_time - start_time:.4f}s")
    print(f"检测到 {len(detection_result.get('detections', []))} 个目标")
    
    # 保存结果
    cv2.imwrite("cuda_test_result.jpg", result_image)
    print("结果已保存到: cuda_test_result.jpg")
```

## 🚨 常见问题解决

### 问题1: CUDA不可用
```bash
# 检查NVIDIA驱动
nvidia-smi

# 检查CUDA Toolkit
nvcc --version

# 重新安装CUDA Toolkit 11.8
# 下载地址: https://developer.nvidia.com/cuda-11-8-0-download-archive
```

### 问题2: 显存不足
```python
# 在代码中添加显存管理
import torch
torch.cuda.empty_cache()  # 清理显存

# 使用半精度模型
model.half()

# 减小批处理大小
YOLO_BATCH_SIZE=1
```

### 问题3: 版本冲突
```bash
# 完全重新安装
pip uninstall torch torchvision torchaudio ultralytics opencv-python -y
pip cache purge
python install_cuda_dependencies.py
```

### 问题4: 性能不佳
```python
# 启用优化选项
import torch
torch.backends.cudnn.benchmark = True
torch.backends.cudnn.deterministic = False

# 预热模型
for _ in range(3):
    model(dummy_input)
```

## 📊 性能对比

### 预期性能提升
| 操作 | CPU (Intel i7) | GPU (RTX 3060) | 加速比 |
|------|----------------|----------------|--------|
| YOLOv8n推理 | ~100ms | ~15ms | 6.7x |
| YOLOv8s推理 | ~200ms | ~25ms | 8.0x |
| 图像预处理 | ~10ms | ~2ms | 5.0x |
| 后处理 | ~5ms | ~1ms | 5.0x |

### 显存使用
| 模型 | 显存占用 | 推荐显存 |
|------|----------|----------|
| YOLOv8n | ~1GB | 4GB+ |
| YOLOv8s | ~2GB | 6GB+ |
| YOLOv8m | ~4GB | 8GB+ |
| YOLOv8l | ~6GB | 12GB+ |

## 🎉 验证成功标志

当您看到以下输出时，说明CUDA安装成功：

```
================================================================================
基于Yolov8与ByteTrack的高速公路智慧监控平台
================================================================================
正在检查系统环境...
✓ 依赖包检查通过
正在检查CUDA支持...
✓ CUDA可用 - 设备: NVIDIA GeForce RTX 3060 (共1个GPU)
正在检查数据库连接...
✓ 数据库连接成功，共有 8 个表
正在创建Flask应用...
--------------------------------------------------------------------------------
服务地址: http://127.0.0.1:5500
调试模式: False
计算设备: CUDA
数据库状态: 正常
--------------------------------------------------------------------------------
系统启动中...
```

现在您的系统已经配置为使用CUDA GPU加速！🚀
